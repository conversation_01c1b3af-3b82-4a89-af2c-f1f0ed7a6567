#!/bin/bash

echo "🚀 启动所有服务..."

# 进入项目目录
cd /home/<USER>/Workspace/langbot

echo "📱 启动 WeChatPadPro 服务..."
docker-compose -f wechatpad-docker-compose.yml up -d

echo "⏳ 等待 WeChatPadPro 启动完成..."
sleep 15

echo "🤖 启动 LangBot 服务..."
docker stop langbot 2>/dev/null || true
docker rm langbot 2>/dev/null || true

docker run -d \
  --name langbot \
  --network host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/pkg:/app/pkg \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/plugins:/app/plugins \
  -v $(pwd)/workflows:/app/workflows \
  -v $(pwd)/templates:/app/templates \
  -v $(pwd)/res:/app/res \
  --restart on-failure \
  -e TZ=Asia/Shanghai \
  docker.langbot.app/langbot-public/rockchin/langbot:latest

echo "⏳ 等待 LangBot 启动完成..."
sleep 10

echo "✅ 所有服务启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   - WeChatPadPro: http://localhost:1239"
echo "   - LangBot WebUI: http://localhost:5300"
echo ""
echo "📱 请访问 http://localhost:1239 扫码登录微信"

echo ""
echo "🔍 检查服务状态："
docker ps | grep -E "(langbot|wechatpadpro)" 