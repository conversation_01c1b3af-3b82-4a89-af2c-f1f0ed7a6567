"""
Kontext 纵横比优化器

负责根据用户意图和输入图片分析最佳纵横比。
"""

import io
import logging
from typing import List, Any
from PIL import Image

class AspectRatioOptimizer:
    """
    Kontext 纵横比优化器
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 支持的比例
        self.supported_ratios = ["1:1", "3:2", "2:3", "16:9", "9:16"]
        
    def determine_optimal_ratio(self, images: List[Any], user_intent: str = "") -> str:
        """确定最适合的图像比例
        
        优先级：
        1. 用户明确指定的比例关键词
        2. 第一张图片的实际比例（当没有明确指定时）
        3. 默认比例（3:2）
        """
        
        # 检查用户明确指定的比例
        user_intent_lower = user_intent.lower()
        
        # 先检查最具体的关键词，避免被通用关键词覆盖
        if any(keyword in user_intent_lower for keyword in ["宽屏", "超宽", "widescreen", "wide screen", "16:9", "9:16"]):
            if "竖" in user_intent_lower or "9:16" in user_intent_lower:
                self.logger.info("用户明确指定竖版宽屏比例：9:16")
                return "9:16"
            else:
                self.logger.info("用户明确指定横版宽屏比例：16:9")
                return "16:9"
        elif any(keyword in user_intent_lower for keyword in ["正方", "方形", "square", "1:1", "正方形"]):
            self.logger.info("用户明确指定正方形比例：1:1")
            return "1:1"
        elif any(keyword in user_intent_lower for keyword in ["横版", "横向", "landscape", "wide", "横屏", "3:2"]):
            self.logger.info("用户明确指定横版比例：3:2")
            return "3:2"
        elif any(keyword in user_intent_lower for keyword in ["竖版", "竖向", "portrait", "tall", "竖屏", "2:3"]):
            self.logger.info("用户明确指定竖版比例：2:3")
            return "2:3"
        
        # 检查是否有"保持比例"或"不改变比例"的指示
        keep_ratio_keywords = ["保持比例", "不改变比例", "维持比例", "same ratio", "keep ratio", "maintain ratio", "original ratio"]
        if any(keyword in user_intent_lower for keyword in keep_ratio_keywords):
            self.logger.info("用户要求保持原图比例，将分析第一张图片")
            # 强制分析图片比例，如果没有图片则使用默认
            pass
        elif user_intent.strip():
            # 如果用户有明确的指令但没有指定比例，检查是否需要参考图片比例
            transform_keywords = ["改变", "修改", "变成", "转换", "change", "transform", "convert", "modify"]
            if not any(keyword in user_intent_lower for keyword in transform_keywords):
                self.logger.info("用户没有明确的比例指示，将分析第一张图片比例作为参考")
            else:
                self.logger.info("用户有明确的变换指令但未指定比例，使用默认比例")
                return "3:2"
        
        # 如果有主图且用户没有明确指定其他比例，分析其比例
        if images and len(images) > 0:
            try:
                # 获取第一张图片
                first_image = images[0]
                image = Image.open(io.BytesIO(first_image))
                width, height = image.size
                
                # 计算实际比例
                actual_ratio = width / height
                self.logger.info(f"第一张图片尺寸: {width}x{height}, 比例: {actual_ratio:.2f}")
                
                # 找到最接近的Kontext支持比例
                ratio_mapping = {
                    "1:1": 1.0,
                    "3:2": 1.5,
                    "2:3": 0.67,
                    "16:9": 1.78,
                    "9:16": 0.56
                }
                
                closest_ratio = "3:2"  # 默认值
                min_diff = float('inf')
                
                for ratio_name, ratio_value in ratio_mapping.items():
                    diff = abs(actual_ratio - ratio_value)
                    if diff < min_diff:
                        min_diff = diff
                        closest_ratio = ratio_name
                
                self.logger.info(f"基于图片比例分析，选择最佳Kontext比例: {closest_ratio}")
                return closest_ratio
                
            except Exception as e:
                self.logger.warning(f"分析图片比例失败: {e}")
        
        # 默认返回3:2比例
        self.logger.info("使用默认比例：3:2")
        return "3:2" 