"""
统一路由系统核心模块
实现PRD-********中定义的两级路由架构：
- 第一级：触发词识别（必须，确定生成管道）
- 第二级：管道内工作流选择（智能分析具体工作流）

同时提供统一的LLM分析服务，避免重复代码
"""

import time
import json
import logging
import re
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from ..session.models import WorkflowType
from ...provider import entities as llm_entities


class RoutingLevel(Enum):
    """路由级别"""
    LEVEL_1 = 1  # 第一级：触发词识别
    LEVEL_2 = 2  # 第二级：管道内工作流选择


class RoutingConfidence(Enum):
    """路由置信度"""
    HIGH = "high"
    MEDIUM = "medium" 
    LOW = "low"
    UNKNOWN = "unknown"


class WorkflowSubType(Enum):
    """工作流子类型"""
    # AIGEN管道
    AIGEN_TEXT_ONLY = "aigen_text_only"           # 纯文生图
    AIGEN_CONTROL_ONLY = "aigen_control_only"     # 控制图
    AIGEN_REFERENCE_ONLY = "aigen_reference_only" # 参考图
    AIGEN_CONTROL_REFERENCE = "aigen_control_reference" # 控制+参考
    
    # KONTEXT管道
    KONTEXT_SINGLE = "kontext_single"             # 单图处理
    KONTEXT_MULTIPLE = "kontext_multiple"         # 多图处理
    
    # KONTEXT_API管道
    KONTEXT_API_SINGLE = "kontext_api_single"     # 单图API
    KONTEXT_API_MULTIPLE = "kontext_api_multiple" # 多图API


@dataclass
class ParameterAnalysisResult:
    """参数分析结果"""
    success: bool
    parameters: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    llm_used: bool = False
    fallback_used: bool = False
    error_message: str = ""
    analysis_time: float = 0.0


@dataclass
class IntentAnalysisResult:
    """意图分析结果"""
    success: bool
    image_types: Dict[int, str] = field(default_factory=dict)  # 图片索引 -> 类型
    confidence: float = 0.0
    reasoning: str = ""
    llm_used: bool = False
    fallback_used: bool = False
    error_message: str = ""
    analysis_time: float = 0.0


@dataclass
class _Level2RoutingResult:
    """第二级路由结果"""
    workflow_subtype: WorkflowSubType
    confidence: RoutingConfidence
    reasoning: str
    processing_time_ms: float
    fallback_used: bool = False
    suggested_prompt: str = ""
    needs_clarification: bool = False
    clarification_question: str = ""
    workflow_file: str = ""


@dataclass
class UnifiedRoutingResult:
    """统一路由结果"""
    routing_level: RoutingLevel
    workflow_type: WorkflowType
    workflow_subtype: WorkflowSubType
    confidence: RoutingConfidence
    reasoning: str
    processing_time_ms: float
    fallback_used: bool = False
    suggested_prompt: str = ""
    needs_clarification: bool = False
    clarification_question: str = ""
    workflow_file: str = ""  # 具体的工作流文件名


class UnifiedRoutingSystem:
    """统一路由系统 - 提供路由决策和LLM分析服务"""
    
    def __init__(self, ap=None):
        self.ap = ap
        self.logger = logging.getLogger(__name__)
        
        # 第一级路由配置（触发词识别）
        self.level_1_keywords = {
            "aigen": WorkflowType.AIGEN,
            "kontext": WorkflowType.KONTEXT,
            "kontext_api": WorkflowType.KONTEXT_API
        }
        
        # 第二级路由配置（管道内工作流选择）
        self.level_2_config = {
            "enabled": True,
            "timeout_ms": 2000,
            "model_name": "default",
            "system_prompt": self._get_routing_system_prompt()
        }
        
        # 工作流文件映射
        self.workflow_files = {
            WorkflowSubType.AIGEN_TEXT_ONLY: "flux_default.json",
            WorkflowSubType.AIGEN_CONTROL_ONLY: "flux_controlnet.json",
            WorkflowSubType.AIGEN_REFERENCE_ONLY: "flux_redux.json",
            WorkflowSubType.AIGEN_CONTROL_REFERENCE: "flux_controlnet_redux.json",
            WorkflowSubType.KONTEXT_SINGLE: "kontext_local_1image.json",
            WorkflowSubType.KONTEXT_MULTIPLE: "kontext_local_2images.json",
            WorkflowSubType.KONTEXT_API_SINGLE: "kontext_api_1image.json",
            WorkflowSubType.KONTEXT_API_MULTIPLE: "kontext_api_2images.json"
        }
        
        # 监控配置
        self.monitoring_config = {
            "enabled": True,
            "log_level": "INFO",
            "metrics_collection": True,
            "performance_tracking": True
        }
        
        # 控制图关键词
        self.control_keywords = [
            "控制", "轮廓", "姿势", "结构", "保持形状", "参考布局",
            "按照这个形状", "保持这个姿势", "控制结构", "参考轮廓",
            "按照这张图的结构", "按照这个结构", "按照这个形状", "按照这个轮廓",
            "保持这个结构", "保持这个形状", "保持这个轮廓", "保持这个姿势",
            "参考这个结构", "参考这个形状", "参考这个轮廓", "参考这个姿势",
            "control", "outline", "pose", "structure", "shape", "layout"
        ]
        
        # 参考图关键词  
        self.reference_keywords = [
            "参考", "风格", "类似", "像这样", "参考这个",
            "参考风格", "类似这样", "参考效果", "参考颜色",
            "reference", "style", "similar", "like this", "color"
        ]

    def _get_routing_system_prompt(self) -> str:
        """获取路由系统提示词"""
        return """
你是一个智能工作流路由器，负责理解用户意图并选择最合适的AI工作流。

可用的工作流类型：
1. aigen - 文生图工作流（本地Flux）
   - 适用于：从文本描述生成图片
   - 可选输入：文本提示词 + 可选参考图片（控制图/参考图）
   - 特点：支持ControlNet、LoRA、高质量生成

2. kontext - 图生图工作流（本地Kontext）  
   - 适用于：基于现有图片进行编辑、修改、风格转换
   - 必需输入：至少1张图片 + 编辑指令
   - 特点：保持原图结构，精准编辑

3. kontext_api - 远程API工作流
   - 适用于：复杂的图像处理，当本地资源不足时
   - 必需输入：图片 + 处理指令
   - 特点：云端处理，功能丰富

路由规则：
- 如果用户明确提到"aigen"、"文生图"、"生成图片"等 → aigen
- 如果用户明确提到"kontext"、"编辑"、"修改图片"等 → kontext  
- 如果用户请求复杂处理或提到"api"、"云端"等 → kontext_api
- 根据用户描述的任务类型智能选择最合适的工作流
- 考虑输入条件（是否有图片、图片数量）

请以JSON格式返回结果：
{
  "workflow_type": "aigen|kontext|kontext_api",
  "confidence": "high|medium|low",
  "reasoning": "选择原因",
  "suggested_prompt": "建议的提示词"
}
"""

    def _get_parameter_analysis_prompt(self) -> str:
        """获取参数分析系统提示词"""
        return """You are an expert AI image generation parameter analyzer. Your task is to analyze user requests and provide optimal parameters for Flux/ComfyUI image generation.

Analyze the user request and return a JSON object with the following parameters:

1. **prompt**: Enhanced English prompt (natural language, not keywords)
2. **width**: Image width (choose from: 1024 for square; 1280, 1440 for landscape; 800, 720 for portrait)
3. **height**: Image height (choose from: 1024 for square; 800, 720 for landscape; 1280, 1440 for portrait)
4. **steps**: Sampling steps (15-30, default 20)
5. **guidance**: CFG guidance (2.0-5.0, default 3.5)
6. **aspect_ratio**: Detected aspect ratio ("landscape", "portrait", "square")
7. **seed_instruction**: Seed handling instruction ("random", "use_last", "specific:<number>")

**Aspect Ratio Detection Rules:**
- 横版/宽屏/横向/landscape → width > height (e.g., 1280x800, 1440x720)
- 竖版/竖向/portrait → height > width (e.g., 800x1280, 720x1440)  
- 正方形/方形/square → width = height (1024x1024)
- No specific mention → default square (1024x1024)

**Resolution Strategy (Target ~1M pixels):**
- Square: 1024x1024 (1,048,576 pixels)
- Landscape: 1280x800 (1,024,000 pixels) or 1440x720 (1,036,800 pixels)
- Portrait: 800x1280 (1,024,000 pixels) or 720x1440 (1,036,800 pixels)

**Seed Handling Rules:**
- Default behavior → "random" (generate new random seed each time)
- 使用上一次种子/用上次的/same seed/previous seed → "use_last"
- 种子123/seed 456/固定种子 → "specific:<number>"
- No mention of seed → "random"

**Quality Keywords → Steps/Guidance:**
- 高质量/精细/详细 → steps: 25-30, guidance: 4.0
- 快速/简单 → steps: 15-20, guidance: 3.0
- Default → steps: 20, guidance: 3.5

Now analyze this user request and return ONLY a valid JSON object:"""

    def _get_intent_analysis_prompt(self) -> str:
        """获取意图分析系统提示词"""
        return """你是一个图像生成AI助手的意图分析专家。你的任务是分析用户的文本，判断他们上传的图片应该如何使用。

图片类型说明：
- control: 控制图，用于控制生成图像的结构、姿势、线条、边缘等，如ControlNet
- reference: 参考图，用于参考风格、色调、氛围、感觉等，如IP-Adapter
- mixed: 混合图，既可以作为控制图也可以作为参考图
- unknown: 用户意图不明确

分析规则：
1. 寻找明确的图片用途指示词汇
2. 理解用户想要实现的效果
3. 考虑上下文和语义
4. 优先选择明确的意图，避免过度推测

请以JSON格式返回结果：
{
    "image_types": {图片索引: "类型"},
    "confidence": 置信度(0-1),
    "reasoning": "分析推理过程"
}

示例：
用户: "以这张图片为控制图，生成一只黑猫"
图片数量: 1
返回: {"image_types": {0: "control"}, "confidence": 0.95, "reasoning": "用户明确说'以这张图片为控制图'，意图非常明确"}

用户: "参考这张图的风格，画一个机器人"
图片数量: 1  
返回: {"image_types": {0: "reference"}, "confidence": 0.9, "reasoning": "用户说'参考风格'，表明是要参考图像的风格特征"}"""

    async def _get_llm_model(self, query) -> Optional[Any]:
        """获取LLM模型"""
        if not self.ap or not query.pipeline_config:
            return None
        
        # 获取LLM模型
        model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
        if not model_uuid:
            return None
        
        # 找到对应的RuntimeLLMModel
        runtime_llm_model = None
        for model in self.ap.model_mgr.llm_models:
            if model.model_entity.uuid == model_uuid:
                runtime_llm_model = model
                break
        
        return runtime_llm_model

    async def _call_llm(self, query, system_prompt: str, user_prompt: str) -> Optional[str]:
        """统一的LLM调用接口"""
        try:
            runtime_llm_model = await self._get_llm_model(query)
            if not runtime_llm_model:
                return None
            
            # 创建消息
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]
            
            # 调用LLM
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )
            
            # 提取响应文本
            response_text = ""
            if hasattr(result, 'content') and result.content:
                if isinstance(result.content, list):
                    for element in result.content:
                        if hasattr(element, 'text') and element.text:
                            response_text += element.text
                elif isinstance(result.content, str):
                    response_text = result.content
                else:
                    response_text = str(result.content)
            
            return response_text.strip()
            
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _clean_json_response(self, response_text: str) -> str:
        """清理LLM返回的JSON响应"""
        if not response_text:
            return ""
        
        # 清理markdown代码块标记
        cleaned_text = response_text
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.startswith('```'):
            cleaned_text = cleaned_text[3:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        
        return cleaned_text.strip()

    async def analyze_parameters(self, user_text: str, query) -> ParameterAnalysisResult:
        """统一的参数分析服务"""
        start_time = time.time()
        result = ParameterAnalysisResult(success=False)
        
        try:
            # 尝试使用LLM分析
            system_prompt = self._get_parameter_analysis_prompt()
            user_prompt = f"User request: {user_text}"
            
            response_text = await self._call_llm(query, system_prompt, user_prompt)
            
            if response_text:
                result.llm_used = True
                self.logger.info(f"LLM参数分析响应: {response_text}")
                
                try:
                    # 清理和解析JSON
                    cleaned_text = self._clean_json_response(response_text)
                    params_json = json.loads(cleaned_text)
                    
                    # 验证和补充参数
                    validated_params = self._validate_and_complete_params(params_json, user_text)
                    
                    result.success = True
                    result.parameters = validated_params
                    result.confidence = 0.9
                    
                    self.logger.info(f"参数分析成功: {validated_params}")
                    
                except json.JSONDecodeError as e:
                    self.logger.warning(f"LLM返回的JSON格式错误: {e}")
                    # 尝试提取JSON部分（备用方案）
                    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                    if json_match:
                        try:
                            params_json = json.loads(json_match.group())
                            validated_params = self._validate_and_complete_params(params_json, user_text)
                            result.success = True
                            result.parameters = validated_params
                            result.confidence = 0.7
                            self.logger.info(f"提取JSON成功: {validated_params}")
                        except:
                            pass
            
            # 如果LLM分析失败，使用默认参数
            if not result.success:
                result.fallback_used = True
                result.parameters = self._get_default_params(user_text)
                result.confidence = 0.5
                result.error_message = "LLM分析失败，使用默认参数"
                
        except Exception as e:
            self.logger.error(f"参数分析失败: {e}")
            result.fallback_used = True
            result.parameters = self._get_default_params(user_text)
            result.confidence = 0.5
            result.error_message = f"分析异常: {str(e)}"
        
        finally:
            result.analysis_time = time.time() - start_time
        
        return result

    async def analyze_intent(self, user_text: str, image_count: int, query) -> IntentAnalysisResult:
        """统一的意图分析服务"""
        start_time = time.time()
        result = IntentAnalysisResult(success=False)
        
        try:
            if image_count == 0:
                result.success = True
                result.image_types = {}
                result.confidence = 1.0
                result.reasoning = "没有图片"
                return result
            
            # 尝试使用LLM分析
            system_prompt = self._get_intent_analysis_prompt()
            user_prompt = f"""用户文本: "{user_text}"
图片数量: {image_count}

请分析用户的意图，判断每张图片应该如何使用。"""
            
            response_text = await self._call_llm(query, system_prompt, user_prompt)
            
            if response_text:
                result.llm_used = True
                self.logger.info(f"LLM意图分析响应: {response_text}")
                
                try:
                    # 清理和解析JSON
                    cleaned_text = self._clean_json_response(response_text)
                    data = json.loads(cleaned_text)
                    
                    # 验证结果
                    if ('image_types' in data and 
                        'confidence' in data and 
                        'reasoning' in data):
                        
                        # 转换并验证图片类型
                        image_types = {}
                        for idx_str, img_type in data['image_types'].items():
                            idx = int(idx_str)
                            if 0 <= idx < image_count and img_type in ['control', 'reference', 'mixed', 'unknown']:
                                image_types[idx] = img_type
                        
                        result.success = True
                        result.image_types = image_types
                        result.confidence = float(data['confidence'])
                        result.reasoning = str(data['reasoning'])
                        
                        self.logger.info(f"意图分析成功: {result.reasoning}")
                        
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    self.logger.warning(f"解析LLM响应失败: {e}")
            
            # 如果LLM分析失败，使用传统关键词分析
            if not result.success:
                result.fallback_used = True
                fallback_result = self._analyze_intent_with_keywords(user_text, image_count)
                result.success = True
                result.image_types = fallback_result.image_types
                result.confidence = fallback_result.confidence
                result.reasoning = fallback_result.reasoning
                result.error_message = "LLM分析失败，使用关键词分析"
                
        except Exception as e:
            self.logger.error(f"意图分析失败: {e}")
            result.fallback_used = True
            fallback_result = self._analyze_intent_with_keywords(user_text, image_count)
            result.success = True
            result.image_types = fallback_result.image_types
            result.confidence = fallback_result.confidence
            result.reasoning = fallback_result.reasoning
            result.error_message = f"分析异常: {str(e)}"
        
        finally:
            result.analysis_time = time.time() - start_time
        
        return result

    def _validate_and_complete_params(self, params_json: Dict[str, Any], user_text: str) -> Dict[str, Any]:
        """验证和补充参数"""
        # 基本参数验证和补充
        params = {
            'prompt': params_json.get('prompt', user_text),
            'width': params_json.get('width', 1024),
            'height': params_json.get('height', 1024),
            'steps': params_json.get('steps', 20),
            'guidance': params_json.get('guidance', 3.5),
            'aspect_ratio': params_json.get('aspect_ratio', 'square'),
            'seed_instruction': params_json.get('seed_instruction', 'random')
        }
        
        # 验证数值范围
        params['width'] = max(512, min(2048, params['width']))
        params['height'] = max(512, min(2048, params['height']))
        params['steps'] = max(10, min(50, params['steps']))
        params['guidance'] = max(1.0, min(10.0, params['guidance']))
        
        return params

    def _get_default_params(self, user_text: str) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'prompt': user_text,
            'width': 1024,
            'height': 1024,
            'steps': 20,
            'guidance': 3.5,
            'aspect_ratio': 'square',
            'seed_instruction': 'random'
        }

    def _analyze_intent_with_keywords(self, user_text: str, image_count: int) -> IntentAnalysisResult:
        """传统关键词分析作为后备方案"""
        user_text_lower = user_text.lower()
        
        # 检查控制图关键词
        is_control = any(keyword in user_text_lower for keyword in self.control_keywords)
        
        # 检查参考图关键词
        is_reference = any(keyword in user_text_lower for keyword in self.reference_keywords)
        
        image_types = {}
        reasoning = "使用传统关键词匹配: "
        
        if image_count == 1:
            if is_control:
                image_types[0] = "control"
                reasoning += "检测到控制相关关键词"
            elif is_reference:
                image_types[0] = "reference"
                reasoning += "检测到参考相关关键词"
            else:
                image_types[0] = "reference"  # 默认为参考图
                reasoning += "无明确指示，默认为参考图"
        else:
            # 多图情况简化处理
            for i in range(image_count):
                image_types[i] = "unknown"
            reasoning += "多图情况需要用户明确指定"
        
        return IntentAnalysisResult(
            success=True,
            image_types=image_types,
            confidence=0.6,  # 传统方法置信度较低
            reasoning=reasoning
        )

    async def route_unified(
        self,
        user_text: str,
        has_images: bool = False,
        image_count: int = 0,
        query: Optional[Any] = None
    ) -> Optional[UnifiedRoutingResult]:
        """
        统一路由入口
        
        执行逻辑:
        1. 第一级：检查触发词，确定生成管道
        2. 第二级：在管道内选择具体工作流
        3. 如果没有触发词，返回None（普通聊天）
        """
        start_time = time.time()
        
        try:
            # 第一级路由：触发词识别（必须）
            level_1_result = self._route_level_1(user_text)
            if not level_1_result:
                # 没有触发词，是普通聊天
                self.logger.info("未检测到触发词，视为普通聊天")
                return None
            
            # 第二级路由：管道内工作流选择
            level_2_result = await self._route_level_2(
                level_1_result, user_text, has_images, image_count, query
            )
            processing_time = (time.time() - start_time) * 1000
            
            return UnifiedRoutingResult(
                routing_level=RoutingLevel.LEVEL_2,
                workflow_type=level_1_result,
                workflow_subtype=level_2_result.workflow_subtype,
                confidence=level_2_result.confidence,
                reasoning=level_2_result.reasoning,
                processing_time_ms=processing_time,
                fallback_used=level_2_result.fallback_used,
                suggested_prompt=level_2_result.suggested_prompt,
                needs_clarification=level_2_result.needs_clarification,
                clarification_question=level_2_result.clarification_question,
                workflow_file=level_2_result.workflow_file
            )
            
        except Exception as e:
            self.logger.error(f"统一路由失败: {e}")
            return None

    def _route_level_1(self, user_text: str) -> Optional[WorkflowType]:
        """
        第一级触发词路由
        
        Args:
            user_text: 用户输入文本
        
        Returns:
            WorkflowType: 匹配的管道类型，None表示无触发词（普通聊天）
        
        性能要求: <5ms
        可靠性要求: 99.99%
        """
        if not user_text:
            return None
            
        user_text_lower = user_text.lower().strip()
        
        # 检查精确触发词匹配
        for keyword, workflow_type in self.level_1_keywords.items():
            if user_text_lower.startswith(keyword.lower() + " "):
                self.logger.info(f"第一级路由匹配: {keyword} -> {workflow_type.value}")
                return workflow_type
        
        return None

    async def _route_level_2(
        self,
        pipeline_type: WorkflowType,
        user_text: str,
        has_images: bool = False,
        image_count: int = 0,
        query: Optional[Any] = None
    ) -> '_Level2RoutingResult':
        """
        第二级管道内工作流选择
        
        Args:
            pipeline_type: 已确定的管道类型
            user_text: 用户输入文本
            has_images: 是否有图片
            image_count: 图片数量
            query: 查询对象（用于LLM调用）
        
        Returns:
            _Level2RoutingResult: 详细的路由结果
        """
        start_time = time.time()
        
        # 根据管道类型选择不同的处理逻辑
        if pipeline_type == WorkflowType.AIGEN:
            result = await self._route_aigen_pipeline(user_text, has_images, image_count, query)
        elif pipeline_type == WorkflowType.KONTEXT:
            result = self._route_kontext_pipeline(has_images, image_count)
        elif pipeline_type == WorkflowType.KONTEXT_API:
            result = self._route_kontext_api_pipeline(has_images, image_count)
        else:
            # 未知管道类型
            result = _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
                confidence=RoutingConfidence.UNKNOWN,
                reasoning="未知管道类型，使用默认工作流",
                fallback_used=True,
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_default.json"
            )
        
        return result

    async def _route_aigen_pipeline(
        self, 
        user_text: str, 
        has_images: bool, 
        image_count: int,
        query: Optional[Any] = None
    ) -> '_Level2RoutingResult':
        """AIGEN管道内工作流选择"""
        start_time = time.time()
        
        # 无图片 → 纯文生图
        if not has_images or image_count == 0:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
                confidence=RoutingConfidence.HIGH,
                reasoning="无图片输入，选择纯文生图工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_default.json"
            )
        
        # 1张图片 → 判断用途
        if image_count == 1:
            return await self._analyze_single_image_usage(user_text, query, start_time)
        
        # 2张图片 → 必须明确指定
        if image_count == 2:
            return await self._analyze_double_image_usage(user_text, query, start_time)
        
        # 多张图片 → 需要询问
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.AIGEN_CONTROL_REFERENCE,
            confidence=RoutingConfidence.LOW,
            reasoning="多张图片，需要用户确认用途",
            processing_time_ms=(time.time() - start_time) * 1000,
            needs_clarification=True,
            clarification_question="您上传的多张图片是用于控制画面结构/姿势，还是作为风格/内容参考？",
            workflow_file="flux_controlnet.json"
        )

    async def _analyze_single_image_usage(
        self, 
        user_text: str, 
        query: Optional[Any],
        start_time: float
    ) -> '_Level2RoutingResult':
        """分析单张图片用途"""
        user_text_lower = user_text.lower()
        
        # 检查控制图关键词
        is_control = any(keyword in user_text_lower for keyword in self.control_keywords)
        
        # 检查参考图关键词
        is_reference = any(keyword in user_text_lower for keyword in self.reference_keywords)
        
        if is_control and not is_reference:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_CONTROL_ONLY,
                confidence=RoutingConfidence.HIGH,
                reasoning="检测到控制图关键词，选择ControlNet工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_controlnet.json"
            )
        
        elif is_reference and not is_control:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_REFERENCE_ONLY,
                confidence=RoutingConfidence.HIGH,
                reasoning="检测到参考图关键词，选择参考图工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_redux.json"
            )
        
        elif is_control and is_reference:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_CONTROL_REFERENCE,
                confidence=RoutingConfidence.MEDIUM,
                reasoning="同时检测到控制和参考关键词，选择混合工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_controlnet.json"
            )
        
        else:
            # 尝试使用LLM分析
            llm_result = await self._route_with_llm(
                user_text, True, 1, query, WorkflowType.AIGEN
            )
            
            if llm_result:
                return llm_result
            
            # LLM分析失败，需要用户确认
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_CONTROL_ONLY,
                confidence=RoutingConfidence.LOW,
                reasoning="无法确定图片用途，需要用户确认",
                processing_time_ms=(time.time() - start_time) * 1000,
                needs_clarification=True,
                clarification_question="您上传的这张图片是用于控制画面结构/姿势，还是作为风格/内容参考？",
                workflow_file="flux_controlnet.json"
            )

    async def _analyze_double_image_usage(
        self, 
        user_text: str, 
        query: Optional[Any],
        start_time: float
    ) -> '_Level2RoutingResult':
        """分析双张图片用途"""
        user_text_lower = user_text.lower()
        
        # 检查是否明确指定了图片用途
        has_control_ref = any(keyword in user_text_lower for keyword in ["控制", "第一张", "control", "first"])
        has_reference_ref = any(keyword in user_text_lower for keyword in ["参考", "第二张", "reference", "second"])
        
        if has_control_ref and has_reference_ref:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.AIGEN_CONTROL_REFERENCE,
                confidence=RoutingConfidence.HIGH,
                reasoning="明确指定了控制图和参考图，选择混合工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="flux_controlnet.json"
            )
        
        # 尝试使用LLM分析
        llm_result = await self._route_with_llm(
            user_text, True, 2, query, WorkflowType.AIGEN
        )
        
        if llm_result:
            return llm_result
        
        # LLM分析失败，需要用户确认
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.AIGEN_CONTROL_REFERENCE,
            confidence=RoutingConfidence.LOW,
            reasoning="无法确定双图用途，需要用户确认",
            processing_time_ms=(time.time() - start_time) * 1000,
            needs_clarification=True,
            clarification_question="请明确指定两张图片的用途：第一张是控制图还是参考图？第二张是控制图还是参考图？",
            workflow_file="flux_controlnet.json"
        )

    def _route_kontext_pipeline(self, has_images: bool, image_count: int) -> '_Level2RoutingResult':
        """KONTEXT管道内工作流选择"""
        start_time = time.time()
        
        if not has_images or image_count == 0:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_SINGLE,
                confidence=RoutingConfidence.LOW,
                reasoning="KONTEXT工作流需要图片，但未提供",
                processing_time_ms=(time.time() - start_time) * 1000,
                needs_clarification=True,
                clarification_question="KONTEXT工作流需要至少一张图片，请上传图片",
                workflow_file="kontext_local_1image.json"
            )
        
        if image_count == 1:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_SINGLE,
                confidence=RoutingConfidence.HIGH,
                reasoning="单张图片，选择单图处理工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_local_1image.json"
            )
        
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.KONTEXT_MULTIPLE,
            confidence=RoutingConfidence.HIGH,
            reasoning="多张图片，选择多图处理工作流",
            processing_time_ms=(time.time() - start_time) * 1000,
            workflow_file="kontext_local_2images.json"
        )

    def _route_kontext_api_pipeline(self, has_images: bool, image_count: int) -> '_Level2RoutingResult':
        """KONTEXT_API管道内工作流选择"""
        start_time = time.time()
        
        if not has_images or image_count == 0:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_SINGLE,
                confidence=RoutingConfidence.LOW,
                reasoning="KONTEXT_API工作流需要图片，但未提供",
                processing_time_ms=(time.time() - start_time) * 1000,
                needs_clarification=True,
                clarification_question="KONTEXT_API工作流需要至少一张图片，请上传图片",
                workflow_file="kontext_api_1image.json"
            )
        
        if image_count == 1:
            return _Level2RoutingResult(
                workflow_subtype=WorkflowSubType.KONTEXT_API_SINGLE,
                confidence=RoutingConfidence.HIGH,
                reasoning="单张图片，选择单图API工作流",
                processing_time_ms=(time.time() - start_time) * 1000,
                workflow_file="kontext_api_1image.json"
            )
        
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.KONTEXT_API_MULTIPLE,
            confidence=RoutingConfidence.HIGH,
            reasoning="多张图片，选择多图API工作流",
            processing_time_ms=(time.time() - start_time) * 1000,
            workflow_file="kontext_api_2images.json"
        )

    async def _route_with_llm(
        self,
        user_text: str,
        has_images: bool,
        image_count: int,
        query: Any,
        pipeline_type: WorkflowType
    ) -> Optional['_Level2RoutingResult']:
        """使用LLM进行智能路由"""
        
        if not self.ap or not query.pipeline_config:
            return None
        
        # 获取LLM模型
        model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
        if not model_uuid:
            return None
        
        # 找到对应的RuntimeLLMModel
        runtime_llm_model = None
        for model in self.ap.model_mgr.llm_models:
            if model.model_entity.uuid == model_uuid:
                runtime_llm_model = model
                break
        
        if not runtime_llm_model:
            return None
        
        # 构建用户查询
        user_prompt = f"""用户请求: "{user_text}"
输入条件:
- 是否有图片: {has_images}
- 图片数量: {image_count}

请分析用户意图并选择最合适的工作流。"""
        
        # 创建消息
        system_prompt = self.level_2_config["system_prompt"].format(
            pipeline_type=pipeline_type.value
        )
        messages = [
            llm_entities.Message(role='system', content=system_prompt),
            llm_entities.Message(role='user', content=user_prompt)
        ]
        
        try:
            # 调用LLM - 修复：使用正确的调用方法
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )

            # 提取响应文本 - 修复：使用与旧版相同的响应处理逻辑
            response_text = self._extract_response_text(result)
            if not response_text:
                return None

            # 解析LLM响应
            try:
                # 清理和解析JSON
                cleaned_text = self._clean_json_response(response_text)
                parsed_result = json.loads(cleaned_text)

                # 映射工作流类型
                workflow_subtype = self._map_llm_result_to_subtype(
                    parsed_result.get('workflow_type', ''),
                    pipeline_type,
                    has_images,
                    image_count
                )

                return _Level2RoutingResult(
                    workflow_subtype=workflow_subtype,
                    confidence=RoutingConfidence(parsed_result.get('confidence', 'medium')),
                    reasoning=parsed_result.get('reasoning', 'LLM分析结果'),
                    processing_time_ms=0,  # 将在外层计算
                    suggested_prompt=parsed_result.get('suggested_prompt', ''),
                    workflow_file=self.workflow_files.get(workflow_subtype, 'flux_default.json')
                )

            except json.JSONDecodeError as e:
                self.logger.warning(f"LLM响应不是有效的JSON格式: {e}, 响应内容: {response_text}")
                # 尝试提取JSON部分（备用方案）
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    try:
                        parsed_result = json.loads(json_match.group())
                        workflow_subtype = self._map_llm_result_to_subtype(
                            parsed_result.get('workflow_type', ''),
                            pipeline_type,
                            has_images,
                            image_count
                        )
                        return _Level2RoutingResult(
                            workflow_subtype=workflow_subtype,
                            confidence=RoutingConfidence(parsed_result.get('confidence', 'low')),
                            reasoning=parsed_result.get('reasoning', 'LLM分析结果（提取JSON）'),
                            processing_time_ms=0,
                            suggested_prompt=parsed_result.get('suggested_prompt', ''),
                            workflow_file=self.workflow_files.get(workflow_subtype, 'flux_default.json')
                        )
                    except:
                        pass
                return None

        except Exception as e:
            self.logger.error(f"LLM路由失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本 - 与旧版保持一致"""
        response_text = ""

        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)

        return response_text.strip()

    def _map_llm_result_to_subtype(
        self, 
        llm_workflow_type: str, 
        pipeline_type: WorkflowType,
        has_images: bool,
        image_count: int
    ) -> WorkflowSubType:
        """将LLM结果映射到工作流子类型"""
        
        if pipeline_type == WorkflowType.AIGEN:
            if not has_images:
                return WorkflowSubType.AIGEN_TEXT_ONLY
            elif image_count == 1:
                return WorkflowSubType.AIGEN_CONTROL_ONLY
            else:
                return WorkflowSubType.AIGEN_CONTROL_REFERENCE
                
        elif pipeline_type == WorkflowType.KONTEXT:
            if image_count == 1:
                return WorkflowSubType.KONTEXT_SINGLE
            else:
                return WorkflowSubType.KONTEXT_MULTIPLE
                
        elif pipeline_type == WorkflowType.KONTEXT_API:
            if image_count == 1:
                return WorkflowSubType.KONTEXT_API_SINGLE
            else:
                return WorkflowSubType.KONTEXT_API_MULTIPLE
        
        return WorkflowSubType.AIGEN_TEXT_ONLY

    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        return {
            "level_1_keywords": list(self.level_1_keywords.keys()),
            "workflow_files": dict(self.workflow_files),
            "config": {
                "level_2_enabled": self.level_2_config["enabled"],
                "timeout_ms": self.level_2_config["timeout_ms"]
            }
        }


def get_unified_router(ap=None) -> UnifiedRoutingSystem:
    """获取统一路由器实例"""
    return UnifiedRoutingSystem(ap) 