"""
核心消息处理模块
提供统一的消息处理和发送接口，支持所有工作流类型
"""

from .processor import MessageProcessor, message_processor
from .sender import MessageSender, message_sender
from .models import (
    MessageType,
    MessageContent,
    ProcessResult,
    ProcessContext,
    MessageFormat
)

__all__ = [
    'MessageProcessor',
    'message_processor',
    'MessageSender', 
    'message_sender',
    'MessageType',
    'MessageContent',
    'ProcessResult',
    'ProcessContext',
    'MessageFormat'
] 