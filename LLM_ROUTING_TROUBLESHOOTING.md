# LLM路由故障排除指南

## 常见问题和解决方案

### 1. 模型UUID设置问题
**问题**: LLM路由失败，提示模型UUID不存在
**解决方案**:
- 确保在Web界面中创建了DeepSeek模型
- 复制正确的模型UUID
- 使用 `python3 get_model_uuid.py --set-uuid <UUID>` 设置

### 2. API Key问题
**问题**: 模型调用失败，提示API Key无效
**解决方案**:
- 检查 `config/llm_config.yaml` 中的DeepSeek API Key
- 确保API Key有效且有余额
- 重启服务使配置生效

### 3. 触发词问题
**问题**: 机器人不响应 @机器人 aigen 命令
**解决方案**:
- 确保在群聊中使用@机器人
- 检查触发词配置是否正确
- 确保 `atbot-with-prefix` 设置为 true

### 4. 网络连接问题
**问题**: 连接DeepSeek API失败
**解决方案**:
- 检查网络连接
- 确认API端点地址正确
- 检查防火墙设置

### 5. 配置同步问题
**问题**: 修改配置后不生效
**解决方案**:
- 重启Docker容器确保配置生效
- 使用 `./langbot-manager.sh restart` 重启服务
- 检查配置文件格式是否正确

## 调试步骤

1. **检查模型配置**:
   ```bash
   python3 get_model_uuid.py --show
   ```

2. **检查服务状态**:
   ```bash
   ./langbot-manager.sh status
   ```

3. **查看日志**:
   ```bash
   ./langbot-manager.sh logs
   ```

4. **测试连接**:
   ```bash
   ./test_llm_routing.sh
   ```

## 联系支持

如果以上步骤都无法解决问题，请：
1. 收集相关日志信息
2. 记录具体的错误信息
3. 提供配置文件内容（隐藏敏感信息）
