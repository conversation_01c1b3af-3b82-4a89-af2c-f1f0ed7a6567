# 🎨 图片生成用户指南

## 📖 **三种触发指令**

系统支持三种简单的触发指令来启动图片生成服务：

### 🎯 **Aigen本地工作流**
```
aigen 一只可爱的猫咪
```
- 使用本地ComfyUI环境
- 支持纯文生图、控制图+文生图、参考图+文生图、混合模式
- 根据上传图片类型自动选择工作流

### 🏠 **Kontext本地模式**
```
kontext 编辑这张图片
```
- 使用本地ComfyUI环境
- 支持1图、2图、3图编辑
- 根据图片数量自动选择工作流

### ☁️ **Kontext API模式**
```
kontext api 编辑这张图片
```
- 使用云端ComfyUI API
- 支持1图、2图、3图编辑
- 适合快速生成

## 🎯 **会话式交互**

### **启动阶段**
```
用户: "aigen 一只猫咪"
机器人: "🎨 开始Aigen工作流...\n📝 提示词: 一只猫咪\n🔧 工作流: 标准文生图\n💡 发送'开始'指令生成图片"
```

### **交互阶段（仅在同一会话内）**
```
用户: "让它更可爱一些"
机器人: "📝 更新提示词: 一只更可爱的猫咪"

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

### **结束/取消**
```
用户: "取消"
机器人: "❌ 工作流已取消"

用户: "go"
机器人: "🚀 开始生成图片..."
```

### **重要说明**
- 生成完成后会话自动结束
- 后续消息会被视为普通聊天
- 如需基于生成图片继续编辑，请引用图片并重新发送前缀指令

## 🎯 **使用示例**

### Aigen工作流示例（单次生成）
```
用户: "aigen 生成一个科幻机器人"
[上传草图]
机器人: "🎨 检测到草图，选择ControlNet工作流"

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

### Kontext工作流示例（单次生成）
```
用户: "kontext 编辑这张图片"
[上传1张图片]
机器人: "🎨 选择单图Kontext工作流"

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

### 基于生成图片的二次生成
```
用户: "aigen 一只可爱的猫咪"
机器人: "🎨 开始Aigen工作流..."

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]

用户: [引用刚才生成的图片] "aigen 让它更可爱一些"
机器人: "🎨 开始Aigen工作流..."
[检测到引用图片，选择参考图工作流]

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回新图片]
[工作流结束]
```

### 多轮调整（同一会话内）
```
用户: "aigen 一只可爱的猫咪"
机器人: "🎨 开始Aigen工作流..."

用户: "让它更可爱一些"
机器人: "📝 更新提示词: 一只更可爱的猫咪"

用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
```

## 💡 **使用技巧**

1. **智能工作流选择**：系统会根据图片类型和数量自动选择最适合的工作流
2. **会话式交互**：启动后无需重复前缀，直接发送指令即可（仅在同一会话内）
3. **多轮调整**：可以连续调整提示词，最后发送"开始"执行（仅在同一会话内）
4. **基于生成图片的编辑**：引用生成的图片，重新发送前缀指令进行二次生成
5. **超时保护**：会话超时自动中断，避免资源浪费

## ⚠️ **注意事项**

- 本地模式需要ComfyUI服务运行
- API模式需要有效的API Key
- 会话超时时间：5分钟
- 支持"开始"、"go"、"执行"等指令触发生成
- 支持"取消"、"cancel"等指令中断工作流
- 支持引用消息中的图片和文本内容

## 🔧 **工作流类型说明**

### Aigen工作流
- **纯文生图**：无图片输入
- **控制图+文生图**：草图、线稿等控制图像
- **参考图+文生图**：风格、内容参考图像
- **混合模式**：控制图+参考图+文字

### Kontext工作流
- **单图编辑**：1张图片输入
- **双图编辑**：2张图片输入
- **多图编辑**：3张图片输入

## 🔧 **技术说明**

- **本地模式**：调用 `http://localhost:8188` 的ComfyUI服务
- **API模式**：使用ComfyUI官方API Key调用云端服务
- **会话管理**：系统自动管理用户会话，支持多轮对话
- **智能提取**：自动从引用消息中提取图片和文本内容

---

**就是这么简单！记住两个指令，开始创作吧！** 🎨✨ 