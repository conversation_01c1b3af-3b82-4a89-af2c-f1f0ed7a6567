"""
图片处理通用工具函数
"""

import base64
import hashlib
import imghdr
from typing import Optional, Dict, Any, <PERSON><PERSON>


def decode_base64_image(base64_str: str) -> Optional[bytes]:
    """
    解码base64字符串为图片二进制数据
    
    Args:
        base64_str: base64编码的图片字符串
        
    Returns:
        图片二进制数据或None
    """
    try:
        # 移除可能的data URL前缀
        if ',' in base64_str:
            base64_str = base64_str.split(',', 1)[1]
        
        # 解码
        return base64.b64decode(base64_str)
    except Exception:
        return None


def validate_image(image_data: bytes, max_size_mb: int = 10) -> Tuple[bool, str]:
    """
    验证图片数据的有效性
    
    Args:
        image_data: 图片二进制数据
        max_size_mb: 最大允许大小（MB）
        
    Returns:
        (是否有效, 错误信息)
    """
    if not image_data:
        return False, "图片数据为空"
    
    # 检查大小
    size_mb = len(image_data) / (1024 * 1024)
    if size_mb > max_size_mb:
        return False, f"图片大小 {size_mb:.2f}MB 超过限制 {max_size_mb}MB"
    
    # 检查是否是有效图片格式
    image_type = detect_image_type(image_data)
    if image_type == "unknown":
        return False, "不支持的图片格式"
    
    return True, ""


def detect_image_type(image_data: bytes) -> str:
    """
    检测图片类型
    
    Args:
        image_data: 图片二进制数据
        
    Returns:
        图片类型字符串 (jpeg, png, gif, webp, unknown)
    """
    if not image_data:
        return "unknown"
    
    # 使用imghdr检测图片类型
    img_type = imghdr.what(None, h=image_data)
    if img_type:
        return img_type
    
    # 手动检测常见格式
    if image_data.startswith(b'\xff\xd8\xff'):
        return "jpeg"
    elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
        return "png"
    elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
        return "gif"
    elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
        return "webp"
    elif image_data.startswith(b'BM'):
        return "bmp"
    else:
        return "unknown"


def extract_image_metadata(image_data: bytes) -> Dict[str, Any]:
    """
    提取图片元数据
    
    Args:
        image_data: 图片二进制数据
        
    Returns:
        元数据字典
    """
    metadata = {
        'size_bytes': len(image_data),
        'size_mb': len(image_data) / (1024 * 1024),
        'type': detect_image_type(image_data),
        'hash': hashlib.md5(image_data).hexdigest()
    }
    
    try:
        # 尝试使用PIL获取更多信息
        from PIL import Image
        import io
        
        with Image.open(io.BytesIO(image_data)) as img:
            metadata.update({
                'width': img.width,
                'height': img.height,
                'mode': img.mode,
                'format': img.format
            })
    except Exception:
        # 如果PIL不可用或图片无法解析，使用默认值
        metadata.update({
            'width': 0,
            'height': 0,
            'mode': 'unknown',
            'format': 'unknown'
        })
    
    return metadata


def get_image_hash(image_data: bytes) -> str:
    """
    获取图片的MD5哈希值
    
    Args:
        image_data: 图片二进制数据
        
    Returns:
        MD5哈希字符串
    """
    return hashlib.md5(image_data).hexdigest()


def resize_image_if_needed(image_data: bytes, max_width: int = 2048, max_height: int = 2048) -> bytes:
    """
    如果图片过大则调整尺寸
    
    Args:
        image_data: 原始图片数据
        max_width: 最大宽度
        max_height: 最大高度
        
    Returns:
        调整后的图片数据
    """
    try:
        from PIL import Image
        import io
        
        with Image.open(io.BytesIO(image_data)) as img:
            # 检查是否需要调整
            if img.width <= max_width and img.height <= max_height:
                return image_data
            
            # 计算新尺寸，保持宽高比
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # 调整尺寸
            resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 保存为字节
            output = io.BytesIO()
            format_name = img.format if img.format else 'JPEG'
            resized.save(output, format=format_name)
            return output.getvalue()
            
    except Exception:
        # 如果调整失败，返回原图
        return image_data


def is_image_similar(image1_data: bytes, image2_data: bytes, threshold: float = 0.9) -> bool:
    """
    比较两张图片是否相似（基于哈希）
    
    Args:
        image1_data: 第一张图片数据
        image2_data: 第二张图片数据
        threshold: 相似度阈值
        
    Returns:
        是否相似
    """
    try:
        from PIL import Image
        import io
        
        def get_image_hash(data):
            with Image.open(io.BytesIO(data)) as img:
                # 转换为灰度并调整为8x8
                img = img.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
                pixels = list(img.getdata())
                
                # 计算平均值
                avg = sum(pixels) / len(pixels)
                
                # 生成哈希
                return ''.join('1' if p >= avg else '0' for p in pixels)
        
        hash1 = get_image_hash(image1_data)
        hash2 = get_image_hash(image2_data)
        
        # 计算汉明距离
        distance = sum(c1 != c2 for c1, c2 in zip(hash1, hash2))
        similarity = 1 - distance / len(hash1)
        
        return similarity >= threshold
        
    except Exception:
        # 如果无法比较，使用MD5哈希
        return get_image_hash(image1_data) == get_image_hash(image2_data) 