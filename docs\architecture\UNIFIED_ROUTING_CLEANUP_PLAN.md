# 统一路由系统清理计划

**文档编号**: CLEANUP-********-001  
**创建日期**: 2024-12-20  
**版本**: v1.0  
**状态**: 待执行  

---

## 概述

随着统一路由系统的完成，项目中存在一些旧的、冗余的路由相关代码和文件需要清理。本计划详细说明需要删除的文件和需要修改的代码。

## 清理目标

1. **删除旧的路由器实现** - 移除已被新系统替代的旧代码
2. **更新依赖引用** - 修改仍在使用旧路由器的文件
3. **清理测试代码** - 移除针对旧路由器的测试
4. **优化项目结构** - 减少代码冗余，提高维护性

---

## 🔴 可以安全删除的文件

### 1. 旧的路由器核心文件
```bash
# 删除旧的路由器实现
rm pkg/core/workflow/router.py                    # 411行，旧的WorkflowRouter
rm pkg/core/workflow/unified_llm_router.py        # 旧版本的统一LLM路由器
rm pkg/provider/runners/unified_routing_mixin.py  # 旧版本的Mixin
```

### 2. 相关的模型文件（如果只被旧路由器使用）
```bash
# 检查models.py是否只被旧路由器使用
rm pkg/core/workflow/models.py                    # 旧的WorkflowRoute等模型
```

### 3. 旧的测试文件
```bash
# 删除针对旧路由器的测试
rm tests/workers/test_old_router.py              # 如果存在
rm tests/workers/test_unified_llm_router.py      # 如果存在
```

---

## 🟡 需要修改的文件

### 1. 消息处理器
**文件**: `pkg/core/message/processor.py`  
**问题**: 使用了旧的WorkflowRouter  
**修改方案**: 替换为新的统一路由系统

```python
# 修改前
from ..workflow.router import WorkflowRouter

class MessageProcessor:
    def __init__(self, logger=None):
        self.workflow_router = WorkflowRouter(logger)

# 修改后
from ..workflow.unified_routing_system import get_unified_router

class MessageProcessor:
    def __init__(self, logger=None):
        self.unified_router = get_unified_router()
```

### 2. 统一智能Agent
**文件**: `pkg/provider/runners/unified_agent.py`  
**问题**: 使用了旧的WorkflowRouter  
**修改方案**: 替换为新的统一路由系统

```python
# 修改前
from pkg.core.workflow.router import WorkflowRouter

class UnifiedAgent:
    def __init__(self, config: dict, ap: Optional[Any] = None, pipeline_config: Optional[dict] = None):
        self.workflow_router = WorkflowRouter()

# 修改后
from pkg.core.workflow.unified_routing_system import get_unified_router

class UnifiedAgent:
    def __init__(self, config: dict, ap: Optional[Any] = None, pipeline_config: Optional[dict] = None):
        self.unified_router = get_unified_router(ap)
```

### 3. 智能工作流处理器
**文件**: `pkg/provider/runners/smart_workflow_handler.py`  
**问题**: 使用了旧的WorkflowRouter  
**修改方案**: 替换为新的统一路由系统

```python
# 修改前
from ...core.workflow.router import WorkflowRouter

class SmartWorkflowHandler:
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.workflow_router = WorkflowRouter()

# 修改后
from ...core.workflow.unified_routing_system import get_unified_router

class SmartWorkflowHandler:
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.unified_router = get_unified_router(ap)
```

### 4. 测试配置文件
**文件**: `tests/conftest_custom.py`  
**问题**: 导入了旧的WorkflowRouter  
**修改方案**: 更新导入和Mock对象

```python
# 修改前
from pkg.core.workflow.router import WorkflowRouter

@pytest.fixture
def workflow_router():
    return WorkflowRouter()

# 修改后
from pkg.core.workflow.unified_routing_system import get_unified_router

@pytest.fixture
def unified_router():
    return get_unified_router()
```

### 5. 工作流模块初始化文件
**文件**: `pkg/core/workflow/__init__.py`  
**问题**: 导出了旧的WorkflowRouter  
**修改方案**: 更新导出列表

```python
# 修改前
from .router import WorkflowRouter, workflow_router

__all__ = [
    'WorkflowRouter',
    'workflow_router',
    # ...
]

# 修改后
from .unified_routing_system import UnifiedRoutingSystem, get_unified_router

__all__ = [
    'UnifiedRoutingSystem',
    'get_unified_router',
    # ...
]
```

---

## 🟢 需要保留的文件

### 1. 新的统一路由系统
- ✅ `pkg/core/workflow/unified_routing_system.py` - 新的统一路由系统
- ✅ `pkg/provider/runners/unified_routing_mixin_v2.py` - 新的Mixin
- ✅ `config/unified_routing.yaml` - 配置文件
- ✅ `tests/workers/test_unified_routing_system.py` - 新系统的测试

### 2. 已集成的Runner
- ✅ `pkg/provider/runners/comfyui_agent.py` - 已集成新系统
- ✅ `pkg/provider/runners/smart_hybrid_agent.py` - 已集成新系统

### 3. 文档文件
- ✅ `docs/architecture/PRD-********-UnifiedRoutingSystem.md` - 设计文档
- ✅ `docs/unified_routing_usage_examples.md` - 使用示例

---

## 执行步骤

### 第一步：备份重要文件
```bash
# 创建备份目录
mkdir -p backup/old_routing_system

# 备份要删除的文件
cp pkg/core/workflow/router.py backup/old_routing_system/
cp pkg/core/workflow/unified_llm_router.py backup/old_routing_system/
cp pkg/provider/runners/unified_routing_mixin.py backup/old_routing_system/
cp pkg/core/workflow/models.py backup/old_routing_system/
```

### 第二步：修改依赖文件
1. 更新 `pkg/core/message/processor.py`
2. 更新 `pkg/provider/runners/unified_agent.py`
3. 更新 `pkg/provider/runners/smart_workflow_handler.py`
4. 更新 `tests/conftest_custom.py`
5. 更新 `pkg/core/workflow/__init__.py`

### 第三步：删除旧文件
```bash
# 删除旧的路由器文件
rm pkg/core/workflow/router.py
rm pkg/core/workflow/unified_llm_router.py
rm pkg/provider/runners/unified_routing_mixin.py
rm pkg/core/workflow/models.py

# 删除旧的测试文件（如果存在）
find tests/ -name "*router*" -not -name "*unified_routing*" -delete
```

### 第四步：验证系统
```bash
# 运行测试确保系统正常
python -m pytest tests/workers/test_unified_routing_system.py -v

# 检查导入是否正常
python -c "from pkg.core.workflow.unified_routing_system import get_unified_router; print('✅ 导入正常')"
```

---

## 风险评估

### 高风险项
1. **依赖关系复杂** - 多个文件依赖旧路由器
   - **缓解措施**: 逐步修改，每次修改后运行测试

2. **测试覆盖率** - 删除旧测试可能影响覆盖率
   - **缓解措施**: 确保新测试覆盖所有功能

### 中风险项
1. **导入错误** - 修改导入可能导致运行时错误
   - **缓解措施**: 仔细检查所有导入路径

2. **功能回归** - 清理过程中可能引入bug
   - **缓解措施**: 完整的测试验证

### 低风险项
1. **文档过时** - 清理后文档可能不准确
   - **缓解措施**: 更新相关文档

---

## 成功标准

### 功能标准
- [ ] 所有旧路由器文件已删除
- [ ] 所有依赖文件已更新
- [ ] 系统启动正常
- [ ] 路由功能正常

### 测试标准
- [ ] 新系统测试通过率100%
- [ ] 无导入错误
- [ ] 无运行时错误

### 代码标准
- [ ] 代码行数减少 >500行
- [ ] 无重复功能代码
- [ ] 导入关系清晰

---

## 清理后的项目结构

```
pkg/core/workflow/
├── __init__.py
├── unified_routing_system.py    # ✅ 新的统一路由系统
├── manager_base.py              # ✅ 保留
└── models.py                    # ❌ 已删除

pkg/provider/runners/
├── comfyui_agent.py             # ✅ 已集成新系统
├── smart_hybrid_agent.py        # ✅ 已集成新系统
├── unified_routing_mixin_v2.py  # ✅ 新的Mixin
├── unified_agent.py             # 🔄 需要修改
└── smart_workflow_handler.py    # 🔄 需要修改

tests/workers/
└── test_unified_routing_system.py  # ✅ 新系统测试

config/
└── unified_routing.yaml         # ✅ 配置文件
```

---

## 执行时间估算

- **备份文件**: 5分钟
- **修改依赖文件**: 30分钟
- **删除旧文件**: 5分钟
- **验证系统**: 15分钟
- **总计**: 约1小时

---

**文档完成日期**: 2024-12-20  
**执行状态**: 待执行  
**责任人**: 系统架构师  
**审批状态**: 待审批 