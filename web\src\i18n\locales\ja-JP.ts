const jaJP = {
  common: {
    login: 'ログイン',
    logout: 'ログアウト',
    email: 'メールアドレス',
    password: 'パスワード',
    welcome: 'LangBot へおかえりなさい 👋',
    continueToLogin: 'ログインしてください',
    loginSuccess: 'ログインに成功しました',
    loginFailed:
      'ログインに失敗しました。メールアドレスまたはパスワードをご確認ください',
    enterEmail: 'メールアドレスを入力',
    enterPassword: 'パスワードを入力',
    invalidEmail: '有効なメールアドレスを入力してください',
    emptyPassword: 'パスワードを入力してください',
    language: '言語',
    helpDocs: 'ヘルプドキュメント',
    create: '作成',
    edit: '編集',
    delete: '削除',
    add: '追加',
    select: '選択してください',
    cancel: 'キャンセル',
    submit: '送信',
    error: 'エラー',
    success: '成功',
    save: '保存',
    saving: '保存中...',
    confirm: '確認',
    confirmDelete: '削除の確認',
    deleteConfirmation: '本当に削除しますか？',
    selectOption: 'オプションを選択',
    required: '必須',
    enable: '有効にする',
    name: '名前',
    description: '説明',
    close: '閉じる',
    deleteSuccess: '削除に成功しました',
    deleteError: '削除に失敗しました：',
    addRound: 'ラウンドを追加',
    copySuccess: 'コピーに成功しました',
    test: 'テスト',
  },
  notFound: {
    title: 'ページが見つかりません',
    description:
      'お探しのページは存在しないようです。入力したURLが正しいか確認するか、ホームページに戻ってください。',
    back: '戻る',
    home: 'ホームに戻る',
    help: 'ヘルプドキュメントを見る',
  },
  models: {
    title: 'モデル設定',
    description: 'パイプラインで使用できるモデルを設定・管理',
    createModel: 'モデルを作成',
    editModel: 'モデルを編集',
    getModelListError: 'モデルリストの取得に失敗しました：',
    modelName: 'モデル名',
    modelProvider: 'モデルプロバイダー',
    modelBaseURL: 'ベースURL',
    modelAbilities: 'モデル機能',
    saveSuccess: '保存に成功しました',
    saveError: '保存に失敗しました：',
    createSuccess: '作成に成功しました',
    createError: '作成に失敗しました：',
    deleteSuccess: '削除に成功しました',
    deleteError: '削除に失敗しました：',
    deleteConfirmation: '本当にこのモデルを削除しますか？',
    modelNameRequired: 'モデル名は必須です',
    modelProviderRequired: 'モデルプロバイダーは必須です',
    requestURLRequired: 'リクエストURLは必須です',
    apiKeyRequired: 'APIキーは必須です',
    keyNameRequired: 'キー名は必須です',
    mustBeValidNumber: '有効な数値である必要があります',
    mustBeTrueOrFalse: 'true または false である必要があります',
    requestURL: 'リクエストURL',
    apiKey: 'APIキー',
    abilities: '機能',
    selectModelAbilities: 'モデル機能を選択',
    visionAbility: '視覚機能',
    functionCallAbility: '関数呼び出し',
    extraParameters: '追加パラメータ',
    addParameter: 'パラメータを追加',
    keyName: 'キー名',
    type: 'タイプ',
    value: '値',
    string: '文字列',
    number: '数値',
    boolean: 'ブール値',
    extraParametersDescription:
      'リクエストボディに追加されるパラメータ（max_tokens、temperature、top_p など）',
    selectModelProvider: 'モデルプロバイダーを選択',
    modelProviderDescription: 'プロバイダーが提供するモデル名をご入力ください',
    selectModel: 'モデルを選択してください',
    testSuccess: 'テストに成功しました',
    testError: 'テストに失敗しました。モデル設定を確認してください',
  },
  bots: {
    title: 'ボット',
    description:
      'ボットの作成と管理を行います。LangBotと各プラットフォームを接続するためのエントリーポイントです',
    createBot: 'ボットを作成',
    editBot: 'ボットを編集',
    getBotListError: 'ボットリストの取得に失敗しました：',
    botName: 'ボット名',
    botDescription: 'ボットの説明',
    botNameRequired: 'ボット名は必須です',
    botDescriptionRequired: 'ボットの説明は必須です',
    adapterRequired: 'アダプターは必須です',
    defaultDescription: 'ボット',
    getBotConfigError: 'ボット設定の取得に失敗しました：',
    saveSuccess: '保存に成功しました',
    saveError: '保存に失敗しました：',
    createSuccess:
      '作成が完了しました。有効化するか、パイプラインの設定を行ってください',
    createError: '作成に失敗しました：',
    deleteSuccess: '削除に成功しました',
    deleteError: '削除に失敗しました：',
    deleteConfirmation: '本当にこのボットを削除しますか？',
    platformAdapter: 'プラットフォーム/アダプター選択',
    selectAdapter: 'アダプターを選択',
    adapterConfig: 'アダプター設定',
    bindPipeline: 'パイプラインを紐付け',
    selectPipeline: 'パイプラインを選択',
    botLogTitle: 'ボットログ',
    enableAutoRefresh: '自動更新を有効にする',
    session: 'セッション',
    yesterday: '昨日',
    earlier: 'それ以前',
    dateFormat: '{{month}}月{{day}}日',
    setBotEnableError: 'ボットの有効状態の設定に失敗しました',
    log: 'ログ',
  },
  plugins: {
    title: 'プラグイン',
    description: 'LangBotの機能を拡張するプラグインをインストール・設定',
    createPlugin: 'プラグインを作成',
    editPlugin: 'プラグインを編集',
    installed: 'インストール済み',
    marketplace: 'プラグインマーケット',
    arrange: '並び替え',
    install: 'インストール',
    installFromGithub: 'GitHubからプラグインをインストール',
    onlySupportGithub: '現在はGitHubからのインストールのみサポートしています',
    enterGithubLink: 'プラグインのGitHubリンクを入力してください',
    installing: 'プラグインをインストール中...',
    installSuccess: 'プラグインのインストールに成功しました',
    installFailed: 'プラグインのインストールに失敗しました：',
    searchPlugin: 'プラグインを検索',
    sortBy: '並び順',
    mostStars: 'スター数順',
    recentlyAdded: '最近追加',
    recentlyUpdated: '最近更新',
    noMatchingPlugins: '一致するプラグインが見つかりません',
    loading: '読み込み中...',
    getPluginListError: 'プラグインリストの取得に失敗しました：',
    noPluginInstalled: 'プラグインがインストールされていません',
    pluginConfig: 'プラグイン設定',
    pluginSort: 'プラグインの並び替え',
    pluginSortDescription:
      'プラグインの順序は、同一イベント内での処理順序に影響します。カードをドラッグして並び替えが可能です',
    pluginSortSuccess: 'プラグインの並び替えに成功しました',
    pluginSortError: 'プラグインの並び替えに失敗しました：',
    pluginNoConfig: 'プラグインに設定項目がありません。',
    deleting: '削除中...',
    deletePlugin: 'プラグインを削除',
    cancel: 'キャンセル',
    saveConfig: '設定を保存',
    saving: '保存中...',
    confirmDeletePlugin:
      'プラグイン「{{author}}/{{name}}」を削除してもよろしいですか？',
    confirmDelete: '削除を確認',
    deleteError: '削除に失敗しました：',
    close: '閉じる',
    deleteConfirm: '削除の確認',
    modifyFailed: '変更に失敗しました：',
    eventCount: 'イベント：{{count}}',
    toolCount: 'ツール：{{count}}',
    starCount: 'スター：{{count}}',
  },
  pipelines: {
    title: 'パイプライン',
    description:
      'メッセージイベントの処理フローを定義し、ボットに紐付けて使用するパイプラインです',
    createPipeline: 'パイプラインを作成',
    editPipeline: 'パイプラインを編集',
    chat: 'チャット',
    getPipelineListError: 'パイプラインリストの取得に失敗しました：',
    daysAgo: '日前',
    today: '今日',
    updateTime: '更新日時',
    defaultBadge: 'デフォルト',
    basicInfo: '基本情報',
    aiCapabilities: 'AI機能',
    triggerConditions: 'トリガー条件',
    safetyControls: '安全制御',
    outputProcessing: '出力処理',
    nameRequired: '名前は必須です',
    descriptionRequired: '説明は必須です',
    createSuccess:
      '作成が完了しました。パイプラインの詳細パラメータを設定してください',
    createError: '作成に失敗しました：',
    saveSuccess: '保存に成功しました',
    saveError: '保存に失敗しました：',
    deleteConfirmation:
      '本当にこのパイプラインを削除しますか？このパイプラインに紐付けられたボットは動作しなくなります。',
    defaultPipelineCannotDelete: 'デフォルトパイプラインは削除できません',
    debugDialog: {
      title: 'パイプラインのチャット',
      selectPipeline: 'パイプラインを選択',
      sessionType: 'セッションタイプ',
      privateChat: 'プライベートチャット',
      groupChat: 'グループチャット',
      send: '送信',
      reset: '会話をリセット',
      inputPlaceholder: 'メッセージを入力...',
      noMessages: 'メッセージがありません',
      userMessage: 'ユーザー',
      botMessage: 'ボット',
      sendFailed: '送信に失敗しました',
      resetSuccess: '会話がリセットされました',
      resetFailed: 'リセットに失敗しました',
      loadMessagesFailed: 'メッセージの読み込みに失敗しました',
      loadPipelinesFailed: 'パイプラインの読み込みに失敗しました',
      atTips: 'ボットをメンション',
    },
  },
  register: {
    title: 'LangBot を初期化 👋',
    description: 'これはLangBotの初回起動です',
    adminAccountNote:
      '入力したメールアドレスとパスワードが初期管理者アカウントになります',
    register: '登録',
    initSuccess: '初期化に成功しました。ログインしてください',
    initFailed: '初期化に失敗しました：',
  },
};

export default jaJP;
