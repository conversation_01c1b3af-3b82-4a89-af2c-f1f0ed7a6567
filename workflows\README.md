# 工作流文件分类说明

## 🎯 本地Flux工作流（按功能分类）

### 1. 纯文生图
- **文件**: `flux_text_to_image.json`
- **用途**: 仅使用文字提示词生成图片
- **特点**: 基础Flux工作流，包含LoRA节点（默认使用add_details）

### 2. 控制图+文生图
- **文件**: `flux_controlnet.json`
- **用途**: 使用控制图（如草图、线稿）+ 文字提示词生成图片
- **特点**: 包含ControlNet节点，支持Canny边缘检测等控制方式

### 3. Redux参考图+文生图
- **文件**: `flux_redux.json`
- **用途**: 使用参考图（风格、内容参考）+ 文字提示词生成图片
- **特点**: 包含ImageToLatent节点，支持参考图风格迁移

### 4. 混合模式
- **文件**: `flux_hybrid.json`
- **用途**: 文字prompt + 控制图 + redux参考图的组合模式
- **特点**: 最复杂的工作流，结合了所有控制方式

## 🏠 本地Kontext工作流（按图片数量分类）

### 单图编辑
- **文件**: `kontext_local_1image.json`
- **用途**: 本地Kontext单图编辑
- **特点**: 使用本地ComfyUI环境，支持单图输入

## ☁️ API Kontext工作流（按图片数量分类）

### 单图编辑
- **文件**: `kontext_api_1image.json`
- **用途**: API Kontext单图编辑
- **特点**: 使用云端ComfyUI API，支持单图输入

### 双图编辑
- **文件**: `kontext_api_2images.json`
- **用途**: API Kontext双图编辑
- **特点**: 使用云端ComfyUI API，支持双图输入

### 多图编辑
- **文件**: `kontext_api_3images.json`
- **用途**: API Kontext多图编辑
- **特点**: 使用云端ComfyUI API，支持多图输入

## 🎨 其他工作流

### 功能工作流
- `image_to_text_workflow.json` - 图生文功能

## 🔧 LoRA模型说明

所有本地Flux工作流都包含LoRA节点，默认使用 `add_details.safetensors`。

### LoRA选择逻辑
1. 根据用户意图分析选择合适的LoRA模型
2. 如果找不到合适的LoRA，默认使用 `add_details`
3. 支持的LoRA类型：
   - 风格LoRA（如动漫、写实等）
   - 细节增强LoRA（如add_details）
   - 特定主题LoRA（如人物、风景等）

### 需要完善的LoRA模型
当前项目中缺少LoRA模型文件，建议：
1. 在ComfyUI的models/loras目录下放置LoRA文件
2. 常用的LoRA模型：
   - `add_details.safetensors` - 细节增强
   - `anime_style.safetensors` - 动漫风格
   - `realistic.safetensors` - 写实风格
   - `portrait.safetensors` - 人像专用

## 📝 使用建议

### 本地Flux工作流
- 根据用户输入和图片类型智能选择工作流
- 纯文生图：无图片输入
- 控制图+文生图：有草图、线稿等控制图像
- Redux参考图+文生图：有风格、内容参考图像
- 混合模式：同时有控制图和参考图

### Kontext工作流
- 本地模式：需要ComfyUI服务运行
- API模式：需要有效的API Key
- 根据图片数量自动选择对应工作流

## 🔄 工作流更新

如需更新工作流：
1. 在ComfyUI客户端中打开对应工作流
2. 修改节点参数或添加新节点
3. 导出为JSON文件
4. 替换项目中的对应文件
5. 重启LangBot服务

## ⚠️ 注意事项

1. 确保所有引用的模型文件存在（如Flux模型、VAE、LoRA等）
2. 工作流中的图片文件名需要与实际输入匹配
3. 节点ID需要保持唯一性，避免冲突
4. 建议定期备份工作流文件 