version: "3"

services:
  langbot:
    image: docker.langbot.app/langbot-public/rockchin/langbot:latest
    container_name: langbot
    volumes:
      # 数据目录
      - ./data:/app/data
      # 插件目录
      - ./plugins:/app/plugins
      # 二次开发代码挂载（覆盖容器内的pkg目录）
      - ./pkg:/app/pkg
      # 配置文件挂载
      - ./config:/app/config
      # 工作流文件
      - ./workflows:/app/workflows
      # 模板文件
      - ./templates:/app/templates
      # 资源文件
      - ./res:/app/res
    restart: on-failure
    environment:
      - TZ=Asia/Shanghai
      # ComfyUI Firebase认证token（用于Kontext工作流）
      - "API_KEY_COMFY_ORG=eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NYvSSddDVDFtPl8wPIXxUUEZn8s2hDtWByDeCJRd0IRf0b53JiWkIPOr6fSc1A7GZclYGc8z82JjbSqT6NnrJLrlgCCLrtuxw0ysgZhpRCeWRY17zimkNhAfNLfMVMzUlWyF8Vn09MUEX60AuyQYT1wN0FNd_NR2TFW6XL1Sw-xK4LUaORg1GWfKTzTH3vKgTWG4RTzf3WJoCo60l3NKAGOiRDJHK8AIBPKhEKF4YiR82Qcdd0_U6QFm4FcvFXM3oUvOue-fIhqOl0_tviz6Zz9sSrW4QbooK6whI30yXXBvYJWeYshi7pabIMTrHR88u9E-g0CKlakW3XHG0rffYg"
      - "AUTH_TOKEN_COMFY_ORG=eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NYvSSddDVDFtPl8wPIXxUUEZn8s2hDtWByDeCJRd0IRf0b53JiWkIPOr6fSc1A7GZclYGc8z82JjbSqT6NnrJLrlgCCLrtuxw0ysgZhpRCeWRY17zimkNhAfNLfMVMzUlWyF8Vn09MUEX60AuyQYT1wN0FNd_NR2TFW6XL1Sw-xK4LUaORg1GWfKTzTH3vKgTWG4RTzf3WJoCo60l3NKAGOiRDJHK8AIBPKhEKF4YiR82Qcdd0_U6QFm4FcvFXM3oUvOue-fIhqOl0_tviz6Zz9sSrW4QbooK6whI30yXXBvYJWeYshi7pabIMTrHR88u9E-g0CKlakW3XHG0rffYg"
    network_mode: host  # 使用host网络模式
    # 根据具体环境配置网络
