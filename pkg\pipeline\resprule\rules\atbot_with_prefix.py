from __future__ import annotations

from .. import rule as rule_model
from .. import entities
from ....core import entities as core_entities
from ....platform.types import message as platform_message


@rule_model.rule_class('atbot-with-prefix')
class AtBotWithPrefixRule(rule_model.GroupRespondRule):
    """
    复合规则：要求同时满足@机器人和包含触发词两个条件
    """
    
    async def match(
        self,
        message_text: str,
        message_chain: platform_message.MessageChain,
        rule_dict: dict,
        query: core_entities.Query,
    ) -> entities.RuleJudgeResult:
        # 检查配置中是否启用了这个复合规则
        if not rule_dict.get('atbot-with-prefix', False):
            return entities.RuleJudgeResult(matching=False, replacement=message_chain)
        
        # 第一个条件：必须@机器人
        has_at_bot = message_chain.has(platform_message.At(query.adapter.bot_account_id))
        if not has_at_bot:
            return entities.RuleJudgeResult(matching=False, replacement=message_chain)
        
        # 🔥 关键修复：创建临时消息链，移除@机器人后再检查前缀
        temp_chain = message_chain.copy()
        temp_chain.remove(platform_message.At(query.adapter.bot_account_id))
        
        # 检查是否有重复的@（回复消息时会at两次）
        if temp_chain.has(platform_message.At(query.adapter.bot_account_id)):
            temp_chain.remove(platform_message.At(query.adapter.bot_account_id))
        
        # 获取移除@机器人后的纯文本
        clean_text = str(temp_chain).strip()
        
        # 第二个条件：必须包含触发词（在清理后的文本中检查）
        prefixes = rule_dict.get('prefix', [])
        has_prefix = False
        matched_prefix = None
        
        for prefix in prefixes:
            if clean_text.startswith(prefix):
                has_prefix = True
                matched_prefix = prefix
                break
        
        if not has_prefix:
            return entities.RuleJudgeResult(matching=False, replacement=message_chain)
        
        # 两个条件都满足，处理消息
        # 1. 移除@机器人
        message_chain.remove(platform_message.At(query.adapter.bot_account_id))
        
        # 检查是否有重复的@（回复消息时会at两次）
        if message_chain.has(platform_message.At(query.adapter.bot_account_id)):
            message_chain.remove(platform_message.At(query.adapter.bot_account_id))
        
        # 2. 移除触发词前缀
        if matched_prefix:
            for me in message_chain:
                if isinstance(me, platform_message.Plain):
                    me.text = me.text.strip()  # 先清理空格
                    if me.text.startswith(matched_prefix):
                        me.text = me.text[len(matched_prefix):].strip()
                    break
        
        return entities.RuleJudgeResult(
            matching=True,
            replacement=message_chain,
        ) 