"""
Kontext 会话管理模块
负责用户会话的创建、获取、生命周期管理等
"""
from typing import Dict, Optional
import time

class KontextSession:
    def __init__(self, user_id: str, chat_id: str, data: Optional[dict] = None, expire_seconds: int = 3600):
        self.user_id = user_id
        self.chat_id = chat_id
        self.data = data or {}
        self.created_at = time.time()
        self.expire_seconds = expire_seconds

    def is_expired(self) -> bool:
        return (time.time() - self.created_at) > self.expire_seconds

class KontextSessionManager:
    """
    管理Kontext用户会话
    """
    def __init__(self):
        self.sessions: Dict[str, KontextSession] = {}

    def _session_key(self, user_id: str, chat_id: str) -> str:
        return f"{user_id}::{chat_id}"

    def get_user_session(self, user_id: str, chat_id: str) -> Optional[KontextSession]:
        key = self._session_key(user_id, chat_id)
        session = self.sessions.get(key)
        if session and not session.is_expired():
            return session
        return None

    def create_session(self, user_id: str, chat_id: str, data: Optional[dict] = None, expire_seconds: int = 3600) -> KontextSession:
        key = self._session_key(user_id, chat_id)
        session = KontextSession(user_id, chat_id, data, expire_seconds)
        self.sessions[key] = session
        return session

    def remove_session(self, user_id: str, chat_id: str):
        key = self._session_key(user_id, chat_id)
        if key in self.sessions:
            del self.sessions[key]

    def cleanup_expired_sessions(self):
        expired_keys = [k for k, s in self.sessions.items() if s.is_expired()]
        for k in expired_keys:
            del self.sessions[k]

# TODO: 实现会话管理相关方法 