#!/usr/bin/env python3
"""
调试完整Flux工作流执行过程
模拟从用户输入到图片生成的完整流程
"""

import base64
import json
import os
import sys
import asyncio
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

def create_test_session_image() -> Any:
    """创建测试SessionImage对象"""
    try:
        from pkg.core.session.models import SessionImage, FluxImageType
        
        # 创建测试图片数据
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        # 创建SessionImage对象
        session_image = SessionImage(
            data=test_image_data,
            purpose="control",
            source="upload",
            flux_image_type=FluxImageType.CONTROL
        )
        
        print(f"✅ 成功创建SessionImage对象")
        print(f"📷 图片大小: {session_image.get_size()} bytes")
        print(f"🎯 图片类型: {session_image.flux_image_type.value}")
        
        return session_image
        
    except Exception as e:
        print(f"❌ 创建SessionImage对象失败: {e}")
        return None

def create_test_query() -> Any:
    """创建测试Query对象"""
    try:
        # 简化测试，直接返回None，让工作流管理器处理
        print(f"✅ 跳过Query对象创建，使用None")
        return None
        
    except Exception as e:
        print(f"❌ 创建Query对象失败: {e}")
        return None

async def test_flux_workflow_manager():
    """测试Flux工作流管理器"""
    print("\n🧪 测试Flux工作流管理器:")
    
    try:
        from pkg.workers.flux.flux_workflow_manager import get_flux_workflow_manager
        from pkg.core import app
        
        # 创建应用实例
        ap = app.Application()
        
        # 加载配置
        pipeline_config = {
            'ai': {
                'comfyui-agent': {
                    'api-url': 'http://localhost:8188',
                    'timeout': 180,
                    'workflow-path': 'workflows'
                }
            }
        }
        
        # 获取Flux工作流管理器
        workflow_manager = get_flux_workflow_manager(ap, pipeline_config)
        print(f"✅ 成功获取Flux工作流管理器")
        
        # 验证配置
        validation_result = workflow_manager.validate_configuration()
        print(f"📋 配置验证结果: {validation_result['valid']}")
        
        if not validation_result['valid']:
            print(f"❌ 配置验证失败: {validation_result['errors']}")
            return None
        
        return workflow_manager
        
    except Exception as e:
        print(f"❌ 测试Flux工作流管理器失败: {e}")
        return None

async def test_parameter_analyzer(workflow_manager: Any, user_text: str, query: Any) -> Optional[Any]:
    """测试参数分析器"""
    print(f"\n🔍 测试参数分析器:")
    print(f"📝 用户输入: {user_text}")
    
    try:
        if workflow_manager.parameter_analyzer is None:
            print("❌ parameter_analyzer 未初始化")
            return None
        
        # 执行参数分析
        analysis_result = await workflow_manager.parameter_analyzer.analyze_params_with_llm(user_text, query)
        
        if analysis_result.success:
            params = analysis_result.parameters
            print(f"✅ 参数分析成功")
            print(f"📋 分析结果:")
            print(f"  - 提示词: {getattr(params, 'prompt', 'N/A')}")
            print(f"  - 负面提示词: {getattr(params, 'negative_prompt', 'N/A')}")
            print(f"  - 种子: {getattr(params, 'seed', 'N/A')}")
            print(f"  - 步数: {getattr(params, 'steps', 'N/A')}")
            print(f"  - CFG: {getattr(params, 'cfg', 'N/A')}")
            return params
        else:
            print(f"❌ 参数分析失败: {analysis_result.error_message}")
            return None
            
    except Exception as e:
        print(f"❌ 参数分析异常: {e}")
        return None

async def test_seed_manager(workflow_manager: Any, params: Any) -> Optional[int]:
    """测试种子管理器"""
    print(f"\n🎲 测试种子管理器:")
    
    try:
        # 处理种子
        final_seed = workflow_manager.seed_manager.process_seed_instruction(params)
        params.seed = final_seed
        
        print(f"✅ 种子处理成功")
        print(f"🎲 最终种子: {final_seed}")
        print(f"📋 种子来源: {getattr(params, 'seed_instruction', {}).get('value', 'N/A')}")
        
        return final_seed
        
    except Exception as e:
        print(f"❌ 种子处理异常: {e}")
        return None

async def test_lora_integration(workflow_manager: Any, params: Any) -> Optional[list]:
    """测试LoRA集成"""
    print(f"\n🎨 测试LoRA集成:")
    
    try:
        # 选择LoRA
        selected_loras = workflow_manager.lora_integration.select_loras_for_prompt(params)
        print(f"✅ 选择LoRA成功，数量: {len(selected_loras)}")
        
        # 优化LoRA权重
        optimized_loras = workflow_manager.lora_integration.optimize_lora_weights(
            selected_loras, 
            getattr(params, 'quality_level', {}).get('value', 'standard')
        )
        print(f"✅ LoRA优化成功，数量: {len(optimized_loras)}")
        
        return optimized_loras
        
    except Exception as e:
        print(f"❌ LoRA集成异常: {e}")
        return None

async def test_workflow_execution(workflow_manager: Any, user_text: str, query: Any, session_images: list) -> bool:
    """测试完整工作流执行"""
    print(f"\n🚀 测试完整工作流执行:")
    
    try:
        # 执行完整Flux工作流
        result = await workflow_manager.execute_flux_workflow(user_text, query, session_images)
        
        if result.success:
            print(f"✅ 工作流执行成功")
            print(f"⏱️  执行时间: {getattr(result, 'execution_time', 'N/A')} 秒")
            print(f"📷 生成图片大小: {len(result.image_data) if result.image_data else 0} bytes")
            print(f"🎲 使用种子: {getattr(result, 'final_seed', 'N/A')}")
            print(f"🎨 使用LoRA数量: {len(getattr(result, 'used_loras', []))}")
            return True
        else:
            print(f"❌ 工作流执行失败: {getattr(result, 'error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 工作流执行异常: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 Flux工作流完整执行调试工具")
    print("=" * 60)
    
    # 1. 创建测试数据
    print("📋 创建测试数据:")
    session_image = create_test_session_image()
    query = create_test_query()
    
    if not session_image:
        print("❌ SessionImage创建失败")
        return
    
    # 2. 测试Flux工作流管理器
    workflow_manager = await test_flux_workflow_manager()
    if not workflow_manager:
        print("❌ Flux工作流管理器初始化失败")
        return
    
    # 3. 跳过参数分析（需要Query对象）
    print("\n⏭️  跳过参数分析（需要Query对象）")
    params = None
    user_text = "生成一张建筑照片，现代风格，高楼大厦"
    
    # 4. 跳过种子管理（需要参数）
    print("\n⏭️  跳过种子管理（需要参数）")
    
    # 5. 跳过LoRA集成（需要参数）
    print("\n⏭️  跳过LoRA集成（需要参数）")
    
    # 6. 测试完整工作流执行
    session_images = [session_image]
    success = await test_workflow_execution(workflow_manager, user_text, query, session_images)
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 工作流执行失败")
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    asyncio.run(main()) 