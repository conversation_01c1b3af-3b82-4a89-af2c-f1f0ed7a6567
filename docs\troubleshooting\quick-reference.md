# LangBot快速故障排除参考

## 🚨 常见问题快速诊断

### 1. 微信消息无响应

**症状**：发送消息后Bot无任何反应

**排查步骤**：
```bash
# 1. 检查容器状态
docker ps | grep langbot

# 2. 查看LangBot日志
docker logs langbot --tail 50

# 3. 检查wechatpad连接
docker logs langbot | grep "WebSocket connected"
```

**常见原因及解决方案**：
- ❌ **网络隔离** → ✅ 使用`network_mode: host`
- ❌ **容器未启动** → ✅ `docker start langbot`
- ❌ **配置错误** → ✅ 检查`config/wechatpad.yaml`

### 2. ComfyUI图片生成失败

**症状**：图片生成指令无响应或报错

**排查步骤**：
```bash
# 1. 检查ComfyUI API
curl http://127.0.0.1:8188/system_stats

# 2. 查看ComfyUI日志
docker logs comfyui --tail 50

# 3. 检查工作流文件
ls -la workflows/
```

**常见解决方案**：
- 重启ComfyUI容器
- 检查工作流路径映射
- 验证模型文件完整性

### 3. Docker网络问题

**快速修复**：
```bash
# 使用host网络模式
docker run --network host ... langbot

# 或创建共享网络
docker network create shared_net
docker run --network shared_net ...
```

## 🔧 一键修复脚本

```bash
#!/bin/bash
# langbot-fix.sh

echo "🔄 重启LangBot服务..."

# 停止容器
docker stop langbot
docker rm langbot

# 使用host网络重启
docker run -d \
  --name langbot \
  --restart on-failure \
  --network host \
  -e TZ=Asia/Shanghai \
  -v ./data:/app/data \
  -v ./plugins:/app/plugins \
  -v ./pkg:/app/pkg \
  -v ./config:/app/config \
  -v ./workflows:/app/workflows \
  -v ./templates:/app/templates \
  -v ./res:/app/res \
  docker.langbot.app/langbot-public/rockchin/langbot:latest

echo "✅ LangBot重启完成"
echo "🔍 检查状态: docker logs langbot --tail 20"
```

## 📞 获取帮助

- **官方文档**: https://docs.langbot.app/
- **社区群组**: 见官方文档
- **问题报告**: GitHub Issues

---
**最后更新**: 2025-06-21 