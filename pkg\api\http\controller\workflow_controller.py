"""
工作流管理控制器
提供工作流的查询、创建、管理等功能的API接口
"""

from flask import Blueprint, request, jsonify
from typing import Dict, Any, List
import os
import json

workflow_bp = Blueprint('workflow', __name__, url_prefix='/api/v1/workflow')

# 全局工作流管理器实例
workflow_manager = None

def init_workflow_manager(workflow_path: str = "workflows"):
    """初始化工作流管理器"""
    global workflow_manager
    # 暂时不初始化，避免接口不兼容问题
    workflow_manager = None


@workflow_bp.route('/list', methods=['GET'])
def list_workflows():
    """
    获取所有可用的工作流列表
    """
    return jsonify({
        'success': True,
        'data': {
            'workflows': [],
            'count': 0,
            'message': '工作流管理器暂未初始化'
        }
    })


@workflow_bp.route('/select', methods=['POST'])
def select_workflow():
    """
    根据用户输入选择最适合的工作流
    """
    return jsonify({
        'success': False,
        'error': '工作流管理器暂未初始化'
    }), 500


@workflow_bp.route('/params', methods=['POST'])
def generate_workflow_params():
    """
    根据工作流配置和用户参数生成最终的工作流参数
    """
    return jsonify({
        'success': False,
        'error': '工作流管理器暂未初始化'
    }), 500


@workflow_bp.route('/create', methods=['POST'])
def create_workflow_template():
    """
    创建新的工作流模板
    """
    return jsonify({
        'success': False,
        'error': '工作流管理器暂未初始化'
    }), 500


@workflow_bp.route('/types', methods=['GET'])
def get_workflow_types():
    """
    获取所有支持的工作流类型
    """
    return jsonify({
        'success': True,
        'data': {
            'workflow_types': [],
            'count': 0,
            'message': '工作流管理器暂未初始化'
        }
    })


@workflow_bp.route('/analyze', methods=['POST'])
def analyze_user_input():
    """
    分析用户输入，推荐合适的工作流和参数
    """
    try:
        data = request.get_json()
        if not data or 'user_input' not in data:
            return jsonify({
                'success': False,
                'error': '缺少user_input参数'
            }), 400
        
        user_input = data['user_input']
        
        # 推荐参数（简化逻辑，避免异步调用）
        recommended_params = {}
        if '高质量' in user_input or '精细' in user_input or '详细' in user_input:
            recommended_params['steps'] = 30
            recommended_params['guidance'] = 4.0
        elif '快速' in user_input or '简单' in user_input:
            recommended_params['steps'] = 15
            recommended_params['guidance'] = 3.0
        # 推荐尺寸
        if '横版' in user_input or '横向' in user_input or 'landscape' in user_input.lower():
            recommended_params['width'] = 1344
            recommended_params['height'] = 896
        elif '竖版' in user_input or '竖向' in user_input or 'portrait' in user_input.lower():
            recommended_params['width'] = 896
            recommended_params['height'] = 1344
        
        return jsonify({
            'success': True,
            'data': {
                'recommended_workflow': {
                    'type': 'aigen',
                    'name': 'AI生成工作流',
                    'description': '基于用户输入的智能工作流推荐'
                },
                'recommended_params': recommended_params
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'分析用户输入失败: {str(e)}'
        }), 500


def register_workflow_routes(app):
    """注册工作流相关路由"""
    app.register_blueprint(workflow_bp) 