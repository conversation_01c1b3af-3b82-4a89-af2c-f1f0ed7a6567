# ComfyUI 集成指南

## 概述

LangBot 支持与 ComfyUI 服务器集成，通过 AI Agent 实现图片生成功能。当前默认使用 Flux.1-dev 模型，这是一个高质量的图像生成模型。

## 功能特性

- 🎨 **Flux 模型支持**: 默认使用 Flux.1-dev 模型，支持高质量图像生成
- 🔄 **WebSocket 图片处理**: 通过 WebSocket 实时获取生成的图片数据
- 💬 **微信图片发送**: 自动将生成的图片发送到微信聊天
- ⚙️ **参数自定义**: 支持自定义图片尺寸、步数、引导强度等参数
- 📁 **工作流管理**: 支持多种工作流文件，可根据需求选择

## 配置说明

### 1. 流水线配置

在流水线配置中添加 ComfyUI Agent：

```yaml
ai:
  comfyui-agent:
    api-url: "http://localhost:8188"  # ComfyUI 服务器地址
    timeout: 120                      # 超时时间（秒）
    workflow-path: "workflows"        # 工作流文件目录
    default-workflow: "default_workflow.json"  # 默认工作流文件
```

### 2. 工作流文件

默认工作流使用 Flux.1-dev 模型，包含以下节点：

- **节点 6**: CLIPTextEncode - 处理文本提示词
- **节点 40**: DualCLIPLoader - 加载 Flux 专用 CLIP 模型
- **节点 42**: VAELoader - 加载 Flux VAE 模型
- **节点 50**: EmptyLatentImage - 创建空白潜空间
- **节点 54**: UNETLoader - 加载 Flux UNet 模型
- **节点 55**: FluxSamplerParams+ - Flux 专用采样器
- **节点 51**: VAEDecode - 解码潜空间为图片
- **节点 60**: SaveImageWebsocket - 通过 WebSocket 输出图片

### 3. 模型文件要求

确保 ComfyUI 服务器包含以下 Flux 模型文件：

```
models/
├── checkpoints/
│   └── flux1_dev.safetensors          # Flux UNet 模型
├── clip/
│   ├── clip_l.safetensors             # Flux CLIP 模型
│   └── t5xxl_fp8_e4m3fn.safetensors   # Flux T5 模型
└── vae/
    └── Flux_ae.safetensors            # Flux VAE 模型
```

## 使用方法

### 1. 基本图片生成

用户发送包含图片生成关键词的消息：

```
用户: 生成一张猫咪的图片
机器人: [生成并发送猫咪图片]
```

### 2. 详细描述生成

用户可以提供详细的图片描述：

```
用户: 画一个美丽的风景，有山有水，夕阳西下
机器人: [生成并发送风景图片]
```

### 3. 支持的触发关键词

- 中文：生成、画、图片、图像、照片
- 英文：create、generate、draw、image、picture

## 技术实现

### 1. 工作流执行流程

1. **参数解析**: 从用户消息中提取图片生成参数
2. **工作流加载**: 加载指定的工作流文件
3. **参数更新**: 将用户需求更新到工作流中
4. **提交执行**: 将工作流提交到 ComfyUI 服务器
5. **状态监控**: 实时监控工作流执行状态
6. **图片获取**: 通过 WebSocket 获取生成的图片数据
7. **图片发送**: 将图片发送到微信聊天

### 2. 图片处理流程

```python
# 1. 获取 WebSocket 图片数据
image_data = await self._download_image(session, filename)

# 2. 转换为 base64 格式
image_base64 = base64.b64encode(image_data).decode('utf-8')

# 3. 创建图片消息
image_content = llm_entities.ContentElement.from_image_base64(image_base64)

# 4. 发送到微信
await self._send_image_to_wechat(image_data, query)
```

### 3. 参数更新逻辑

工作流参数会根据用户需求自动更新：

- **提示词**: 更新节点 6 的 text 参数
- **采样参数**: 更新节点 55 的 steps、guidance 等参数
- **图片尺寸**: 更新节点 50 的 width、height 参数

## 默认参数

Flux 模型的推荐参数：

```python
{
    'width': 1024,      # 图片宽度
    'height': 1024,     # 图片高度
    'steps': 20,        # 采样步数
    'guidance': 3.5,    # 引导强度
    'seed': -1          # 随机种子（-1 表示随机）
}
```

## 错误处理

### 1. 常见错误

- **工作流文件不存在**: 检查工作流文件路径
- **ComfyUI 服务器连接失败**: 检查服务器地址和端口
- **模型文件缺失**: 确保所有 Flux 模型文件已下载
- **超时错误**: 增加 timeout 参数或检查服务器性能

### 2. 调试方法

1. 检查 ComfyUI 服务器日志
2. 查看 LangBot 应用日志
3. 验证工作流文件格式
4. 测试 ComfyUI API 连接

## 扩展功能

### 1. 自定义工作流

可以创建自定义工作流文件：

```json
{
  "6": {
    "inputs": {
      "text": "{{prompt}}",
      "clip": ["40", 0]
    },
    "class_type": "CLIPTextEncode"
  }
  // ... 其他节点
}
```

### 2. 多风格支持

通过不同的工作流文件支持多种图片风格：

- `realistic_workflow.json` - 写实风格
- `anime_workflow.json` - 动漫风格
- `artistic_workflow.json` - 艺术风格

### 3. 批量生成

支持批量生成多张图片，通过修改 batch_size 参数实现。

## 性能优化

### 1. 服务器配置

- 使用 GPU 加速（推荐 RTX 4090 或更高）
- 配置足够的内存（推荐 32GB+）
- 使用 SSD 存储模型文件

### 2. 参数调优

- 根据需求调整 steps 参数（更多步数 = 更高质量，但更慢）
- 调整 guidance 参数控制生成质量
- 使用固定 seed 获得可重复的结果

## 故障排除

### 1. 图片生成失败

1. 检查 ComfyUI 服务器状态
2. 验证模型文件完整性
3. 查看错误日志
4. 测试简单工作流

### 2. 图片发送失败

1. 检查微信连接状态
2. 验证图片文件格式
3. 检查临时文件权限
4. 查看网络连接

### 3. 性能问题

1. 检查 GPU 使用率
2. 优化工作流参数
3. 升级硬件配置
4. 使用更高效的模型

## 更新日志

### v1.0.0
- 初始版本，支持基本的图片生成功能
- 集成 Flux.1-dev 模型
- 支持 WebSocket 图片处理
- 实现微信图片发送功能

### v1.1.0
- 优化参数更新逻辑
- 改进错误处理机制
- 添加性能监控
- 支持自定义工作流

## 技术支持

如遇到问题，请：

1. 查看应用日志文件
2. 检查 ComfyUI 服务器状态
3. 验证配置文件格式
4. 联系技术支持团队 