name: trigger
label:
  en_US: Trigger
  zh_Hans: 触发条件
stages:
  - name: group-respond-rules
    label:
      en_US: Group Respond Rule
      zh_Hans: 群响应规则
    description:
      en_US: The respond rule of the messages in the groups
      zh_Hans: 群内消息的响应规则
    config:
      - name: at
        label:
          en_US: At
          zh_Hans: '@'
        description:
          en_US: Whether to trigger when the message mentions the bot
          zh_Hans: 是否在消息@机器人时触发
        type: boolean
        required: true
        default: false
      - name: prefix
        label:
          en_US: Prefix
          zh_Hans: 前缀
        description:
          en_US: Messages with these prefixes will be responded (the prefixes will be removed automatically when sending to AI)
          zh_Hans: 具有这些前缀的消息将被响应（发送给 AI 时会自动去除对应前缀）
        type: array[string]
        required: true
        default: []
      - name: regexp
        label:
          en_US: Regexp
          zh_Hans: 正则表达式
        description:
          en_US: Messages with these regular expressions will be responded
          zh_Hans: 符合这些正则表达式的消息将被响应
        type: array[string]
        required: true
        default: []
      - name: random
        label:
          en_US: Random
          zh_Hans: 随机
        description:
          en_US: 'Probability of automatically responding to messages that are not matched by other rules. Range: 0.0-1.0 (0%-100%).'
          zh_Hans: '自动响应其他规则未匹配的消息的概率。范围：0.0-1.0 (0%-100%)。'
        type: float
        required: false
        default: 0
  - name: access-control
    label:
      en_US: Access Control
      zh_Hans: 访问控制
    config:
      - name: mode
        label:
          en_US: Mode
          zh_Hans: 模式
        description:
          en_US: The mode of the access control
          zh_Hans: 访问控制模式
        type: select
        required: true
        default: blacklist
        options:
          - name: blacklist
            label:
              en_US: Blacklist
              zh_Hans: 黑名单
          - name: whitelist
            label:
              en_US: Whitelist
              zh_Hans: 白名单
      - name: blacklist
        label:
          en_US: Blacklist
          zh_Hans: 黑名单
        type: array[string]
        required: true
        default: []
      - name: whitelist
        label:
          en_US: Whitelist
          zh_Hans: 白名单
        type: array[string]
        required: true
        default: []
  - name: ignore-rules
    label:
      en_US: Ignore Rules
      zh_Hans: 消息忽略规则
    description:
      en_US: Ignore rules that apply to both group and private messages
      zh_Hans: 对群聊、私聊消息均适用的忽略规则（优先级高于群响应规则）
    config:
      - name: prefix
        label:
          en_US: Prefix
          zh_Hans: 前缀
        description:
          en_US: Messages with these prefixes will be ignored
          zh_Hans: 包含这些前缀的消息将被忽略
        type: array[string]
        required: true
        default: []
      - name: regexp
        label:
          en_US: Regexp
          zh_Hans: 正则表达式
        description:
          en_US: Messages with these regular expressions will be ignored
          zh_Hans: 符合这些正则表达式的消息将被忽略
        type: array[string]
        required: true
        default: []
  - name: misc
    label:
      en_US: Misc
      zh_Hans: 杂项
    config:
      - name: combine-quote-message
        label:
          en_US: Combine Quote Message
          zh_Hans: 合并引用消息
        description:
          en_US: If enabled, the bot will combine the quote message with the user's message
          zh_Hans: 如果启用，将合并引用消息与用户发送的消息
        type: boolean
        required: true
        default: true
