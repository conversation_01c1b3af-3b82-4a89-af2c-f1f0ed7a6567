apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: telegram
  label:
    en_US: Telegram
    zh_Hans: 电报
  description:
    en_US: Telegram Adapter
    zh_Hans: 电报适配器，请查看文档了解使用方式
  icon: telegram.svg
spec:
  config:
    - name: token
      label:
        en_US: Token
        zh_Hans: 令牌
      type: string
      required: true
      default: ""
    - name: markdown_card
      label:
        en_US: Markdown Card
        zh_Hans: 是否使用 Markdown 卡片
      type: boolean
      required: false
      default: true
execution:
  python:
    path: ./telegram.py
    attr: TelegramAdapter
