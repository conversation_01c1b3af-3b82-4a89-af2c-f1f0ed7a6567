"""
统一路由Mixin
为现有的Runner提供统一的LLM路由功能，替代硬编码的关键词匹配
"""

import logging
from typing import Optional, List, Dict, Any

from ...core.workflow.unified_llm_router import get_unified_router, RoutingResult, RouterConfidence
from ...core.session.models import WorkflowType
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...core import entities as core_entities


class UnifiedRoutingMixin:
    """统一路由Mixin，提供智能工作流路由功能"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.unified_router = get_unified_router(getattr(self, 'ap', None))
        self.logger = logging.getLogger(__name__)
    
    async def route_workflow_intelligently(
        self, 
        user_text: str, 
        query: Any,
        attached_images: Optional[List] = None
    ) -> RoutingResult:
        """
        智能路由工作流，替代硬编码的关键词匹配
        
        Args:
            user_text: 用户输入文本
            query: 查询对象
            attached_images: 附加的图片列表
            
        Returns:
            RoutingResult: 路由结果
        """
        # 计算图片信息
        has_images = bool(attached_images and len(attached_images) > 0)
        image_count = len(attached_images) if attached_images else 0
        
        # 调用统一路由器
        routing_result = await self.unified_router.route_workflow(
            user_text=user_text,
            has_images=has_images,
            image_count=image_count,
            query=query
        )
        
        # 记录路由决策
        self.logger.info(f"🎯 统一路由决策: {routing_result.workflow_type.value}")
        self.logger.info(f"   置信度: {routing_result.confidence.value}")
        self.logger.info(f"   推理: {routing_result.reasoning}")
        
        if routing_result.needs_clarification:
            self.logger.warning(f"   需要用户确认: {routing_result.clarification_question}")
        
        return routing_result
    
    def should_use_workflow_type(
        self, 
        user_text: str, 
        expected_workflow: WorkflowType,
        attached_images: Optional[List] = None
    ) -> bool:
        """
        判断是否应该使用指定的工作流类型（同步版本，用于向后兼容）
        
        Args:
            user_text: 用户输入文本
            expected_workflow: 期望的工作流类型
            attached_images: 附加的图片列表
            
        Returns:
            bool: 是否应该使用该工作流
        """
        # 快速启发式判断（无需LLM调用）
        has_images = bool(attached_images and len(attached_images) > 0)
        image_count = len(attached_images) if attached_images else 0
        
        heuristic_result = self.unified_router._route_with_heuristics(
            user_text, has_images, image_count
        )
        
        # 检查是否匹配期望的工作流
        is_match = heuristic_result.workflow_type == expected_workflow
        
        # 对于高置信度的结果，直接返回匹配结果
        if heuristic_result.confidence == RouterConfidence.HIGH:
            return is_match
        
        # 对于中等置信度，如果是第一选择或备选方案，也接受
        if heuristic_result.confidence == RouterConfidence.MEDIUM:
            if is_match or (heuristic_result.alternative_workflows and expected_workflow in heuristic_result.alternative_workflows):
                return True
        
        # 对于低置信度，保守处理
        return is_match
    
    def is_image_generation_request(self, user_text: str, attached_images: Optional[List] = None) -> bool:
        """
        判断是否是图片生成请求（向后兼容方法）
        
        Args:
            user_text: 用户输入文本
            attached_images: 附加的图片列表
            
        Returns:
            bool: 是否是图片生成请求
        """
        # 使用统一路由器的启发式判断
        has_images = bool(attached_images and len(attached_images) > 0)
        image_count = len(attached_images) if attached_images else 0
        
        result = self.unified_router._route_with_heuristics(
            user_text, has_images, image_count
        )
        
        # 任何工作流类型都算作图片生成请求
        return result.confidence in [RouterConfidence.HIGH, RouterConfidence.MEDIUM]
    
    def extract_workflow_command(self, user_text: str) -> tuple[Optional[str], str]:
        """
        提取工作流命令（向后兼容方法）
        
        Args:
            user_text: 用户输入文本
            
        Returns:
            tuple: (工作流类型, 去除前缀后的内容)
        """
        user_text_lower = user_text.lower().strip()
        
        # 检查明确的工作流前缀
        if user_text_lower.startswith('aigen '):
            return 'aigen', user_text[6:].strip()
        elif user_text_lower.startswith('kontext '):
            return 'kontext', user_text[8:].strip()
        elif user_text_lower.startswith('kontext_api '):
            return 'kontext_api', user_text[12:].strip()
        elif user_text_lower.startswith('@kontext '):
            return 'kontext', user_text[9:].strip()
        
        # 没有明确前缀
        return None, user_text
    
    def get_routing_explanation(self, routing_result: RoutingResult) -> str:
        """
        获取路由决策的解释说明
        
        Args:
            routing_result: 路由结果
            
        Returns:
            str: 解释说明
        """
        return self.unified_router.explain_routing_decision(routing_result)
    
    def format_workflow_options(self) -> str:
        """
        格式化工作流选项说明
        
        Returns:
            str: 格式化的工作流选项
        """
        options = []
        
        for workflow_type in [WorkflowType.AIGEN, WorkflowType.KONTEXT, WorkflowType.KONTEXT_API]:
            description = self.unified_router.get_workflow_description(workflow_type)
            options.append(description)
        
        return "\n".join(options)
    
    def suggest_workflow_command(self, user_text: str, routing_result: RoutingResult) -> str:
        """
        建议工作流命令
        
        Args:
            user_text: 用户输入文本
            routing_result: 路由结果
            
        Returns:
            str: 建议的命令
        """
        workflow_prefix = routing_result.workflow_type.value
        
        # 如果有建议的提示词，使用它
        if routing_result.suggested_prompt:
            return f"{workflow_prefix} {routing_result.suggested_prompt}"
        else:
            return f"{workflow_prefix} {user_text}"
    
    def log_routing_decision(self, routing_result: RoutingResult, user_text: str):
        """
        记录路由决策（用于调试和分析）
        
        Args:
            routing_result: 路由结果
            user_text: 用户输入文本
        """
        self.logger.info(f"🎯 统一路由决策详情:")
        self.logger.info(f"   用户输入: {user_text[:50]}{'...' if len(user_text) > 50 else ''}")
        self.logger.info(f"   选择工作流: {routing_result.workflow_type.value}")
        self.logger.info(f"   置信度: {routing_result.confidence.value}")
        self.logger.info(f"   推理原因: {routing_result.reasoning}")
        
        if routing_result.alternative_workflows:
            alt_names = [wf.value for wf in routing_result.alternative_workflows]
            self.logger.info(f"   备选方案: {', '.join(alt_names)}")
        
        if routing_result.needs_clarification:
            self.logger.warning(f"   需要确认: {routing_result.clarification_question}")
        
        if routing_result.suggested_prompt:
            self.logger.info(f"   建议提示词: {routing_result.suggested_prompt}")
    
    def handle_routing_clarification(self, routing_result: RoutingResult) -> Optional[str]:
        """
        处理路由需要确认的情况
        
        Args:
            routing_result: 路由结果
            
        Returns:
            Optional[str]: 需要发送给用户的确认消息
        """
        if not routing_result.needs_clarification:
            return None
        
        explanation = self.get_routing_explanation(routing_result)
        
        message = f"🤔 **需要确认工作流选择**\n\n"
        message += explanation
        
        if routing_result.alternative_workflows:
            message += f"\n💡 **其他选项**:\n"
            for alt_workflow in routing_result.alternative_workflows:
                alt_desc = self.unified_router.get_workflow_description(alt_workflow)
                message += f"  • {alt_desc}\n"
        
        message += f"\n📝 **建议**: 可以明确指定工作流前缀，如:\n"
        message += f"  • `aigen {routing_result.suggested_prompt or '你的提示词'}`\n"
        message += f"  • `kontext {routing_result.suggested_prompt or '你的编辑指令'}`\n"
        
        return message


# 工厂函数，用于为现有的Runner添加统一路由能力
def add_unified_routing(runner_class):
    """
    装饰器：为Runner类添加统一路由能力
    
    Args:
        runner_class: 要增强的Runner类
        
    Returns:
        增强后的Runner类
    """
    # 创建一个新的类，继承自原类和UnifiedRoutingMixin
    class EnhancedRunner(UnifiedRoutingMixin, runner_class):
        pass
    
    # 保持原有的类名和模块信息
    EnhancedRunner.__name__ = runner_class.__name__
    EnhancedRunner.__qualname__ = runner_class.__qualname__
    EnhancedRunner.__module__ = runner_class.__module__
    
    return EnhancedRunner 