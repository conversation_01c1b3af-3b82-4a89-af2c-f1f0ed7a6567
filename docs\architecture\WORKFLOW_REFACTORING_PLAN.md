# 工作流重构规划文档

## 📋 **当前工作流线路分析**

### **三条主要工作流线路**

#### 1. **Local Flux 工作流** (`AIGEN`)
- **位置**: `pkg/provider/runners/standard_image_handler.py`
- **特性**: 
  - 使用标准 Flux 模型进行文生图
  - 支持标准 ComfyUI 节点 (CLIPTextEncode, KSampler, VAEDecode)
  - 参数映射到标准节点ID (节点6, 55, 50等)
  - 支持 LoRA 模型集成
  - 智能参数分析和LLM优化提示词
  - 种子历史记录功能

#### 2. **Local Kontext 工作流** (`KONTEXT`)
- **位置**: `pkg/workers/kontext/local_executor.py`
- **特性**:
  - 使用自定义 FluxKontextProImageNode 节点
  - 支持多图片输入 (1-3张图片)
  - 特殊的 aspect_ratio 参数
  - prompt_upsampling 功能
  - 基于 easy loadImageBase64 的图片处理

#### 3. **Remote Kontext API 工作流** (`KONTEXT_API`)
- **位置**: `pkg/workers/kontext/api_executor.py`
- **特性**:
  - 远程 ComfyUI.com API 调用
  - 需要 API 认证 (API_KEY_COMFY_ORG)
  - 图片上传到远程服务器
  - 异步任务队列处理
  - 网络容错和重试机制

---

## 🔄 **共享模块识别**

### **可以统一的通用模块**

#### 1. **会话管理** ✅
- **统一模块**: `pkg/core/session/manager.py`
- **合并来源**:
  - `pkg/workers/unified_session_manager.py` (主要保留)
  - `pkg/workers/kontext/session_manager.py` (合并)
  - `pkg/workers/session_manager.py` (合并)

#### 2. **图片处理** ✅
- **统一模块**: `pkg/core/image/processor.py`
- **合并来源**:
  - `pkg/workers/image_utils.py` (主要保留)
  - `pkg/workers/kontext/image_processor.py` (合并)
  - `pkg/provider/runners/base_agent.py` 中的图片处理方法

#### 3. **意图分析和路由** ✅
- **统一模块**: `pkg/core/intent/analyzer.py` + `pkg/core/workflow/router.py`
- **合并来源**:
  - `pkg/workers/intent_analyzer.py` (主要保留)
  - `pkg/workers/intelligent_router.py` (简化为路由器)
  - `pkg/provider/runners/smart_workflow_handler.py` 中的意图分析

#### 4. **消息处理** ✅
- **统一模块**: `pkg/core/message/processor.py`
- **合并来源**:
  - `pkg/workers/message_processor.py`
  - `pkg/workers/message_sender.py`

#### 5. **基础工具** ✅
- **统一模块**: `pkg/core/utils/`
- **合并来源**:
  - `pkg/provider/runners/base_agent.py` 中的通用方法
  - 各处理器中的重复工具方法

---

## 🎯 **专有特性保留**

### **Local Flux 工作流专有** ✅
```
pkg/workers/flux/
├── __init__.py
├── manager.py              # Flux工作流管理
├── parameter_analyzer.py   # LLM参数分析 (专有)
├── lora_integration.py     # LoRA模型集成 (专有)
├── seed_manager.py         # 种子历史管理 (专有)
└── standard_nodes.py       # 标准ComfyUI节点映射 (专有)
```

**专有特性**:
- 标准ComfyUI节点参数映射 (节点6, 55, 50)
- LLM智能参数分析
- LoRA模型自动选择和集成
- 种子历史记录和复用
- 标准Flux模型优化

### **Local Kontext 工作流专有** ✅
```
pkg/workers/kontext/
├── __init__.py
├── manager.py              # Kontext本地工作流管理
├── custom_nodes.py         # FluxKontextProImageNode (专有)
├── multi_image_handler.py  # 多图片输入处理 (专有)
├── aspect_optimizer.py     # 纵横比优化 (专有)
└── prompt_upsampler.py     # 提示词增强 (专有)
```

**专有特性**:
- FluxKontextProImageNode 自定义节点
- 多图片输入支持 (1-3张)
- aspect_ratio 智能选择
- prompt_upsampling 功能
- easy loadImageBase64 图片处理

### **Remote Kontext API 工作流专有** ✅
```
pkg/workers/kontext_api/
├── __init__.py
├── manager.py              # 远程API工作流管理
├── auth_handler.py         # API认证管理 (专有)
├── upload_manager.py       # 图片上传管理 (专有)
├── queue_monitor.py        # 远程队列监控 (专有)
└── retry_handler.py        # 网络重试机制 (专有)
```

**专有特性**:
- ComfyUI.com API 认证
- 图片上传到远程服务器
- 远程任务队列监控
- 网络容错和重试机制
- API限流和配额管理

---

## 🏗️ **新架构设计**

### **目录结构**
```
pkg/
├── core/                   # 核心共享模块 ✅
│   ├── session/
│   │   ├── __init__.py
│   │   ├── manager.py      # 统一会话管理
│   │   ├── models.py       # 会话数据模型
│   │   └── states.py       # 会话状态枚举
│   ├── image/
│   │   ├── __init__.py
│   │   ├── processor.py    # 统一图片处理
│   │   ├── analyzer.py     # 图片分析
│   │   └── utils.py        # 图片工具
│   ├── intent/
│   │   ├── __init__.py
│   │   ├── analyzer.py     # 统一意图分析
│   │   └── models.py       # 意图数据模型
│   ├── workflow/
│   │   ├── __init__.py
│   │   ├── router.py       # 工作流路由器
│   │   ├── executor.py     # 基础执行器接口
│   │   └── models.py       # 工作流数据模型
│   ├── message/
│   │   ├── __init__.py
│   │   ├── processor.py    # 消息处理
│   │   └── sender.py       # 消息发送
│   └── utils/
│       ├── __init__.py
│       ├── http.py         # HTTP工具
│       ├── auth.py         # 认证工具
│       └── validation.py   # 验证工具
│
├── workers/                # 业务层专有模块 ✅
│   ├── flux/               # Local Flux 专有 ✅
│   │   ├── __init__.py
│   │   ├── manager.py
│   │   ├── parameter_analyzer.py
│   │   ├── lora_integration.py
│   │   ├── seed_manager.py
│   │   └── standard_nodes.py
│   ├── kontext/            # Local Kontext 专有 ✅
│   │   ├── __init__.py
│   │   ├── manager.py
│   │   ├── custom_nodes.py
│   │   ├── multi_image_handler.py
│   │   ├── aspect_optimizer.py
│   │   └── prompt_upsampler.py
│   ├── kontext_api/        # Remote Kontext API 专有 ✅
│   │   ├── __init__.py
│   │   ├── manager.py
│   │   ├── auth_handler.py
│   │   ├── upload_manager.py
│   │   ├── queue_monitor.py
│   │   └── retry_handler.py
│   └── shared/             # 跨工作流共享 (但不是核心) ✅
│       ├── __init__.py
│       ├── lora_manager.py
│       └── comfyui_client.py
│
└── provider/runners/       # 接口层
    ├── __init__.py
    ├── unified_agent.py    # 统一智能Agent (新) ✅
    ├── local_agent.py      # 本地LLM Agent (保留)
    ├── dify_agent.py       # Dify API (保留)
    ├── dashscope_agent.py  # DashScope API (保留)
    └── n8n_agent.py        # N8n Webhook (保留)
```

---

## 📋 **重构实施计划**

### **阶段一: 核心模块提取** ✅ (1-2天)
- [x] **CORE-001**: 创建 `pkg/core/session/manager.py` - 统一会话管理
- [x] **CORE-002**: 创建 `pkg/core/image/processor.py` - 统一图片处理  
- [x] **CORE-003**: 创建 `pkg/core/intent/analyzer.py` - 统一意图分析
- [x] **CORE-004**: 创建 `pkg/core/workflow/router.py` - 工作流路由器
- [x] **CORE-005**: 创建 `pkg/core/message/processor.py` - 消息处理

### **阶段二: 专有模块重组** ✅ (2-3天)
- [x] **FLUX-001**: 重组 Flux 专有模块到 `pkg/workers/flux/`
- [x] **KONTEXT-001**: 重组 Local Kontext 专有模块到 `pkg/workers/kontext/`
- [x] **KONTEXT-API-001**: 重组 Remote Kontext API 专有模块到 `pkg/workers/kontext_api/`

### **阶段三: 接口层简化** ✅ (1天)
- [x] **RUNNER-001**: 创建 `unified_agent.py` 替代 `smart_hybrid_agent.py`
- [x] **RUNNER-002**: 简化其他 Agent 运行器

### **阶段四: 迁移和测试** 🔄 (1-2天)
- [ ] **MIGRATION-001**: 迁移现有功能到新架构
- [ ] **CLEANUP-001**: 删除冗余文件
- [ ] **TEST-001**: 全面功能测试

### **阶段五: 文档更新** 🔄 (1天)
- [x] **DOC-001**: 更新架构文档
- [ ] **DOC-002**: 更新开发指南

---

## 🎯 **重构原则**

### **共享原则**
- 会话管理、图片处理、意图分析等**通用功能**必须统一
- 避免重复实现相同逻辑
- 提供统一的接口和数据模型

### **专有原则**  
- 每条工作流线路的**特定功能**保持独立
- 不强制统一差异化的实现
- 保持各工作流的性能优化

### **兼容原则**
- 保持现有API接口不变
- 用户使用方式不受影响
- 渐进式重构，避免破坏性变更

---

## 📊 **预期收益**

### **代码质量**
- 减少 **40-50%** 的重复代码
- 提高代码可维护性
- 统一错误处理和日志

### **开发效率**
- 新功能开发更快 (共享模块复用)
- Bug 修复影响面更广 (一次修复, 三处受益)
- 测试覆盖更全面

### **系统稳定性**
- 统一的会话管理减少状态不一致
- 统一的图片处理减少格式错误
- 统一的错误处理提高容错性

---

## 🚀 **当前进度总结**

### **已完成** ✅
1. **核心模块提取** - 所有核心共享模块已创建
2. **专有模块重组** - 三个工作流专有模块已重组完成
3. **共享模块创建** - 跨工作流共享模块已创建
4. **统一接口设计** - UnifiedAgent 已创建

### **进行中** 🔄
1. **功能迁移** - 需要将现有功能迁移到新架构
2. **代码清理** - 删除冗余和重复代码
3. **测试验证** - 确保重构后功能正常

### **下一步** 📋
1. 完善核心模块的具体实现
2. 迁移现有功能到新架构
3. 进行全面的功能测试
4. 更新开发文档

---

*最后更新: 2024-12-19* 