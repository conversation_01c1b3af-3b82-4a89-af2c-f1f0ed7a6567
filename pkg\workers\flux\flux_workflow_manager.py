"""
Flux 工作流管理器
统一协调所有 Flux 专有模块，提供完整的 Flux 工作流执行能力
这是 Flux 工作流的主要入口
"""

import json
import time
import asyncio
import aiohttp
import base64
import os
from typing import Dict, Any, Optional, List
import logging
from dataclasses import dataclass

from ...core import app, entities as core_entities
from ...provider import entities as llm_entities
from .flux_workflow_models import (
    FluxParameters, AnalysisResult, ExecutionResult, 
    SeedInstruction, LoRAConfig
)
# ParameterAnalyzer已迁移到统一路由系统
# from .parameter_analyzer import get_parameter_analyzer
from .seed_manager import get_seed_manager
from .standard_nodes import get_standard_node_mapper
from .lora_integration import get_lora_integration
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult

class FluxWorkflowManager(BaseWorkflowManager):
    """Flux 工作流管理器"""
    
    def __init__(self, ap: Optional[app.Application] = None, pipeline_config: dict = {}):
        self.ap = ap
        self.pipeline_config = pipeline_config
        self.logger = logging.getLogger(__name__)
        
        # 获取ComfyUI配置
        comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
        self.api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        self.ws_url = self.api_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.timeout = comfyui_config.get('timeout', 180)
        self.workflow_path = comfyui_config.get('workflow-path', 'workflows')
        
        # 初始化专有模块
        # ParameterAnalyzer已迁移到统一路由系统
        # self.parameter_analyzer = get_parameter_analyzer(ap) if ap is not None else None
        self.parameter_analyzer = None
        self.seed_manager = get_seed_manager()
        self.node_mapper = get_standard_node_mapper(self.workflow_path)
        self.lora_integration = get_lora_integration()
        
        # 工作流状态
        self.current_session: Optional[Dict[str, Any]] = None
        self.current_workflow_data: Optional[Dict[str, Any]] = None
        self.last_execution_result: Optional[ExecutionResult] = None
    
    def is_flux_request(self, text: str) -> bool:
        """
        判断是否是Flux图片生成请求
        """
        if self.parameter_analyzer is not None:
            return self.parameter_analyzer.is_image_generation_request(text)
        return False
    
    async def execute_flux_workflow(self, user_text: str, query: Optional[Any] = None, session_images: Optional[List] = None) -> ExecutionResult:
        """
        执行完整的Flux工作流
        
        Args:
            user_text: 用户输入文本
            query: 查询对象
            session_images: 会话图片列表（SessionImage对象，包含类型信息）
            
        Returns:
            ExecutionResult: 执行结果
        """
        start_time = time.time()
        result = ExecutionResult(success=False)
        
        try:
            self.logger.info(f"开始执行Flux工作流: {user_text}")
            
            # 步骤1: 参数分析（集成统一路由系统）
            self.logger.info("步骤1: 开始参数分析")
            params = await self._analyze_parameters_with_unified_system(user_text, query)
            result.final_parameters = params
            self.logger.info(f"参数分析完成: prompt='{params.prompt[:50]}...', size={params.width}x{params.height}")
            
            # 步骤2: 种子处理
            self.logger.info("步骤2: 开始种子处理")
            final_seed = self.seed_manager.process_seed_instruction(params)
            params.seed = final_seed
            result.final_seed = final_seed
            result.seed_source = params.seed_instruction.value
            self.logger.info(f"种子处理完成: {final_seed}")
            
            # 步骤3: LoRA选择和优化
            self.logger.info("步骤3: 开始LoRA选择和优化")
            selected_loras = self.lora_integration.select_loras_for_prompt(params)
            optimized_loras = self.lora_integration.optimize_lora_weights(
                selected_loras, 
                params.quality_level.value
            )
            result.used_loras = optimized_loras
            self.logger.info(f"LoRA优化完成: {len(optimized_loras)} 个LoRA")
            
            # 步骤4: 工作流构建
            self.logger.info("步骤4: 开始工作流构建")
            
            # 根据图片类型智能选择工作流模板
            workflow_file = self._select_workflow_by_image_type(session_images)
            self.logger.info(f"选择工作流: {workflow_file}")
            
            workflow_data = self.node_mapper.load_workflow_template(workflow_file)
            if not workflow_data:
                result.error_message = f"无法加载工作流模板: {workflow_file}"
                return result
            
            # 图片数据处理交给_apply_images_by_type方法，这里不需要预处理
            
            # 🔥 关键修复：应用用户参数到工作流（种子、提示词、LoRA等）
            self.logger.info("应用用户参数到工作流")
            workflow_data = self.node_mapper.apply_parameters_to_workflow(workflow_data, params)
            self.logger.info(f"成功应用用户提示词: {params.prompt[:100]}...")
            
            # 🔥 关键修复：重新添加图片数据分配逻辑
            if session_images and len(session_images) > 0:
                self.logger.info("应用图片数据到工作流节点")
                workflow_data = self._apply_images_by_type(workflow_data, session_images)
            
            # 应用LoRA到工作流
            workflow_data = self.lora_integration.apply_loras_to_workflow(workflow_data, optimized_loras)
            
            # 🔥 设置当前工作流数据，供输出节点识别使用
            self.current_workflow_data = workflow_data
            
            # 记录节点映射
            result.node_mappings = self.node_mapper.get_node_mappings(workflow_data)
            result.workflow_file = "flux_generated_workflow.json"
            
            # 步骤5: 执行工作流
            self.logger.info("步骤5: 开始执行ComfyUI工作流")
            image_data = await self._execute_comfyui_workflow(workflow_data)
            
            if image_data:
                result.success = True
                result.image_data = image_data
                result.execution_time = time.time() - start_time
                self.logger.info(f"Flux工作流执行成功，耗时: {result.execution_time:.2f}秒")
            else:
                result.error_message = "ComfyUI工作流执行失败"
                self.logger.error("ComfyUI工作流执行失败")
            
        except Exception as e:
            result.error_message = f"Flux工作流执行异常: {e}"
            self.logger.error(f"Flux工作流执行异常: {e}")
        
        return result
    
    async def _execute_comfyui_workflow(self, workflow_data: Dict[str, Any]) -> Optional[bytes]:
        """
        执行ComfyUI工作流
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            Optional[bytes]: 生成的图片数据
        """
        try:
            self.logger.info(f"开始执行ComfyUI工作流，API URL: {self.api_url}")
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                # 保存工作流数据到temp目录，便于调试
                os.makedirs('temp', exist_ok=True)
                with open(f'temp/last_workflow_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                    json.dump(workflow_data, f, ensure_ascii=False, indent=2)
                
                # 提交工作流
                self.logger.info("准备提交工作流到ComfyUI")
                prompt_id = await self._submit_workflow(session, workflow_data)
                if not prompt_id:
                    self.logger.error("提交工作流失败")
                    return None
                
                self.logger.info(f"工作流已提交，prompt_id: {prompt_id}")
                
                # 等待完成并获取结果
                self.logger.info("开始等待工作流完成")
                image_data = await self._wait_for_completion_via_api(prompt_id)
                if image_data:
                    self.logger.info(f"成功获取图片数据，大小: {len(image_data)} bytes")
                else:
                    self.logger.error("获取图片数据失败")
                return image_data
                
        except Exception as e:
            self.logger.error(f"执行ComfyUI工作流失败: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到ComfyUI"""
        try:
            # 📋 记录关键工作流参数用于调试
            self.logger.info("=" * 60)
            self.logger.info("🚀 FluxWorkflowManager准备提交工作流")
            self.logger.info("=" * 60)
            
            # 记录种子信息
            for node_id, node_data in workflow.items():
                if isinstance(node_data, dict):
                    class_type = node_data.get('class_type', '')
                    if class_type == "easy globalSeed":
                        inputs = node_data.get('inputs', {})
                        value = inputs.get('value', '未设置')
                        last_seed = inputs.get('last_seed', '未设置')
                        self.logger.info(f"  🎲 EasyGlobalSeed节点{node_id}: value={value}, last_seed={last_seed}")
                    elif "Sampler" in class_type:
                        inputs = node_data.get('inputs', {})
                        seed = inputs.get('seed', '未设置')
                        noise_seed = inputs.get('noise_seed', '未设置')
                        steps = inputs.get('steps', '未设置')
                        self.logger.info(f"  🎯 采样器节点{node_id}({class_type}): seed={seed}, noise_seed={noise_seed}, steps={steps}")
            
            prompt_data = {"prompt": workflow}
            
            async with session.post(f"{self.api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    self.logger.info(f"工作流提交成功: {prompt_id}")
                    return prompt_id
                else:
                    error_text = await response.text()
                    self.logger.error(f"提交工作流失败: {response.status}, {error_text}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"提交工作流异常: {e}")
            return None
    
    async def _wait_for_completion_via_api(self, prompt_id: str) -> Optional[bytes]:
        """通过API等待工作流完成"""
        try:
            # 轮询检查状态
            max_polls = 180  # 最大轮询次数
            poll_interval = 1  # 轮询间隔(秒)
            
            async with aiohttp.ClientSession() as session:
                for _ in range(max_polls):
                    # 检查队列状态
                    queue_info = await self._check_queue_status(session, prompt_id)
                    if queue_info is None:
                        self.logger.error("获取队列状态失败")
                        return None
                    
                    # 检查是否完成
                    if queue_info.get('completed', False):
                        self.logger.info("工作流执行完成")
                        
                        # 获取输出图片
                        image_data = await self._get_output_image(session, prompt_id)
                        return image_data
                    
                    # 检查是否出错
                    if queue_info.get('failed', False):
                        error_msg = queue_info.get('error', '未知错误')
                        self.logger.error(f"工作流执行失败: {error_msg}")
                        return None
                    
                    # 等待下次轮询
                    await asyncio.sleep(poll_interval)
                
                self.logger.error("工作流执行超时")
                return None
                
        except Exception as e:
            self.logger.error(f"等待工作流完成失败: {e}")
            return None
    
    async def _check_queue_status(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[Dict[str, Any]]:
        """检查队列状态"""
        try:
            async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history_data = await response.json()
                    
                    if prompt_id in history_data:
                        prompt_data = history_data[prompt_id]
                        status = prompt_data.get('status', {})
                        
                        return {
                            'completed': status.get('completed', False),
                            'failed': 'error' in prompt_data,
                            'error': prompt_data.get('error', {}).get('message', '') if 'error' in prompt_data else None
                        }
                    else:
                        # 还在队列中
                        return {'completed': False, 'failed': False}
                else:
                    self.logger.warning(f"检查队列状态失败: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"检查队列状态异常: {e}")
            return None
    
    async def _get_output_image(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[bytes]:
        """获取输出图片"""
        try:
            # 获取历史记录找到输出
            async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                if response.status != 200:
                    return None
                
                history_data = await response.json()
                prompt_data = history_data.get(prompt_id, {})
                outputs = prompt_data.get('outputs', {})
                
                self.logger.info(f"工作流输出节点: {list(outputs.keys())}")
                
                # 🔥 智能识别最终输出节点
                final_node_id = self._identify_final_output_node(outputs)
                if final_node_id:
                    output_data = outputs[final_node_id]
                    if 'images' in output_data:
                        images = output_data['images']
                        if images:
                            # 获取第一张图片
                            image_info = images[0]
                            filename = image_info['filename']
                            subfolder = image_info.get('subfolder', '')
                            image_type = image_info.get('type', '')
                            
                            self.logger.info(f"找到最终结果图片: 节点{final_node_id}, 文件: {filename}")
                            
                            # 下载图片
                            image_data = await self._download_image(session, filename, subfolder, image_type)
                            return image_data
                
                # 如果没有找到最终结果节点，查找其他预览节点
                for node_id, output_data in outputs.items():
                    if 'images' in output_data:
                        images = output_data['images']
                        if images:
                            # 获取第一张图片
                            image_info = images[0]
                            filename = image_info['filename']
                            subfolder = image_info.get('subfolder', '')
                            image_type = image_info.get('type', '')
                            
                            self.logger.info(f"找到预览图片: 节点{node_id}, 文件: {filename}")
                            
                            # 下载图片
                            image_data = await self._download_image(session, filename, subfolder, image_type)
                            return image_data
                
                self.logger.warning("没有找到输出图片")
                return None
                
        except Exception as e:
            self.logger.error(f"获取输出图片失败: {e}")
            return None
    
    def _identify_final_output_node(self, outputs: Dict[str, Any]) -> Optional[str]:
        """
        智能识别最终输出节点
        
        Args:
            outputs: 工作流输出节点数据
            
        Returns:
            Optional[str]: 最终输出节点ID，如果没有找到返回None
        """
        try:
            # 查找所有有图片输出的节点
            image_output_nodes = []
            
            for node_id, output_data in outputs.items():
                if 'images' in output_data and output_data['images']:
                    image_output_nodes.append(node_id)
            
            if not image_output_nodes:
                self.logger.warning("没有找到任何图片输出节点")
                return None
            
            # 🎯 第一优先级：查找标题为"final_image_output"的节点（VAEDecode节点）
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')
                        
                        if title == 'final_image_output':
                            self.logger.info(f"✅ 找到final_image_output节点: {node_id}")
                            return node_id
            
            # 🔥 第二优先级：查找其他VAEDecode节点
            if self.current_workflow_data:
                vae_decode_nodes = []
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        class_type = node_data.get('class_type', '')
                        
                        if class_type == 'VAEDecode':
                            vae_decode_nodes.append(node_id)
                
                if vae_decode_nodes:
                    # 选择ID最大的VAEDecode节点
                    try:
                        numeric_nodes = [(int(node_id), node_id) for node_id in vae_decode_nodes if node_id.isdigit()]
                        if numeric_nodes:
                            numeric_nodes.sort(key=lambda x: x[0], reverse=True)
                            final_node_id = numeric_nodes[0][1]
                            self.logger.info(f"✅ 选择VAEDecode节点: {final_node_id}")
                            return final_node_id
                    except (ValueError, TypeError):
                        pass
                    
                    final_node_id = vae_decode_nodes[0]
                    self.logger.info(f"✅ 选择第一个VAEDecode节点: {final_node_id}")
                    return final_node_id
            
            # 🔥 第三优先级：查找标题为"final_image"的PreviewImage节点
            if self.current_workflow_data:
                for node_id in image_output_nodes:
                    if node_id in self.current_workflow_data:
                        node_data = self.current_workflow_data[node_id]
                        title = node_data.get('_meta', {}).get('title', '')
                        
                        if title == 'final_image':
                            self.logger.info(f"⚠️  选择final_image PreviewImage节点: {node_id}")
                            return node_id
            
            # 🔥 最后兜底：选择ID最大的节点
            if len(image_output_nodes) == 1:
                self.logger.info(f"只有一个图片输出节点: {image_output_nodes[0]}")
                return image_output_nodes[0]
            
            try:
                numeric_nodes = [(int(node_id), node_id) for node_id in image_output_nodes if node_id.isdigit()]
                if numeric_nodes:
                    numeric_nodes.sort(key=lambda x: x[0], reverse=True)
                    final_node_id = numeric_nodes[0][1]
                    self.logger.info(f"⚠️  兜底方案：选择节点ID最大的: {final_node_id}")
                    return final_node_id
            except (ValueError, TypeError):
                pass
            
            # 如果节点ID不是数字，按字符串排序选择最后一个
            image_output_nodes.sort(reverse=True)
            final_node_id = image_output_nodes[0]
            self.logger.info(f"⚠️  兜底方案：按字符串排序选择: {final_node_id}")
            return final_node_id
            
        except Exception as e:
            self.logger.error(f"识别最终输出节点失败: {e}")
            return None
    
    async def _download_image(self, session: aiohttp.ClientSession, filename: str, subfolder: str = "", image_type: str = "") -> Optional[bytes]:
        """下载图片"""
        try:
            # 构建下载URL
            url_params = [f"filename={filename}"]
            if subfolder:
                url_params.append(f"subfolder={subfolder}")
            if image_type:
                url_params.append(f"type={image_type}")
            
            url = f"{self.api_url}/view?" + "&".join(url_params)
            
            async with session.get(url) as response:
                if response.status == 200:
                    image_data = await response.read()
                    self.logger.info(f"下载图片成功: {filename}, 大小: {len(image_data)} bytes")
                    return image_data
                else:
                    self.logger.error(f"下载图片失败: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"下载图片异常: {e}")
            return None
    
    async def create_image_message(self, image_data: bytes) -> Optional[llm_entities.Message]:
        """创建图片消息"""
        try:
            # 将图片数据转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 创建消息
            message = llm_entities.Message(
                role='assistant',
                content=f"![Generated Image](data:image/png;base64,{image_base64})"
            )
            
            return message
            
        except Exception as e:
            self.logger.error(f"创建图片消息失败: {e}")
            return None
    
    def get_flux_statistics(self) -> Dict[str, Any]:
        """获取Flux工作流统计信息"""
        try:
            seed_stats = self.seed_manager.get_seed_statistics()
            lora_stats = self.lora_integration.get_lora_statistics()
            
            # 组合统计信息
            stats = {
                'workflow_type': 'Flux',
                'seed_statistics': seed_stats,
                'lora_statistics': lora_stats,
                'supported_parameters': self.node_mapper.get_supported_parameters(),
                'last_execution': None
            }
            
            # 添加最后执行信息
            if self.last_execution_result:
                stats['last_execution'] = self.last_execution_result.get_generation_info()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取Flux统计失败: {e}")
            return {}
    
    def export_workflow_config(self, file_path: str) -> bool:
        """导出工作流配置"""
        try:
            config = {
                'workflow_type': 'flux',
                'api_url': self.api_url,
                'timeout': self.timeout,
                'workflow_path': self.workflow_path,
                'export_time': time.time(),
                'node_configurations': {
                    node_id: self.node_mapper.get_node_info(node_id)
                    for node_id in ["6", "55", "50", "61", "60"]
                },
                'seed_settings': {
                    'max_history': self.seed_manager.max_history,
                    'storage_file': self.seed_manager.storage_file
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"导出Flux工作流配置: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出工作流配置失败: {e}")
            return False
    
    def validate_configuration(self) -> Dict[str, Any]:
        """验证Flux配置"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'checks': {}
        }
        
        try:
            # 检查API连接
            validation_result['checks']['api_url'] = {
                'value': self.api_url,
                'status': 'unknown'  # 需要实际请求才能确定
            }
            
            # 检查工作流文件
            workflow_files = [
                os.path.join(self.workflow_path, self.node_mapper.default_workflow_file),
                os.path.join(self.workflow_path, self.node_mapper.fallback_workflow_file)
            ]
            
            workflow_exists = any(os.path.exists(f) for f in workflow_files)
            validation_result['checks']['workflow_files'] = {
                'files': workflow_files,
                'exists': workflow_exists
            }
            
            if not workflow_exists:
                validation_result['warnings'].append("没有找到工作流文件，将使用内置模板")
            
            # 检查种子管理器
            validation_result['checks']['seed_manager'] = {
                'storage_file': self.seed_manager.storage_file,
                'history_count': len(self.seed_manager.seed_history)
            }
            
            # 检查LoRA集成
            validation_result['checks']['lora_integration'] = {
                'manager_available': self.lora_integration.lora_manager is not None
            }
            
        except Exception as e:
            validation_result['valid'] = False
            validation_result['errors'].append(f"配置验证异常: {e}")
        
        return validation_result

    async def generate_image(self, user_text: str, params: Dict[str, Any], session_images: Optional[List] = None) -> WorkflowResult:
        result = await self.execute_flux_workflow(user_text, None, session_images=session_images)
        image_bytes = result.image_data if getattr(result, 'image_data', None) else None
        return WorkflowResult(
            success=result.success,
            image_data=image_bytes,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            error_message=getattr(result, 'error_message', None)
        )

    async def submit_workflow(self, workflow_data: Dict[str, Any], session_images: Optional[List] = None) -> WorkflowResult:
        image_data = await self._execute_comfyui_workflow(workflow_data)
        return WorkflowResult(
            success=image_data is not None,
            image_data=image_data,
            metadata={},
            error_message=None if image_data else "图片生成失败"
        )

    async def execute_workflow(self, prompt: str, query: Any, session_images: Optional[List] = None) -> WorkflowResult:
        result = await self.execute_flux_workflow(prompt, query, session_images=session_images)
        image_bytes = result.image_data if getattr(result, 'image_data', None) else None
        return WorkflowResult(
            success=result.success,
            image_data=image_bytes,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            error_message=getattr(result, 'error_message', None)
        )

    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        return WorkflowResult(
            success=True, 
            image_data=None,
            metadata={"task_id": task_id}, 
            error_message=None
        )


    def _select_workflow_by_image_type(self, session_images: Optional[List]) -> str:
        """
        根据图片类型智能选择工作流模板
        
        Args:
            session_images: 会话图片列表（SessionImage对象）
            
        Returns:
            str: 工作流文件名
        """
        try:
            # 无图片时使用默认工作流
            if not session_images or len(session_images) == 0:
                self.logger.info("无图片输入，选择默认工作流")
                return "flux_default.json"
            
            # 分析图片类型
            control_images = [img for img in session_images if img.is_control_image()]
            reference_images = [img for img in session_images if img.is_reference_image()]
            mixed_images = [img for img in session_images if img.is_mixed_image()]
            
            self.logger.info(f"图片类型分析: 控制图{len(control_images)}张, 参考图{len(reference_images)}张, 混合图{len(mixed_images)}张")
            
            # 根据图片类型选择工作流
            if mixed_images:
                # 有混合图，使用ControlNet+Redux混合工作流
                self.logger.info("检测到混合图，选择ControlNet+Redux混合工作流")
                return "flux_controlnet_redux.json"
            elif control_images:
                # 只有控制图，使用ControlNet工作流
                self.logger.info("检测到控制图，选择ControlNet工作流")
                return "flux_controlnet.json"
            elif reference_images:
                # 只有参考图，使用Redux工作流
                self.logger.info("检测到参考图，选择Redux工作流")
                return "flux_redux.json"
            else:
                # 🔥 用户上传了图片，必然是想要做图生图，默认选择ControlNet工作流
                self.logger.info("用户上传了图片，默认选择ControlNet工作流进行图生图")
                return "flux_controlnet.json"
                
        except Exception as e:
            self.logger.error(f"选择工作流时出错: {e}")
            return "flux_default.json"

    def _apply_images_by_type(self, workflow_data: Dict[str, Any], session_images: List) -> Dict[str, Any]:
        """
        根据图片类型将图片分配到不同节点
        Args:
            workflow_data: 工作流数据
            session_images: 会话图片列表（SessionImage对象）
        Returns:
            修改后的工作流数据
        """
        try:
            import base64
            # 获取不同类型的图片
            control_images = [img for img in session_images if img.is_control_image()]
            reference_images = [img for img in session_images if img.is_reference_image()]
            mixed_images = [img for img in session_images if img.is_mixed_image()]
            self.logger.info(f"图片类型分布: 控制图{len(control_images)}张, 参考图{len(reference_images)}张, 混合图{len(mixed_images)}张")
            # 查找特定功能的图片输入节点
            controlnet_node_id = None
            redux_node_id = None
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and 'class_type' in node_data and '_meta' in node_data:
                    title = node_data['_meta'].get('title', '')
                    if title == 'controlnet_image_input':
                        controlnet_node_id = node_id
                        self.logger.info(f"找到ControlNet图片输入节点: {node_id}")
                    elif title == 'redux_image_input':
                        redux_node_id = node_id
                        self.logger.info(f"找到Redux图片输入节点: {node_id}")
            # 分配控制图到ControlNet节点
            if control_images and controlnet_node_id:
                control_image = control_images[0]  # 使用第一张控制图
                image_base64 = base64.b64encode(control_image.data).decode('utf-8')
                workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                self.logger.info(f"成功应用控制图到ControlNet节点 {controlnet_node_id}")
            elif control_images and not controlnet_node_id:
                self.logger.warning("有控制图但未找到ControlNet节点")
            # 分配参考图到Redux节点
            if reference_images and redux_node_id:
                reference_image = reference_images[0]  # 使用第一张参考图
                image_base64 = base64.b64encode(reference_image.data).decode('utf-8')
                workflow_data[redux_node_id]["inputs"]["base64_data"] = image_base64
                self.logger.info(f"成功应用参考图到Redux节点 {redux_node_id}")
            elif reference_images and not redux_node_id:
                self.logger.warning("有参考图但未找到Redux节点")
            # 处理混合图片 - 如果有两个节点都可用，同时分配
            if mixed_images:
                mixed_image = mixed_images[0]
                image_base64 = base64.b64encode(mixed_image.data).decode('utf-8')
                # 如果没有专门的控制图，混合图可以作为控制图
                if not control_images and controlnet_node_id:
                    workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"成功应用混合图作为控制图到ControlNet节点 {controlnet_node_id}")
                # 如果没有专门的参考图，混合图可以作为参考图
                if not reference_images and redux_node_id:
                    workflow_data[redux_node_id]["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"成功应用混合图作为参考图到Redux节点 {redux_node_id}")
            
            # 🔥 处理未明确类型的图片 - 默认作为控制图
            untyped_images = [img for img in session_images if not (img.is_control_image() or img.is_reference_image() or img.is_mixed_image())]
            if untyped_images and controlnet_node_id and not control_images and not mixed_images:
                # 如果有未明确类型的图片，且没有专门的控制图，将第一张图片作为控制图
                untyped_image = untyped_images[0]
                image_base64 = base64.b64encode(untyped_image.data).decode('utf-8')
                workflow_data[controlnet_node_id]["inputs"]["base64_data"] = image_base64
                self.logger.info(f"成功应用未明确类型的图片作为控制图到ControlNet节点 {controlnet_node_id}")
            
            # 统计分配结果
            assigned_count = 0
            if control_images and controlnet_node_id:
                assigned_count += 1
            if reference_images and redux_node_id:
                assigned_count += 1
            if mixed_images and (controlnet_node_id or redux_node_id):
                assigned_count += 1
            if untyped_images and controlnet_node_id and not control_images and not mixed_images:
                assigned_count += 1
            if assigned_count == 0:
                self.logger.warning("未分配任何图片到工作流节点")
            else:
                self.logger.info(f"总共分配了 {assigned_count} 张图片到工作流节点")
            return workflow_data
        except Exception as e:
            self.logger.error(f"根据类型分配图片失败: {e}")
            return workflow_data

    async def _analyze_parameters_with_unified_system(self, user_text: str, query) -> FluxParameters:
        """使用统一路由系统分析参数"""
        try:
            # 导入统一路由系统
            from ...core.workflow.unified_routing_system import UnifiedRoutingSystem

            # 创建统一路由系统实例
            unified_router = UnifiedRoutingSystem(self.ap)

            # 调用参数分析
            param_result = await unified_router.analyze_parameters(user_text, query)

            if param_result.success and param_result.parameters:
                # 转换为FluxParameters
                params = FluxParameters()

                # 映射参数
                if 'prompt' in param_result.parameters:
                    params.prompt = param_result.parameters['prompt']
                if 'width' in param_result.parameters:
                    params.width = param_result.parameters['width']
                if 'height' in param_result.parameters:
                    params.height = param_result.parameters['height']
                if 'steps' in param_result.parameters:
                    params.steps = param_result.parameters['steps']
                if 'guidance' in param_result.parameters:
                    params.guidance = param_result.parameters['guidance']
                if 'seed_instruction' in param_result.parameters:
                    seed_instruction = param_result.parameters['seed_instruction']
                    if seed_instruction == 'random':
                        params.seed_instruction = SeedInstruction.RANDOM
                    elif seed_instruction == 'use_last':
                        params.seed_instruction = SeedInstruction.USE_LAST
                    elif seed_instruction.startswith('specific:'):
                        params.seed_instruction = SeedInstruction.SPECIFIC
                        try:
                            params.seed = int(seed_instruction.split(':')[1])
                        except:
                            params.seed_instruction = SeedInstruction.RANDOM

                self.logger.info(f"统一路由系统参数分析成功，置信度: {param_result.confidence}")
                return params
            else:
                self.logger.warning(f"统一路由系统参数分析失败: {param_result.error_message}")

        except Exception as e:
            self.logger.error(f"调用统一路由系统失败: {e}")

        # 降级到默认参数
        params = FluxParameters()
        params.prompt = user_text  # 至少使用用户输入作为提示词
        self.logger.info("使用默认参数")
        return params

    async def close(self) -> None:
        # 预留资源释放接口
        pass


# 全局单例
flux_workflow_manager: Optional[FluxWorkflowManager] = None

def get_flux_workflow_manager(ap: Optional[app.Application] = None, pipeline_config: dict = {}) -> FluxWorkflowManager:
    """获取Flux工作流管理器单例"""
    global flux_workflow_manager
    if flux_workflow_manager is None:
        flux_workflow_manager = FluxWorkflowManager(ap, pipeline_config)
    return flux_workflow_manager