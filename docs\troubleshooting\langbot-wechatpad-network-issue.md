# LangBot与WeChatPad网络连接问题排障指南

## 📋 问题描述

**症状**：
- 用户发送微信消息后，LangBot无响应
- ComfyUI图片生成功能不工作
- LangBot日志显示HTTP连接失败错误

**错误信息**：
```
http请求失败, url=http://127.0.0.1:1239//login/GetLoginStatus?key=xxx
HTTPConnectionPool(host='127.0.0.1', port=1239): Max retries exceeded
[Errno 111] Connection refused
```

## 🔍 问题根因分析

### 网络架构问题
- **LangBot容器**：运行在Docker默认bridge网络 (172.17.0.x)
- **WeChatPadPro容器**：运行在独立的网络 (deploy_wechatpadpro_net)
- **核心问题**：Docker网络隔离导致容器间无法直接通信

### 错误诊断思路
❌ **错误方向**：修改IP地址配置
✅ **正确方向**：解决Docker网络连通性

## 🛠️ 解决方案

### 方案选择：使用Host网络模式

**原理**：让LangBot容器直接使用宿主机网络栈，绕过Docker网络隔离

### 具体配置修改

#### 1. 修改docker-compose.yaml

**修改前**：
```yaml
services:
  langbot:
    image: docker.langbot.app/langbot-public/rockchin/langbot:latest
    container_name: langbot
    ports:
      - 5300:5300
      - 2280-2290:2280-2290
    # ... 其他配置
```

**修改后**：
```yaml
services:
  langbot:
    image: docker.langbot.app/langbot-public/rockchin/langbot:latest
    container_name: langbot
    network_mode: host  # 关键修改
    # 移除ports配置，因为host模式下不需要
    # ... 其他配置
```

#### 2. 确认wechatpad配置

保持原有配置不变：
```yaml
# config/wechatpad.yaml
wechatpad:
  wechatpad_url: "http://*************:1239"  # 宿主机IP
  host: "0.0.0.0"
  port: 2285
  token: "7b8db3e8-0df2-43f4-abd0-ad96e2c85243"
```

### 部署命令

```bash
# 停止并删除旧容器
docker stop langbot && docker rm langbot

# 使用host网络模式启动
docker run -d \
  --name langbot \
  --restart on-failure \
  --network host \
  -e TZ=Asia/Shanghai \
  -v ./data:/app/data \
  -v ./plugins:/app/plugins \
  -v ./pkg:/app/pkg \
  -v ./config:/app/config \
  -v ./workflows:/app/workflows \
  -v ./templates:/app/templates \
  -v ./res:/app/res \
  docker.langbot.app/langbot-public/rockchin/langbot:latest
```

## ✅ 验证方法

### 1. 检查连接状态
```bash
docker logs langbot --tail 20
```

**成功标志**：
```
WebSocket connected successfully!
'nickName': {'str': 'username'}
```

### 2. 功能测试
- 发送微信消息测试Bot响应
- 发送图片生成指令测试ComfyUI

## 📚 技术要点总结

### Docker网络模式对比

| 网络模式 | 特点 | 适用场景 | 限制 |
|---------|------|----------|------|
| bridge | 容器间隔离 | 标准服务部署 | 需要端口映射 |
| host | 共享宿主机网络 | 需要访问宿主机服务 | 失去网络隔离 |

### 排障经验

#### ✅ 正确的排障思路
1. **确认网络拓扑**：理解各容器的网络配置
2. **测试连通性**：验证实际的网络访问能力
3. **选择合适方案**：根据架构选择最优解决方案

#### ❌ 常见错误思路
1. 盲目修改IP地址配置
2. 忽视Docker网络架构
3. 不进行连通性测试

## 🚨 注意事项

### 使用Host网络模式的影响

**优势**：
- 完全解决容器间网络隔离问题
- 性能最优，无网络转发开销
- 配置简单，无需复杂的网络设置

**风险**：
- 失去Docker网络隔离保护
- 端口冲突风险增加
- 安全性相对降低

### 安全建议

1. **端口管理**：确保宿主机防火墙正确配置
2. **访问控制**：限制不必要的服务暴露
3. **监控**：加强对网络流量的监控

## 🔄 替代方案

如果不能使用host网络模式，可考虑：

### 方案1：创建自定义网络
```bash
# 创建共享网络
docker network create shared_network

# 将两个容器加入同一网络
docker run --network shared_network ...
```

### 方案2：使用容器名解析
```yaml
# 修改配置为容器名
wechatpad_url: "http://wechatpadpro:1239"
```

## 📖 相关文档

- [Docker网络配置官方文档](https://docs.docker.com/network/)
- [LangBot部署指南](https://docs.langbot.app/)
- [WeChatPad API文档](https://github.com/weshmily/WeChatPad)

## 🎯 经验总结

1. **网络问题优先考虑连通性**，而非配置语法
2. **容器化环境要理解网络拓扑**，避免盲目调试
3. **Host网络模式是简单有效的解决方案**，但要权衡安全性
4. **完整的问题复现和验证流程很重要**

---

**文档创建时间**：2025-06-21  
**适用版本**：LangBot v4.0.6, WeChatPadPro v0.11  
**维护者**：AI Assistant  
**最后更新**：2025-06-21 