"""
统一路由Mixin V2
基于新的统一路由系统，为现有的Runner提供智能工作流路由功能
"""

import logging
from typing import Optional, List, Dict, Any

from ...core.workflow.unified_routing_system import (
    get_unified_router, UnifiedRoutingResult, RoutingLevel, RoutingConfidence
)
from ...core.session.models import WorkflowType
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...core import entities as core_entities


class UnifiedRoutingMixinV2:
    """统一路由Mixin V2，提供智能工作流路由功能"""
    
    def __init__(self, *args, **kwargs):
        # 不调用super()，因为这是一个mixin类
        self.unified_router = get_unified_router(getattr(self, 'ap', None))
        self.logger = logging.getLogger(__name__)
    
    async def route_workflow_intelligently(
        self, 
        user_text: str, 
        query: Any,
        attached_images: Optional[List] = None
    ) -> UnifiedRoutingResult:
        """
        智能路由工作流，使用新的统一路由系统
        
        Args:
            user_text: 用户输入文本
            query: 查询对象
            attached_images: 附加的图片列表
            
        Returns:
            UnifiedRoutingResult: 路由结果
        """
        # 计算图片信息
        has_images = bool(attached_images and len(attached_images) > 0)
        image_count = len(attached_images) if attached_images else 0
        
        # 🔥 添加调试日志
        self.logger.info(f"🔍 UnifiedRoutingMixinV2.route_workflow_intelligently 开始...")
        self.logger.info(f"   用户文本: {user_text}")
        self.logger.info(f"   是否有图片: {has_images}")
        self.logger.info(f"   图片数量: {image_count}")
        self.logger.info(f"   unified_router对象: {self.unified_router}")
        
        # 调用统一路由器
        try:
            self.logger.info(f"🔄 正在调用 unified_router.route_unified...")
            routing_result = await self.unified_router.route_unified(
                user_text=user_text,
                has_images=has_images,
                image_count=image_count,
                query=query
            )
            self.logger.info(f"✅ route_unified调用完成，返回结果: {routing_result}")
        except Exception as e:
            self.logger.error(f"❌ route_unified调用异常: {e}")
            import traceback
            self.logger.error(f"❌ 异常详情: {traceback.format_exc()}")
            routing_result = None
        
        # 检查路由结果是否为空
        if routing_result is None:
            # 返回默认的AIGEN路由结果
            from ...core.session.models import WorkflowType
            from ...core.workflow.unified_routing_system import UnifiedRoutingResult, RoutingLevel, RoutingConfidence, WorkflowSubType
            
            default_result = UnifiedRoutingResult(
                routing_level=RoutingLevel.LEVEL_1,
                workflow_type=WorkflowType.AIGEN,
                workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
                confidence=RoutingConfidence.LOW,
                reasoning="路由失败，使用默认AIGEN工作流",
                processing_time_ms=0.0,
                fallback_used=True
            )
            
            self.logger.warning("⚠️ 路由返回None，使用默认AIGEN工作流")
            return default_result
        
        # 记录路由决策
        self.logger.info(f"🎯 统一路由决策: {routing_result.workflow_type.value}")
        self.logger.info(f"   路由级别: {routing_result.routing_level.value}")
        self.logger.info(f"   置信度: {routing_result.confidence.value}")
        self.logger.info(f"   推理: {routing_result.reasoning}")
        self.logger.info(f"   处理时间: {routing_result.processing_time_ms:.1f}ms")
        
        if routing_result.fallback_used:
            self.logger.warning(f"   使用了后备路由")
        
        if routing_result.needs_clarification:
            self.logger.warning(f"   需要用户确认: {routing_result.clarification_question}")
        
        return routing_result
    
    def should_use_workflow_type(
        self, 
        user_text: str, 
        expected_workflow: WorkflowType,
        attached_images: Optional[List] = None
    ) -> bool:
        """
        判断是否应该使用指定的工作流类型（同步版本，用于向后兼容）
        
        Args:
            user_text: 用户输入文本
            expected_workflow: 期望的工作流类型
            attached_images: 附加的图片列表
            
        Returns:
            bool: 是否应该使用该工作流
        """
        # 快速启发式判断（无需LLM调用）
        has_images = bool(attached_images and len(attached_images) > 0)
        image_count = len(attached_images) if attached_images else 0
        
        # 使用第一级路由进行快速判断
        workflow_type = self.unified_router._route_level_1(user_text)
        
        # 检查是否匹配期望的工作流
        is_match = workflow_type == expected_workflow
        
        # 如果有明确的触发词匹配，直接返回结果
        if workflow_type is not None:
            return is_match
        
        # 如果没有触发词，根据图片数量进行简单判断
        if expected_workflow == WorkflowType.AIGEN:
            # AIGEN工作流可以处理有图片或无图片的情况
            return True
        elif expected_workflow == WorkflowType.KONTEXT:
            # KONTEXT工作流需要图片
            return has_images
        elif expected_workflow == WorkflowType.KONTEXT_API:
            # KONTEXT_API工作流需要图片
            return has_images
        
        return False
    
    def get_routing_explanation(self, routing_result: UnifiedRoutingResult) -> str:
        """
        获取路由决策的解释说明
        
        Args:
            routing_result: 路由结果
            
        Returns:
            str: 解释说明
        """
        explanation = f"🎯 **路由决策**: {self.get_workflow_description(routing_result.workflow_type)}\n\n"
        explanation += f"**路由级别**: {'关键词触发' if routing_result.routing_level == RoutingLevel.LEVEL_1 else 'LLM智能分析'}\n"
        explanation += f"**置信度**: {routing_result.confidence.value}\n"
        explanation += f"**选择原因**: {routing_result.reasoning}\n"
        explanation += f"**处理时间**: {routing_result.processing_time_ms:.1f}ms\n"
        
        if routing_result.fallback_used:
            explanation += f"**后备路由**: 是\n"
        
        # 新的路由系统没有alternative_workflows属性，跳过这部分
        pass
        
        if routing_result.needs_clarification:
            explanation += f"**需要确认**: {routing_result.clarification_question}\n"
        
        if routing_result.suggested_prompt:
            explanation += f"**建议提示词**: {routing_result.suggested_prompt}\n"
        
        return explanation
    
    def get_workflow_description(self, workflow_type: WorkflowType) -> str:
        """获取工作流描述"""
        descriptions = {
            WorkflowType.AIGEN: "🎨 **文生图工作流** - 根据文本描述生成图片，支持参考图和控制图",
            WorkflowType.KONTEXT: "✏️ **图生图工作流** - 编辑和修改现有图片，保持原图结构",
            WorkflowType.KONTEXT_API: "☁️ **远程API工作流** - 云端图像处理，功能丰富"
        }
        return descriptions.get(workflow_type, "❓ 未知工作流")
    
    def format_workflow_options(self) -> str:
        """
        格式化工作流选项说明
        
        Returns:
            str: 格式化的工作流选项
        """
        options = []
        
        for workflow_type in [WorkflowType.AIGEN, WorkflowType.KONTEXT, WorkflowType.KONTEXT_API]:
            description = self.get_workflow_description(workflow_type)
            options.append(description)
        
        return "\n".join(options)
    
    def suggest_workflow_command(self, user_text: str, routing_result: UnifiedRoutingResult) -> str:
        """
        建议工作流命令
        
        Args:
            user_text: 用户输入文本
            routing_result: 路由结果
            
        Returns:
            str: 建议的命令
        """
        workflow_prefix = routing_result.workflow_type.value
        
        # 如果有建议的提示词，使用它
        if routing_result.suggested_prompt:
            return f"{workflow_prefix} {routing_result.suggested_prompt}"
        else:
            return f"{workflow_prefix} {user_text}"
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """
        获取路由统计信息
        
        Returns:
            Dict[str, Any]: 路由统计信息
        """
        return self.unified_router.get_routing_stats()
    
    def is_high_confidence_route(self, routing_result: UnifiedRoutingResult) -> bool:
        """
        判断是否是高置信度路由
        
        Args:
            routing_result: 路由结果
            
        Returns:
            bool: 是否是高置信度
        """
        return routing_result.confidence in [RoutingConfidence.HIGH, RoutingConfidence.MEDIUM]
    
    def should_ask_for_confirmation(self, routing_result: UnifiedRoutingResult) -> bool:
        """
        判断是否需要用户确认
        
        Args:
            routing_result: 路由结果
            
        Returns:
            bool: 是否需要确认
        """
        return (
            routing_result.needs_clarification or 
            routing_result.confidence == RoutingConfidence.LOW or
            routing_result.confidence == RoutingConfidence.UNKNOWN
        ) 