# Ubuntu系统上安装私有LangBot仓库并连接微信指南

本文档提供在Ubuntu系统上从私有仓库安装LangBot、配置微信连接以及集成ComfyUI的完整步骤。

## 1. 安装LangBot

### 1.1 安装基础依赖

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y git python3 python3-pip python3-venv
```

### 1.2 克隆并安装私有LangBot仓库

```bash
# 克隆私有仓库（需要有访问权限）
git clone https://github.com/YOUR_USERNAME/YOUR_PRIVATE_LANGBOT_REPO.git langbot
cd langbot

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -e .
```

> 注意：请确保您有权限访问私有仓库，并且已经配置了GitHub凭证。如果使用SSH克隆，请使用：
> ```bash
> <NAME_EMAIL>:YOUR_USERNAME/YOUR_PRIVATE_LANGBOT_REPO.git langbot
> ```

## 2. 安装GeWeChat服务

在Ubuntu系统上，推荐使用GeWeChat适配器连接微信，因为它不需要图形界面。

### 2.1 安装Docker

```bash
sudo apt install -y docker.io docker-compose
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER
# 重新登录以使用户组更改生效
```

### 2.2 部署GeWeChat服务

```bash
# 创建目录
mkdir -p ~/gewechat
cd ~/gewechat

# 创建docker-compose.yml文件
cat > docker-compose.yml << 'EOF'
version: '3'
services:
  gewechat:
    image: gewechat/gewechat:latest
    container_name: gewechat
    restart: always
    ports:
      - "19088:19088"
    volumes:
      - ./data:/app/data
EOF

# 启动服务
docker-compose up -d
```

## 3. 配置LangBot

### 3.1 创建配置目录和文件

回到LangBot目录，创建必要的配置目录：

```bash
# 创建配置目录
mkdir -p config
mkdir -p data
mkdir -p workflows
mkdir -p plugins
```

### 3.2 创建配置文件

**config/platform.yaml**:
```yaml
# 平台配置
platform:
  # 启用的平台适配器
  enabled:
    - gewechat
```

**config/gewechat.yaml**:
```yaml
# GeWeChat配置
gewechat:
  # GeWeChat服务器地址
  gewechat_url: "http://127.0.0.1:19088"
  # 回调地址，用于接收消息
  callback_url: "http://127.0.0.1:2285/gewechat/callback"
  # 监听地址
  host: "0.0.0.0"
  # 监听端口
  port: 2285
  # 令牌，需与GeWeChat服务器配置一致
  token: "your_token_here"
  # 应用ID，首次登录后会自动填充
  app_id: ""
```

**config/llm_config.yaml**:
```yaml
# 深度求索配置
deepseek:
  api_key: "您的API_KEY"
  endpoint: "https://api.deepseek.com/v1"
  model: "deepseek-chat"

# ComfyUI集成配置
comfyui:
  local_endpoint: "http://localhost:8188"
  workflow_template: "微信图片生成"
  timeout: 120
```

**config/service.yaml**:
```yaml
# 服务配置
server:
  admin_port: 5300
  api_port: 5002

# ComfyUI集成  
comfyui:
  enabled: true
  endpoint: "http://localhost:8188"
  workflows:
    image_generation: "微信图片生成.json"
```

## 4. 配置微信连接

### 4.1 访问GeWeChat管理界面

在浏览器中访问 `http://your_server_ip:19088` 进入GeWeChat管理界面。

### 4.2 配置GeWeChat

1. 在GeWeChat管理界面中，创建一个新的应用
2. 设置回调URL为 `http://your_server_ip:2285/gewechat/callback`
3. 设置Token与LangBot配置中的token一致
4. 记下生成的AppID，填入LangBot的gewechat.yaml配置中

### 4.3 登录微信

1. 在GeWeChat管理界面中，点击"扫码登录"
2. 使用手机微信扫描二维码登录
3. 登录成功后，GeWeChat会显示"已登录"状态

## 5. 启动LangBot

### 5.1 创建启动脚本

创建文件 `start_langbot.sh`:
```bash
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python main.py
```

添加执行权限:
```bash
chmod +x start_langbot.sh
```

### 5.2 创建系统服务（推荐）

```bash
# 创建服务文件
sudo nano /etc/systemd/system/langbot.service
```

添加以下内容（请替换路径为实际路径）：
```
[Unit]
Description=LangBot Service
After=network.target

[Service]
Type=simple
User=YOUR_USERNAME
WorkingDirectory=/path/to/your/private/langbot
ExecStart=/path/to/your/private/langbot/start_langbot.sh
Restart=always
RestartSec=10s

[Install]
WantedBy=multi-user.target
```

启用并启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable langbot.service
sudo systemctl start langbot.service
```

## 6. 集成ComfyUI

### 6.1 创建ComfyUI工作流

1. 访问ComfyUI界面：`http://your_server_ip:8188`
2. 创建一个图像生成工作流
3. 保存工作流为JSON文件，例如`微信图片生成.json`
4. 将工作流文件放入LangBot的workflows目录

### 6.2 测试微信与ComfyUI集成

向您登录的微信账号发送消息，例如："帮我生成一张猫咪的图片"。LangBot应该会处理这条消息，调用LLM分析内容，然后通过ComfyUI生成图像，并将生成的图像发送回微信。

## 7. 私有仓库维护

由于您使用的是私有仓库，以下是一些维护建议：

### 7.1 保持与上游同步

如果您的私有仓库是从原始LangBot仓库fork的，可以定期同步上游更新：

```bash
# 添加上游仓库
git remote add upstream https://github.com/RockChinQ/LangBot.git

# 获取上游更新
git fetch upstream

# 合并上游更新（假设主分支是main）
git checkout main
git merge upstream/main

# 处理可能的冲突并提交
git push origin main
```

### 7.2 自定义开发

对于私有仓库的自定义开发，建议：

1. 创建特性分支进行开发
2. 使用清晰的提交信息
3. 保留原始功能的同时添加自定义功能

## 8. 故障排除

### 8.1 检查服务状态

```bash
# 检查LangBot服务状态
sudo systemctl status langbot.service

# 检查GeWeChat服务状态
docker ps | grep gewechat
```

### 8.2 查看日志

```bash
# 查看LangBot日志
sudo journalctl -fu langbot.service

# 查看GeWeChat日志
docker logs -f gewechat
```

### 8.3 常见问题解决

1. **微信无法登录**：
   - 确保GeWeChat服务正常运行
   - 检查网络连接是否正常
   - 尝试重启GeWeChat服务：`docker restart gewechat`

2. **LangBot无法连接GeWeChat**：
   - 检查配置中的URL和端口是否正确
   - 确保防火墙未阻止连接
   - 验证Token是否一致

3. **ComfyUI集成问题**：
   - 确保ComfyUI服务正常运行
   - 检查工作流文件是否正确
   - 验证LangBot能否访问ComfyUI API

4. **私有仓库特定问题**：
   - 检查是否有自定义代码导致的兼容性问题
   - 确保所有依赖项都已正确安装
   - 查看是否有配置文件格式变更

## 9. 安全建议

1. **防火墙配置**：只开放必要的端口
   ```bash
   sudo ufw allow 5300/tcp  # LangBot管理界面
   sudo ufw allow 2285/tcp  # GeWeChat回调
   sudo ufw allow 19088/tcp # GeWeChat管理界面
   ```

2. **使用HTTPS**：在生产环境中，建议配置HTTPS以保护通信安全

3. **定期备份**：定期备份配置文件和重要数据
   ```bash
   # 创建备份脚本
   mkdir -p ~/backups
   cp -r /path/to/your/private/langbot/config ~/backups/config_$(date +%Y%m%d)
   cp -r ~/gewechat/data ~/backups/gewechat_data_$(date +%Y%m%d)
   ```

4. **私有仓库安全**：
   - 限制仓库访问权限
   - 不要在代码中硬编码API密钥和敏感信息
   - 考虑使用环境变量或加密配置文件存储敏感信息

## 10. 自动更新（可选）

如果您希望自动从私有仓库更新LangBot，可以创建一个自动更新脚本：

```bash
#!/bin/bash
# 自动更新脚本 - 保存为 update_langbot.sh

cd /path/to/your/private/langbot

# 备份当前配置
cp -r config /tmp/langbot_config_backup

# 获取最新代码
git pull

# 重启服务
sudo systemctl restart langbot.service

echo "LangBot已更新并重启，更新时间: $(date)"
```

添加执行权限并设置定时任务：
```bash
chmod +x update_langbot.sh

# 添加到crontab，每周日凌晨3点更新
(crontab -l 2>/dev/null; echo "0 3 * * 0 /path/to/your/private/langbot/update_langbot.sh >> /path/to/your/private/langbot/update.log 2>&1") | crontab -
```

## 11. 资源链接

- [LangBot官方文档](https://docs.langbot.app/)
- [GeWeChat项目](https://github.com/gewechat/gewechat)
- [ComfyUI项目](https://github.com/comfyanonymous/ComfyUI)

---

通过以上步骤，您应该能够在Ubuntu系统上成功安装私有LangBot仓库，并将其与微信和ComfyUI集成。如有任何问题，请参考官方文档或检查您的私有仓库文档。