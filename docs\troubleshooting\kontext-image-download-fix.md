# Kontext图片下载修复说明

## 问题描述

在Kontext工作流中，系统返回的图片不正确，似乎只返回了用户上传的原图，而不是经过Kontext处理后的图片。

## 问题分析

### 工作流结构分析

通过检查 `workflows/kontext_1images_input.json` 工作流文件，发现以下节点结构：

1. **节点102** (`easy loadImageBase64`) - 输入节点，用于加载用户上传的图片
2. **节点83** (`FluxKontextProImageNode`) - Kontext处理节点，生成处理后的图片
3. **节点90** (`PreviewImage`) - 预览节点，显示处理后的图片

### 问题根源

原代码在下载图片时，简单地选择第一个有图片输出的节点进行下载：

```python
# 原代码：下载第一个有图片的节点
for node_id, node_output in outputs.items():
    if 'images' in node_output:
        images = node_output['images']
        if images and len(images) > 0:
            # 下载第一张图片
            filename = images[0]['filename']
            # ...
```

这导致系统下载了节点102（输入节点）的图片，而不是节点90（输出节点）的图片。

## 修复方案

### 1. 优先节点选择策略

修改图片下载逻辑，优先选择正确的输出节点：

```python
# 修复后：优先选择正确的输出节点
preferred_output_nodes = ['90', '83']  # 优先选择这些节点
fallback_nodes = []

# 先收集所有有图片输出的节点
for node_id, node_output in outputs.items():
    if 'images' in node_output:
        images = node_output['images']
        if images and len(images) > 0:
            if node_id in preferred_output_nodes:
                # 优先节点，直接下载
                # ...
            else:
                # 备用节点，记录但不立即下载
                fallback_nodes.append((node_id, node_output))

# 如果没有找到优先节点，尝试备用节点
for node_id, node_output in fallback_nodes:
    # ...
```

### 2. 节点优先级说明

- **节点90** (`PreviewImage`) - 最高优先级，这是标准的预览输出节点
- **节点83** (`FluxKontextProImageNode`) - 次优先级，这是Kontext处理节点
- **其他节点** - 备用选择，包括输入节点等

### 3. 日志增强

添加详细的日志记录，便于调试：

```python
self.ap.logger.info(f"从优先输出节点{node_id}下载Kontext图片: {filename} (类型: {image_type})")
self.ap.logger.info(f"从备用节点{node_id}下载Kontext图片: {filename} (类型: {image_type})")
```

## 修复验证

### 测试步骤

1. **上传图片**：向Kontext会话上传一张图片
2. **发送指令**：发送 "go" 或具体的修改指令
3. **检查日志**：确认下载的是正确的输出节点
4. **验证结果**：确认返回的是处理后的图片，不是原图

### 预期日志输出

修复后应该看到类似以下的日志：

```
[INFO] Kontext工作流输出节点: ['102', '90']
[INFO] 从优先输出节点90下载Kontext图片: easyPreview_temp_xxx.png (类型: temp)
[INFO] 成功获取Kontext图片，用时: 12.0s
```

而不是：

```
[INFO] 从节点102下载Kontext图片: ComfyUI_xxx.png (类型: output)
```

## 技术细节

### 节点类型说明

- **输入节点**：通常以 `loadImage`、`easy loadImageBase64` 等开头
- **处理节点**：通常以 `FluxKontextProImageNode`、`KSampler` 等开头
- **输出节点**：通常以 `PreviewImage`、`SaveImage` 等开头

### 图片类型说明

- **temp**：临时图片，通常用于预览
- **output**：输出图片，通常用于保存
- **input**：输入图片，用户上传的原始图片

## 总结

通过实施优先节点选择策略，确保Kontext工作流返回的是经过处理后的图片，而不是用户上传的原图。这个修复解决了Kontext功能的核心问题，使其能够正确显示处理结果。 