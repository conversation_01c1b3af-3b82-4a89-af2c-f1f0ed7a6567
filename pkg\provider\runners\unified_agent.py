"""
统一智能 Agent

整合所有工作流线路的统一接口，提供智能路由和负载均衡
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from pkg.core.session.manager import SessionManager
from pkg.core.session.models import WorkflowType
from pkg.core.image.processor import ImageProcessor
from pkg.core.intent.analyzer import IntentAnalyzer
from pkg.core.workflow.unified_routing_system import get_unified_router
from pkg.core.message.processor import MessageProcessor

# 延迟导入以避免循环导入
# from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
# from pkg.workers.kontext import LocalKontextWorkflowManager
# from pkg.workers.kontext_api.kontext_api_workflow_manager import KontextAPIManager

from pkg.workers.shared.shared_lora_manager import SharedLoraManager
from pkg.workers.shared.shared_comfyui_client import SharedComfyUIClient, ComfyUIConnection
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult


class UnifiedAgent:
    """统一智能 Agent"""
    
    def __init__(self, config: dict, ap: Optional[Any] = None, pipeline_config: Optional[dict] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.ap = ap
        self.pipeline_config = pipeline_config
        self.session_manager = SessionManager()
        self.image_processor = ImageProcessor()
        self.intent_analyzer = IntentAnalyzer()
        self.unified_router = get_unified_router()
        self.message_processor = MessageProcessor()
        self._init_workflow_managers()
        self._init_shared_modules()
        
    def _init_workflow_managers(self):
        """初始化工作流管理器"""
        # Local Flux 工作流
        if self.config.get("enable_flux", True):
            try:
                from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
                ap = getattr(self, 'ap', None)
                pipeline_config = getattr(self, 'pipeline_config', {})
                self.flux_manager = FluxWorkflowManager(ap, pipeline_config)
            except ImportError:
                self.logger.warning("Flux工作流管理器导入失败，跳过初始化")
                self.flux_manager = None
        else:
            self.flux_manager = None
            
        # Local Kontext 工作流
        if self.config.get("enable_kontext", True):
            try:
                from pkg.workers.kontext.local_kontext_workflow_manager import LocalKontextWorkflowManager
                self.kontext_manager = LocalKontextWorkflowManager()
            except ImportError:
                self.logger.warning("Local Kontext工作流管理器导入失败，跳过初始化")
                self.kontext_manager = None
        else:
            self.kontext_manager = None
            
        # Remote Kontext API 工作流
        if self.config.get("enable_kontext_api", False):
            api_key = self.config.get("comfyui_api_key")
            auth_token = self.config.get("comfyui_auth_token")
            if api_key and auth_token:
                try:
                    from pkg.workers.kontext_api.kontext_api_workflow_manager import KontextAPIManager
                    self.kontext_api_manager = KontextAPIManager(api_key, auth_token)
                except ImportError:
                    self.logger.warning("Remote Kontext API工作流管理器导入失败，跳过初始化")
                    self.kontext_api_manager = None
            else:
                self.logger.warning("Remote Kontext API 未配置，跳过初始化")
                self.kontext_api_manager = None
        else:
            self.kontext_api_manager = None
    
    def _init_shared_modules(self):
        """初始化共享模块"""
        # 共享 LoRA 管理器
        self.lora_manager = SharedLoraManager()
        
        # 共享 ComfyUI 客户端
        comfyui_config = self.config.get("comfyui", {})
        if comfyui_config:
            connection = ComfyUIConnection(
                host=comfyui_config.get("host", "localhost"),
                port=comfyui_config.get("port", 8188),
                protocol=comfyui_config.get("protocol", "http"),
                api_key=comfyui_config.get("api_key"),
                timeout=comfyui_config.get("timeout", 30)
            )
            self.comfyui_client = SharedComfyUIClient(connection)
        else:
            self.comfyui_client = None
    
    async def process_request(
        self, 
        user_id: str,
        user_input: str,
        images: Optional[list] = None,
        **kwargs
    ) -> WorkflowResult:
        """
        处理用户请求
        """
        try:
            # 1. 会话管理
            session = self.session_manager.get_session(user_id)
            if session is None:
                session = self.session_manager.create_session(user_id, WorkflowType.AIGEN)

            # 2. 图片处理
            processed_images = images or []

            # 3. 意图分析
            intent_analysis = self.intent_analyzer.analyze_intent(user_input)

            # 4. 工作流路由
            route_result = await self.unified_router.route_unified(
                user_text=user_input,
                has_images=bool(processed_images),
                image_count=len(processed_images) if processed_images else 0
            )
            workflow_type = "aigen"
            if route_result and route_result.workflow_type:
                workflow_type = route_result.workflow_type.value

            # 5. 执行工作流
            intent = {"user_input": user_input, "parameters": getattr(intent_analysis, 'suggested_params', {})}
            session_dict = {"session_id": getattr(session, 'session_id', None), "user_id": getattr(session, 'user_id', None)}
            result: WorkflowResult = await self._execute_workflow(workflow_type, intent, session_dict, processed_images)

            # 6. 更新会话（直接操作会话对象，无需update_session）
            if session is not None:
                # 示例：将本次推理参数写入会话
                if 'parameters' in intent:
                    session.parameters.update(intent['parameters'])
                if user_input:
                    session.set_prompt(user_input)
                session.update_activity()

            return result
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="unknown",
                error_message=str(e)
            )
    
    async def _execute_workflow(
        self,
        workflow_type: str,
        intent: dict,
        session: dict,
        images: list
    ) -> WorkflowResult:
        """执行指定的工作流"""
        
        if workflow_type == "flux" and self.flux_manager is not None:
            return await self._execute_flux_workflow(intent, session, images)
        elif workflow_type == "kontext" and self.kontext_manager is not None:
            return await self._execute_kontext_workflow(intent, session, images)
        elif workflow_type == "kontext_api" and self.kontext_api_manager is not None:
            return await self._execute_kontext_api_workflow(intent, session, images)
        else:
            # 降级到可用的工作流
            return await self._fallback_workflow(intent, session, images)
    
    async def _execute_flux_workflow(
        self,
        intent: dict,
        session: dict,
        images: list
    ) -> WorkflowResult:
        """执行 Flux 工作流"""
        try:
            user_text = intent.get("user_input", "") if intent else ""
            params = intent.get("parameters", {}) if intent else {}
            if self.flux_manager is not None:
                result: WorkflowResult = await self.flux_manager.generate_image(user_text, params, images)
                return result
            else:
                raise RuntimeError("flux_manager is None")
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="flux",
                error_message=str(e)
            )
    
    async def _execute_kontext_workflow(
        self,
        intent: dict,
        session: dict,
        images: list
    ) -> WorkflowResult:
        """执行 Local Kontext 工作流"""
        try:
            user_text = intent.get("user_input", "") if intent else ""
            params = intent.get("parameters", {}) if intent else {}
            if self.kontext_manager is not None:
                result: WorkflowResult = await self.kontext_manager.generate_image(user_text, params, images)
                return result
            else:
                raise RuntimeError("kontext_manager is None")
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="kontext",
                error_message=str(e)
            )
    
    async def _execute_kontext_api_workflow(
        self,
        intent: dict,
        session: dict,
        images: list
    ) -> WorkflowResult:
        """执行 Remote Kontext API 工作流"""
        try:
            params = intent.get("parameters", {}) if intent else {}
            if self.kontext_api_manager is not None:
                result: WorkflowResult = await self.kontext_api_manager.generate_image("", params, images)
                return result
            else:
                raise RuntimeError("kontext_api_manager is None")
        except Exception as e:
            return WorkflowResult(
                success=False,
                images=[],
                metadata={},
                workflow_type="kontext_api",
                error_message=str(e)
            )
    
    async def _fallback_workflow(
        self,
        intent: dict,
        session: dict,
        images: list
    ) -> WorkflowResult:
        """降级工作流"""
        # 按优先级尝试可用的工作流
        workflows = [
            ("flux", self.flux_manager),
            ("kontext", self.kontext_manager),
            ("kontext_api", self.kontext_api_manager)
        ]
        
        for workflow_type, manager in workflows:
            if manager is not None:
                try:
                    if workflow_type == "flux":
                        return await self._execute_flux_workflow(intent, session, images)
                    elif workflow_type == "kontext":
                        return await self._execute_kontext_workflow(intent, session, images)
                    elif workflow_type == "kontext_api":
                        return await self._execute_kontext_api_workflow(intent, session, images)
                except Exception as e:
                    self.logger.warning(f"{workflow_type} 工作流执行失败: {e}")
                    continue
        
        # 所有工作流都失败
        return WorkflowResult(
            success=False,
            images=[],
            metadata={},
            workflow_type="fallback",
            error_message="所有工作流都不可用"
        )
    
    def _build_kontext_api_workflow(
        self,
        intent: dict,
        session: dict
    ) -> dict:
        """构建 Kontext API 工作流数据"""
        # 这里需要根据具体的 ComfyUI 工作流格式来构建
        # 暂时返回基础结构
        return {
            "workflow": {
                "nodes": {},
                "connections": {}
            },
            "prompt": intent.get("prompt", ""),
            "parameters": intent.get("parameters", {})
        }
    
    async def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status: Dict[str, Any] = {
            "workflows": {
                "flux": bool(self.flux_manager),
                "kontext": bool(self.kontext_manager),
                "kontext_api": bool(self.kontext_api_manager)
            },
            "shared_modules": {
                "lora_manager": bool(self.lora_manager),
                "comfyui_client": bool(self.comfyui_client)
            }
        }
        
        # 检查 ComfyUI 连接状态
        if self.comfyui_client:
            status["comfyui_connection"] = await self.comfyui_client.check_connection()
        
        return status
    
    async def close(self):
        """关闭 Agent，清理资源"""
        if self.flux_manager and hasattr(self.flux_manager, 'close'):
            await self.flux_manager.close()
        if self.kontext_manager and hasattr(self.kontext_manager, 'close'):
            await self.kontext_manager.close()
        if self.kontext_api_manager and hasattr(self.kontext_api_manager, 'close'):
            await self.kontext_api_manager.close()
        if self.comfyui_client and hasattr(self.comfyui_client, 'close'):
            await self.comfyui_client.close() 