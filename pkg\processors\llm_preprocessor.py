from typing import Dict, Any, List, Optional
import re
from pkg.platform.types import message as platform_message

class LLMPreprocessor:
    """LLM预处理器，用于处理微信群消息并提取关键信息"""
    
    def __init__(self, llm_router):
        """
        初始化LLM预处理器
        :param llm_router: LLM路由器实例
        """
        self.llm_router = llm_router
        
    async def process_message(self, message_chain: platform_message.MessageChain) -> Dict[str, Any]:
        """
        处理消息链并提取关键信息
        :param message_chain: 消息链
        :return: 提取的关键信息
        """
        # 提取文本内容
        text_content = self._extract_text(message_chain)
        
        # 提取图片内容
        images = self._extract_images(message_chain)
        
        # 使用LLM分析内容
        analysis = await self._analyze_with_llm(text_content, images)
        
        return analysis
    
    def _extract_text(self, message_chain: platform_message.MessageChain) -> str:
        """提取消息链中的文本内容"""
        text_parts = []
        for component in message_chain:
            if isinstance(component, platform_message.Plain):
                text_parts.append(component.text)
        return " ".join(text_parts)
    
    def _extract_images(self, message_chain: platform_message.MessageChain) -> List[str]:
        """提取消息链中的图片URL"""
        image_urls = []
        for component in message_chain:
            if isinstance(component, platform_message.Image):
                if hasattr(component, 'url') and component.url:
                    image_urls.append(component.url)
        return image_urls
    
    async def _analyze_with_llm(self, text: str, images: List[str]) -> Dict[str, Any]:
        """
        使用LLM分析文本和图片内容
        :param text: 文本内容
        :param images: 图片URL列表
        :return: 分析结果
        """
        # 构建提示词
        prompt = f"""
        分析以下微信群消息，提取可能的图像生成指令：
        
        文本内容: {text}
        图片数量: {len(images)}
        
        请提取以下信息:
        1. 是否包含图像生成请求 (是/否)
        2. 图像生成的主题描述
        3. 图像的风格要求
        4. 其他特殊要求
        
        以JSON格式返回结果。
        """
        
        # 调用LLM
        response = await self.llm_router.route(prompt)
        
        # 解析LLM返回的结果
        try:
            import json
            result = json.loads(response.get("text", "{}"))
            return result
        except:
            # 如果解析失败，返回默认结果
            return {
                "is_image_request": False,
                "theme": "",
                "style": "",
                "special_requirements": ""
            }
