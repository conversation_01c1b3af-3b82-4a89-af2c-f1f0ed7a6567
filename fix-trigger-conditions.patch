diff --git a/pkg/core/session/models.py b/pkg/core/session/models.py
index 98ad41af..c3ddf67b 100644
--- a/pkg/core/session/models.py
+++ b/pkg/core/session/models.py
@@ -101,6 +101,9 @@ class WorkflowSession:
     quoted_text: str = ""
     quoted_images: List[bytes] = field(default_factory=list)
     
+    # 路由结果
+    routing_result: Optional[Any] = None  # 存储路由决策结果
+    
     # 时间管理
     created_at: float = field(default_factory=time.time)
     updated_at: float = field(default_factory=time.time)
diff --git a/pkg/pipeline/resprule/rules/atbot_with_prefix.py b/pkg/pipeline/resprule/rules/atbot_with_prefix.py
index 2f3f67d0..f3ba7eeb 100644
--- a/pkg/pipeline/resprule/rules/atbot_with_prefix.py
+++ b/pkg/pipeline/resprule/rules/atbot_with_prefix.py
@@ -28,13 +28,24 @@ class AtBotWithPrefixRule(rule_model.GroupRespondRule):
         if not has_at_bot:
             return entities.RuleJudgeResult(matching=False, replacement=message_chain)
         
-        # 第二个条件：必须包含触发词
+        # 🔥 关键修复：创建临时消息链，移除@机器人后再检查前缀
+        temp_chain = message_chain.copy()
+        temp_chain.remove(platform_message.At(query.adapter.bot_account_id))
+        
+        # 检查是否有重复的@（回复消息时会at两次）
+        if temp_chain.has(platform_message.At(query.adapter.bot_account_id)):
+            temp_chain.remove(platform_message.At(query.adapter.bot_account_id))
+        
+        # 获取移除@机器人后的纯文本
+        clean_text = str(temp_chain).strip()
+        
+        # 第二个条件：必须包含触发词（在清理后的文本中检查）
         prefixes = rule_dict.get('prefix', [])
         has_prefix = False
         matched_prefix = None
         
         for prefix in prefixes:
-            if message_text.startswith(prefix):
+            if clean_text.startswith(prefix):
                 has_prefix = True
                 matched_prefix = prefix
                 break
@@ -54,7 +65,9 @@ class AtBotWithPrefixRule(rule_model.GroupRespondRule):
         if matched_prefix:
             for me in message_chain:
                 if isinstance(me, platform_message.Plain):
-                    me.text = me.text[len(matched_prefix):].strip()
+                    me.text = me.text.strip()  # 先清理空格
+                    if me.text.startswith(matched_prefix):
+                        me.text = me.text[len(matched_prefix):].strip()
                     break
         
         return entities.RuleJudgeResult(
diff --git a/pkg/provider/runners/comfyui_agent.py b/pkg/provider/runners/comfyui_agent.py
index d6cf8fb0..39100c38 100644
--- a/pkg/provider/runners/comfyui_agent.py
+++ b/pkg/provider/runners/comfyui_agent.py
@@ -74,293 +74,157 @@ class ComfyUIAgentRunner(runner.RequestRunner, UnifiedRoutingMixinV2):
         self.unified_router = get_unified_router(ap)
 
     async def run(self, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
-        """运行 ComfyUI Agent"""
+        """处理查询请求"""
         try:
-            # 延迟初始化处理器（需要query对象）
-            self._initialize_handlers(query)
-            
-            # 确保所有处理器已初始化
-            if not self.standard_handler or not self.kontext_handler or not self.admin_sync_handler:
-                yield llm_entities.Message(
-                    role='assistant', 
-                    content="处理器初始化失败"
-                )
-                return
+            # 调试计数器
+            self.debug_counter += 1
+            self.ap.logger.info(f"🔄 ComfyUIAgentRunner.run() 调用 #{self.debug_counter}")
             
-            # 提取用户文本和图片
+            # 1. 基础数据提取
             user_text = self._extract_user_text(query)
             user_images = await self._extract_user_images(query)
-            
-            # 获取用户和聊天ID
             user_id = self._get_user_id(query)
             chat_id = self._get_chat_id(query)
             
-            # 🔥 使用统一会话管理器检查会话状态
-            session = self.session_manager.get_session(user_id, chat_id)
-            
-            # 检查是否是开始指令
-            from ...core.session.states import is_execution_command
-            if is_execution_command(user_text):
-                if session and session.is_ready_for_execution():
-                    # 触发生成
-                    success, message, session = self.session_manager.trigger_execution(user_id, chat_id)
-                    if success and session:
-                        yield llm_entities.Message(role='assistant', content=message)
-                        
-                        # 根据工作流类型执行生成
-                        if session.workflow_type == WorkflowType.AIGEN:
-                            async for msg in self._execute_aigen_workflow(session, query):
-                                yield msg
-                        elif session.workflow_type == WorkflowType.KONTEXT:
-                            # 转换SessionImage为bytes列表
-                            image_bytes = [img.data for img in session.images]
-                            async for msg in self.kontext_handler.handle_kontext_workflow(
-                                session.prompt, image_bytes, user_id, chat_id, query
-                            ):
-                                yield msg
-                        elif session.workflow_type == WorkflowType.KONTEXT_API:
-                            # TODO: 实现kontext api工作流
-                            yield llm_entities.Message(role='assistant', content="Kontext API工作流暂未实现")
-                        
-                        # 完成会话
-                        self.session_manager.complete_session(user_id, chat_id)
-                    else:
-                        yield llm_entities.Message(role='assistant', content=message)
-                else:
-                    yield llm_entities.Message(
-                        role='assistant',
-                        content="没有活跃的工作流会话，请先发送工作流指令启动"
-                    )
-                return
-            
-            # 检查是否是取消指令
-            from ...core.session.states import is_cancel_command
-            if is_cancel_command(user_text):
-                success, message = self.session_manager.cancel_session(user_id, chat_id)
-                yield llm_entities.Message(role='assistant', content=message)
-                return
+            self.ap.logger.info(f"📝 用户消息: {user_text[:100]}...")
+            self.ap.logger.info(f"🖼️ 用户图片: {len(user_images)} 张")
+            self.ap.logger.info(f"👤 用户ID: {user_id}")
+            self.ap.logger.info(f"💬 聊天ID: {chat_id}")
             
-            # 如果有活跃会话，处理会话交互（上传图片、修改文字等）
-            if session:
-                self.ap.logger.info(f"🔍 处理活跃会话交互 - 用户文本: '{user_text}', 图片数量: {len(user_images)}")
-                
-                # 🔥 使用统一的会话管理器处理用户输入
-                try:
-                    response_message, action_info = self.session_manager.process_user_input(
-                        user_id, user_text, user_images, chat_id
-                    )
-                    
-                    self.ap.logger.info(f"📝 会话处理结果: action={action_info.get('action', 'unknown')}, message='{response_message}'")
-                    
-                    # 发送响应消息
-                    if response_message:
-                        yield llm_entities.Message(role='assistant', content=response_message)
-                    
-                    # 处理特殊动作
-                    action = action_info.get('action', '')
-                    if action == 'execute' and action_info.get('success', False):
-                        # 执行工作流
-                        updated_session = action_info.get('session')
-                        if updated_session:
-                            if updated_session.workflow_type == WorkflowType.AIGEN:
-                                async for msg in self._execute_aigen_workflow(updated_session, query):
-                                    yield msg
-                            elif updated_session.workflow_type == WorkflowType.KONTEXT:
-                                if self.kontext_handler:
-                                    async for msg in self.kontext_handler.handle_kontext_workflow(
-                                        updated_session.prompt, 
-                                        [img.data for img in updated_session.images], 
-                                        user_id, 
-                                        chat_id, 
-                                        query
-                                    ):
-                                        yield msg
-                                else:
-                                    yield llm_entities.Message(role='assistant', content="Kontext处理器未初始化")
-                            elif updated_session.workflow_type == WorkflowType.KONTEXT_API:
-                                yield llm_entities.Message(role='assistant', content="Kontext API工作流暂未实现")
-                            
-                            # 完成会话
-                            self.session_manager.complete_session(user_id, chat_id)
-                    
-                    # 🔥 新增：图片反推工作流执行
-                    elif action == 'execute_image_to_text' and action_info.get('success', False):
-                        updated_session = action_info.get('session')
-                        if updated_session and updated_session.images:
-                            # 执行图片转文本工作流
-                            async for msg in self._execute_image_to_text_workflow(updated_session, query):
-                                yield msg
-                            # 完成会话
-                            self.session_manager.complete_session(user_id, chat_id)
-                    
-                    elif action == 'image_added_with_question':
-                        # 图片已添加，但需要确认类型
-                        self.ap.logger.info("📷 图片已添加，等待用户确认图片类型")
-                        # 不需要额外处理，响应消息已经包含了询问
-                    
-                    elif action == 'image_type_confirmed':
-                        # 图片类型已确认
-                        self.ap.logger.info("✅ 图片类型已确认")
-                        # 不需要额外处理，响应消息已经包含了确认结果
-                    
-                except Exception as e:
-                    self.ap.logger.error(f"❌ 处理会话交互失败: {e}")
-                    import traceback
-                    self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
-                    yield llm_entities.Message(role='assistant', content=f"处理会话交互失败: {str(e)}")
-                
-                return
+            # 2. 获取或创建会话
+            session = self.session_manager.get_session(user_id, chat_id)
             
-            # 🔥 使用统一路由系统判断是否是工作流请求
-            # 🔥 重要修改：只在没有活跃会话时才做路由决策，避免过早选择工作流
+            # 3. 如果没有会话，使用路由系统创建会话
             if not session:
-                # 🚨 关键修复：在调用路由系统之前，先检查是否包含有效的工作流前缀
-                # 避免对普通群聊消息进行路由决策，防止误触发
-                if not self._is_valid_image_generation_request(user_text, query, user_id, chat_id):
-                    self.ap.logger.info(f"❌ 不是有效的工作流请求，跳过处理: {user_text[:50]}...")
-                    return
+                self.ap.logger.info("🆕 没有活跃会话，启动路由系统")
                 
+                # 使用统一路由系统
                 routing_result = await self.route_workflow_intelligently(user_text, query, user_images)
                 
                 # 记录路由决策
-                self.ap.logger.info(f"🎯 统一路由决策: {routing_result.workflow_type.value}")
-                self.ap.logger.info(f"   路由级别: {routing_result.routing_level.value}")
+                self.ap.logger.info(f"🎯 路由决策: {routing_result.workflow_type.value}")
                 self.ap.logger.info(f"   置信度: {routing_result.confidence.value}")
-                self.ap.logger.info(f"   推理: {routing_result.reasoning}")
+                self.ap.logger.info(f"   原因: {routing_result.reasoning}")
                 
-                # 如果需要用户确认，发送确认消息
-                if self.should_ask_for_confirmation(routing_result):
-                    confirmation_message = self.get_routing_explanation(routing_result)
-                    yield llm_entities.Message(role='assistant', content=confirmation_message)
+                # 如果是未知类型，跳过处理
+                if routing_result.workflow_type == WorkflowType.UNKNOWN:
+                    self.ap.logger.info("🚫 路由系统判断为非工作流请求，跳过处理")
                     return
                 
-                # 🔥 根据统一路由结果创建会话（不立即选择工作流）
-                workflow_type = routing_result.workflow_type
-                suggested_prompt = routing_result.suggested_prompt or user_text
+                # 创建新会话
+                session = self.session_manager.create_session(
+                    user_id=user_id,
+                    workflow_type=routing_result.workflow_type,
+                    chat_id=chat_id,
+                    prompt=user_text,
+                    timeout_minutes=10
+                )
                 
-                # 1. 处理Kontext工作流
-                if workflow_type == WorkflowType.KONTEXT:
-                    # 创建kontext会话
-                    session = self.session_manager.create_session(user_id, WorkflowType.KONTEXT, chat_id, suggested_prompt)
-                    
+                # 保存路由结果
+                session.routing_result = routing_result
+                
+                self.ap.logger.info(f"✅ 创建新会话: {session.session_id}")
+                
+                # 添加用户图片
+                if user_images:
+                    for img_data in user_images:
+                        img_type = self._detect_image_type(img_data, user_text)
+                        session.add_image(img_data, img_type)
+                
+                self.ap.logger.info(f"📋 会话创建完成: {session.workflow_type.value}")
+                
+                # 根据工作流类型提供不同的响应
+                if session.workflow_type == WorkflowType.AIGEN:
+                    yield llm_entities.Message(
+                        role='assistant',
+                        content=f"🎨 **Aigen工作流已启动**\n\n"
+                               f"📝 **提示词**: {session.prompt}\n"
+                               f"🖼️ **图片**: {len(session.images)} 张\n"
+                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
+                               f"❌ 发送 '取消' 取消工作流"
+                    )
+                elif session.workflow_type == WorkflowType.KONTEXT:
                     yield llm_entities.Message(
                         role='assistant',
                         content=f"🎨 **Kontext工作流已启动**\n\n"
-                               f"📝 **提示词**: {suggested_prompt}\n"
-                               f"🖼️ **图片数量**: {len(user_images)} 张\n"
-                               f"💡 **状态**: 等待图片上传\n\n"
-                               f"📷 请上传图片或发送 '开始' 指令生成图片\n"
+                               f"📝 **提示词**: {session.prompt}\n"
+                               f"🖼️ **图片**: {len(session.images)} 张\n"
+                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                                f"❌ 发送 '取消' 取消工作流"
                     )
-                    return
-                
-                # 2. 处理AIGEN工作流
-                elif workflow_type == WorkflowType.AIGEN:
-                    # 🔥 新增：图片反推专用分支
-                    if suggested_prompt.startswith('图片反推'):
-                        session = self.session_manager.create_session(user_id, WorkflowType.AIGEN, chat_id, suggested_prompt)
-                        session.set_prompt('图片反推')
-                        session.state = SessionState.WAITING_FOR_IMAGES
-                        yield llm_entities.Message(
-                            role='assistant',
-                            content=f"🖼️ **图片反推工作流已启动**\n\n请上传要识别的图片，系统将自动识别图片内容。\n\n发送 '取消' 可随时中断。"
-                        )
-                        return
-                    
-                    # 创建aigen会话
-                    session = self.session_manager.create_session(user_id, WorkflowType.AIGEN, chat_id, suggested_prompt)
-                    
-                    # 🔥 新增：检查提示词是否包含图片需求
-                    prompt_lower = suggested_prompt.lower()
-                    needs_image = any(keyword in prompt_lower for keyword in ['这张图', '图片', '照片', '控制图', '参考图', '根据', '基于'])
-                    
-                    if needs_image:
-                        yield llm_entities.Message(
-                            role='assistant',
-                            content=f"🎨 **Aigen工作流已启动**\n\n"
-                                   f"📝 **提示词**: {suggested_prompt}\n"
-                                   f"🖼️ **图片数量**: {len(user_images)} 张\n"
-                                   f"💡 **状态**: 等待图片上传\n\n"
-                                   f"📷 请上传图片，然后发送 '开始' 指令生成图片\n"
-                                   f"❌ 发送 '取消' 取消工作流"
-                        )
-                    else:
-                        yield llm_entities.Message(
-                            role='assistant',
-                            content=f"🎨 **Aigen工作流已启动**\n\n"
-                                   f"📝 **提示词**: {suggested_prompt}\n"
-                                   f"🖼️ **图片数量**: {len(user_images)} 张\n"
-                                   f"💡 **状态**: 准备就绪\n\n"
-                                   f"🚀 发送 '开始'、'go'、'执行' 等指令生成图片\n"
-                                   f"❌ 发送 '取消'、'cancel' 取消工作流"
-                        )
-                    return
-                
-                # 3. 处理KONTEXT_API工作流
-                elif workflow_type == WorkflowType.KONTEXT_API:
-                    # 创建kontext_api会话
-                    session = self.session_manager.create_session(user_id, WorkflowType.KONTEXT_API, chat_id, suggested_prompt)
-                    
+                elif session.workflow_type == WorkflowType.KONTEXT_API:
                     yield llm_entities.Message(
                         role='assistant',
                         content=f"☁️ **远程API工作流已启动**\n\n"
-                               f"📝 **提示词**: {suggested_prompt}\n"
-                               f"🖼️ **图片数量**: {len(user_images)} 张\n"
-                               f"💡 **状态**: 等待图片上传\n\n"
-                               f"📷 请上传图片或发送 '开始' 指令生成图片\n"
+                               f"📝 **提示词**: {session.prompt}\n"
+                               f"🖼️ **图片**: {len(session.images)} 张\n"
+                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                                f"❌ 发送 '取消' 取消工作流"
                     )
-                    return
-            else:
-                # 有活跃会话，不需要重新路由
-                self.ap.logger.info(f"🔍 检测到活跃会话，跳过路由决策")
+                return
             
-            # 4. 处理标准图片生成请求 - 🔥 修改：所有工作流都应该等待"开始"指令
-            self.ap.logger.info(f"ComfyUI Agent处理图片生成请求: {user_text[:50]}...")
+            # 4. 处理有活跃会话的情况
+            self.ap.logger.info(f"🔄 处理活跃会话: {session.session_id}")
+            self.ap.logger.info(f"   工作流类型: {session.workflow_type.value}")
+            self.ap.logger.info(f"   会话状态: {session.state.value}")
             
-            # 🔥 重要：检查是否是执行指令
+            # 初始化处理器
+            self._initialize_handlers(query)
+            
+            # 检查是否是执行指令
             if is_execution_command(user_text):
-                # 用户发送了执行指令，但没有活跃会话
+                self.ap.logger.info("🚀 检测到执行指令")
+                
+                # 根据工作流类型执行不同的处理逻辑
+                if session.workflow_type == WorkflowType.AIGEN:
+                    async for message in self._execute_aigen_workflow(session, query):
+                        yield message
+                elif session.workflow_type == WorkflowType.KONTEXT:
+                    async for message in self._execute_kontext_workflow(session, query):
+                        yield message
+                elif session.workflow_type == WorkflowType.KONTEXT_API:
+                    async for message in self._execute_kontext_api_workflow(session, query):
+                        yield message
+                else:
+                    yield llm_entities.Message(
+                        role='assistant',
+                        content=f"❌ 未知工作流类型: {session.workflow_type.value}"
+                    )
+                return
+            
+            # 检查是否是取消指令
+            if is_cancel_command(user_text):
+                self.ap.logger.info("❌ 检测到取消指令")
+                self.session_manager.delete_session(user_id, chat_id)
                 yield llm_entities.Message(
                     role='assistant',
-                    content="❌ **没有活跃的工作流会话**\n\n"
-                           f"💡 **请先启动工作流**:\n"
-                           f"  • 发送 'aigen [提示词]' 启动文生图工作流\n"
-                           f"  • 发送 'kontext [指令]' 启动图生图工作流\n\n"
-                           f"📝 **示例**:\n"
-                           f"  • aigen 一只可爱的猫咪\n"
-                           f"  • kontext 编辑这张图片"
+                    content="❌ 工作流已取消"
                 )
                 return
             
-            # 🔥 重要：如果不是执行指令，提示用户启动工作流
+            # 其他情况，更新会话内容
+            session.set_prompt(user_text)
+            if user_images:
+                for img_data in user_images:
+                    img_type = self._detect_image_type(img_data, user_text)
+                    session.add_image(img_data, img_type)
+            
             yield llm_entities.Message(
                 role='assistant',
-                content="💡 **请使用工作流指令**\n\n"
-                       f"🎨 **支持的指令**:\n"
-                       f"  • aigen [提示词] - 文生图工作流\n"
-                       f"  • kontext [指令] - 图生图工作流\n"
-                       f"  • kontext_api [指令] - 远程API工作流\n\n"
-                       f"📝 **示例**:\n"
-                       f"  • aigen 一只可爱的猫咪\n"
-                       f"  • aigen 以这张图为控制图，生成吉卜力风格插画\n"
-                       f"  • kontext 编辑这张图片\n"
-                       f"  • kontext_api 复杂图像处理\n\n"
-                       f"💡 **工作流特点**:\n"
-                       f"  • 支持多轮交互（修改提示词、上传图片）\n"
-                       f"  • 需要发送 '开始' 指令才会执行\n"
-                       f"  • 可以随时发送 '取消' 中断工作流\n"
-                       f"  • 支持自然语言描述，系统自动选择最适合的工作流"
+                content=f"✅ 已更新会话内容\n\n"
+                       f"📝 **提示词**: {session.prompt}\n"
+                       f"🖼️ **图片**: {len(session.images)} 张\n"
+                       f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
+                       f"❌ 发送 '取消' 取消工作流"
             )
-            return
-            
+                
         except Exception as e:
-            self.ap.logger.error(f"ComfyUI Agent运行出错: {e}")
+            self.ap.logger.error(f"ComfyUIAgentRunner处理失败: {e}")
             import traceback
             self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
             yield llm_entities.Message(
                 role='assistant',
-                content=f"处理请求时出错: {str(e)}"
+                content=f"❌ 处理请求时出错: {str(e)}"
             )
 
     def _init_handlers(self):
