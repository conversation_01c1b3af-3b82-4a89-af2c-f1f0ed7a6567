"""
远程队列监控器

负责监控远程 ComfyUI.com 的任务队列状态
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from .kontext_api_auth_handler import APIAuthHandler
from .kontext_api_workflow_manager import RemoteTaskResult


class RemoteQueueMonitor:
    """远程队列监控器"""
    
    def __init__(self, auth_handler: APIAuthHandler):
        self.auth_handler = auth_handler
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://comfyui.com/api"
        
    async def monitor_task(self, task_id: str, timeout: int = 300) -> RemoteTaskResult:
        """
        监控任务执行状态
        
        Args:
            task_id: 任务ID
            timeout: 超时时间(秒)
            
        Returns:
            RemoteTaskResult: 任务结果
        """
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=timeout)
        
        while datetime.now() < end_time:
            try:
                status_info = await self.get_task_status(task_id)
                status = status_info.get("status", "unknown")
                
                if status == "completed":
                    return await self._build_completed_result(task_id, status_info)
                elif status == "failed":
                    return await self._build_failed_result(task_id, status_info)
                elif status in ["pending", "running"]:
                    # 继续等待
                    await asyncio.sleep(2)
                else:
                    self.logger.warning(f"未知任务状态: {status}")
                    await asyncio.sleep(5)
                    
            except Exception as e:
                self.logger.error(f"监控任务 {task_id} 时出错: {e}")
                await asyncio.sleep(5)
        
        # 超时
        return await self._build_timeout_result(task_id)
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        session = await self.auth_handler._get_session()
        url = f"{self.base_url}/task/{task_id}/status"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                raise Exception(f"获取任务状态失败: {response.status} - {error_text}")
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            session = await self.auth_handler._get_session()
            url = f"{self.base_url}/task/{task_id}/cancel"
            
            async with session.post(url) as response:
                return response.status == 200
        except Exception as e:
            self.logger.error(f"取消任务 {task_id} 失败: {e}")
            return False
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        session = await self.auth_handler._get_session()
        url = f"{self.base_url}/queue/status"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                self.logger.warning(f"获取队列状态失败: {response.status}")
                return {"queue_length": "unknown", "estimated_wait": "unknown"}
    
    async def _build_completed_result(self, task_id: str, status_info: Dict[str, Any]) -> RemoteTaskResult:
        """构建完成结果"""
        return RemoteTaskResult(
            task_id=task_id,
            status="completed",
            images=status_info.get("images", []),
            metadata=status_info.get("metadata", {}),
            created_at=datetime.now(),
            completed_at=datetime.now()
        )
    
    async def _build_failed_result(self, task_id: str, status_info: Dict[str, Any]) -> RemoteTaskResult:
        """构建失败结果"""
        return RemoteTaskResult(
            task_id=task_id,
            status="failed",
            images=[],
            metadata=status_info.get("metadata", {}),
            created_at=datetime.now(),
            completed_at=datetime.now(),
            error_message=status_info.get("error", "Unknown error")
        )
    
    async def _build_timeout_result(self, task_id: str) -> RemoteTaskResult:
        """构建超时结果"""
        return RemoteTaskResult(
            task_id=task_id,
            status="timeout",
            images=[],
            metadata={},
            created_at=datetime.now(),
            error_message="Task execution timeout"
        )
    
    async def close(self):
        """关闭监控器"""
        # 清理资源
        pass 