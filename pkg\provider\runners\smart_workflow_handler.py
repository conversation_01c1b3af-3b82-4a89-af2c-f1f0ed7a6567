"""
智能工作流处理器
基于智能路由系统，自动分析用户意图和图像内容，选择最适合的工作流
"""

from __future__ import annotations

import json
import typing
import asyncio
import aiohttp
import base64
import random
import os
import tempfile
import re
import time
from typing import Dict, Any, Optional, List, AsyncGenerator
import hashlib
import uuid

from .. import entities as llm_entities
from ...core import app, entities as core_entities
from ...platform import types as platform_types
from ...platform.types import message as platform_message
from ...core.session.models import WorkflowType
from ...core.intent.analyzer import IntentAnalyzer
from ...core.workflow.unified_routing_system import get_unified_router
from pkg.core.workflow.manager_base import WorkflowResult


class SmartWorkflowHandler:
    """智能工作流处理器"""
    
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.ap = ap
        self.pipeline_config = pipeline_config
        
        # 获取 ComfyUI 配置
        ai_config = self.pipeline_config.get('ai', {})
        comfyui_config = ai_config.get('comfyui-agent', {})
        self.api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        self.timeout = comfyui_config.get('timeout', 180)
        
        # 初始化工作流管理器和意图分析器
        from ...workers.flux.flux_workflow_manager import get_flux_workflow_manager
        self.workflow_manager = get_flux_workflow_manager(ap, pipeline_config)
        self.intent_analyzer = IntentAnalyzer()
        
        # 用户会话管理
        self.user_sessions: Dict[str, Dict[str, Any]] = {}
        
        self.ap.logger.info(f"SmartWorkflowHandler 初始化完成")
        self.ap.logger.info(f"  - API URL: {self.api_url}")
        self.ap.logger.info(f"  - 超时时间: {self.timeout}")

    def is_workflow_request(self, text: str) -> bool:
        """检查是否是工作流请求"""
        text_lower = text.lower().strip()
        
        # 检查是否是明确的开始指令
        if text_lower in ['开始', 'go', '生成', 'create', 'start']:
            return True
        
        # 检查是否包含工作流关键词
        workflow_keywords = [
            'controlnet', 'redux', 'kontext', '混合', 'hybrid',
            '控制', '参考', '风格', '草图', '线稿'
        ]
        
        return any(keyword in text_lower for keyword in workflow_keywords)

    async def extract_user_images(self, query: core_entities.Query) -> List[bytes]:
        """提取用户消息中的图片数据"""
        images = []
        try:
            # 提取直接消息中的图片
            if query.user_message and query.user_message.content:
                if isinstance(query.user_message.content, list):
                    for content in query.user_message.content:
                        if hasattr(content, 'type') and content.type in ['image', 'image_base64', 'image_url']:
                            if hasattr(content, 'image_base64') and content.image_base64:
                                try:
                                    base64_str = content.image_base64
                                    if base64_str.startswith('data:'):
                                        if ',' in base64_str:
                                            base64_str = base64_str.split(',', 1)[1]
                                    
                                    missing_padding = len(base64_str) % 4
                                    if missing_padding:
                                        base64_str += '=' * (4 - missing_padding)
                                    
                                    image_data = base64.b64decode(base64_str)
                                    images.append(image_data)
                                except Exception as e:
                                    self.ap.logger.error(f"解码base64图片失败: {e}")
                            elif hasattr(content, 'data') and getattr(content, 'data', None):
                                images.append(getattr(content, 'data'))
            
            # 提取引用消息中的图片
            quoted_images = await self.extract_quoted_images(query)
            if quoted_images:
                images.extend(quoted_images)
            
            self.ap.logger.info(f"提取到 {len(images)} 张图片")
        except Exception as e:
            self.ap.logger.error(f"提取图片数据失败: {e}")
        return images

    async def extract_quoted_images(self, query: core_entities.Query) -> List[bytes]:
        """从引用消息中提取图片"""
        images = []
        try:
            message_chain = query.message_chain
            if not message_chain:
                return images
            
            from ...platform.types import message as platform_message
            for component in message_chain:
                if isinstance(component, platform_message.Quote):
                    for quote_item in component.origin:
                        if isinstance(quote_item, platform_message.Image):
                            try:
                                image_bytes, mime_type = await quote_item.get_bytes()
                                images.append(image_bytes)
                            except Exception as e:
                                self.ap.logger.error(f"提取引用消息图片失败: {e}")
        except Exception as e:
            self.ap.logger.error(f"提取引用消息图片失败: {e}")
        return images

    def get_user_id(self, query: core_entities.Query) -> str:
        """获取用户ID"""
        try:
            if hasattr(query.message_event, 'sender') and getattr(query.message_event, 'sender', None):
                sender = getattr(query.message_event, 'sender')
                if hasattr(sender, 'member_name') and sender.member_name:
                    return str(sender.member_name)
                elif hasattr(sender, 'nickname') and sender.nickname:
                    return str(sender.nickname)
                elif hasattr(sender, 'user_id') and sender.user_id:
                    return str(sender.user_id)
            
            if hasattr(query, 'session') and query.session:
                return str(query.session.launcher_id)
            
            return "unknown_user"
        except Exception as e:
            self.ap.logger.error(f"获取用户ID失败: {e}")
            return "unknown_user"

    async def handle_workflow_request(self, user_text: str, user_images: list[bytes], user_id: str, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理工作流请求"""
        try:
            # 1. 分析用户意图
            intent_analysis = self.intent_analyzer.analyze_intent(user_text)
            self.ap.logger.info(f"意图分析结果: {intent_analysis.content_type.value}, 置信度: {intent_analysis.confidence}")
            
            # 2. 检查是否是开始指令
            if self._is_start_command(user_text):
                # 检查是否有活跃会话
                if user_id in self.user_sessions:
                    session = self.user_sessions[user_id]
                    async for message in self._execute_workflow_session(session, query):
                        yield message
                    return
                else:
                    yield llm_entities.Message(
                        role='assistant',
                        content="❌ 没有活跃的工作流会话。请先上传图片或设置参数，然后发送 '开始' 指令。"
                    )
                    return
            
            # 3. 根据意图分析结果处理
            if intent_analysis.input_mode == "text_only":
                # 检查是否需要等待图片输入
                if self._needs_image_input(user_text):
                    async for message in self._handle_waiting_for_image_workflow(user_text, intent_analysis, user_id, query):
                        yield message
                else:
                    # 纯文本生成
                    async for message in self._handle_text_only_workflow(user_text, intent_analysis, query):
                        yield message
            elif intent_analysis.input_mode == "image_control":
                # 图像控制生成
                async for message in self._handle_image_control_workflow(user_text, user_images, intent_analysis, user_id, query):
                    yield message
            elif intent_analysis.input_mode == "hybrid":
                # 混合控制生成
                async for message in self._handle_hybrid_workflow(user_text, user_images, intent_analysis, user_id, query):
                    yield message
            else:
                # 默认处理
                async for message in self._handle_default_workflow(user_text, user_images, intent_analysis, user_id, query):
                    yield message
                    
        except Exception as e:
            self.ap.logger.error(f"处理工作流请求出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理工作流请求时出错: {str(e)}"
            )

    def _is_start_command(self, text: str) -> bool:
        """检查是否是开始指令"""
        start_commands = ['开始', 'go', '生成', 'create', 'start', '执行']
        return text.lower().strip() in [cmd.lower() for cmd in start_commands]

    def _needs_image_input(self, text: str) -> bool:
        """检查是否需要图片输入"""
        image_related_keywords = ["根据", "参考", "这张", "图片", "照片", "姿势", "pose", "风格", "style", "草图", "线稿", "控制"]
        return any(keyword in text for keyword in image_related_keywords)

    async def _handle_waiting_for_image_workflow(self, user_text: str, intent_analysis, user_id: str, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理等待图片输入的工作流"""
        try:
            # 创建等待图片的会话
            session = {
                'user_id': user_id,
                'workflow_type': 'REFERENCE_TO_IMAGE',  # 默认使用参考图工作流
                'prompt': user_text,
                'images': [],
                'image_types': ['reference'],
                'min_images': 1,
                'max_images': 3,
                'image_purpose': '参考图片',
                'suggested_params': intent_analysis.suggested_params,
                'created_at': time.time(),
                'waiting_for_images': True
            }
            
            self.user_sessions[user_id] = session
            
            yield llm_entities.Message(
                role='assistant',
                content=f"🎨 **Aigen工作流已启动**\n\n"
                       f"📝 **提示词**: {user_text}\n"
                       f"🖼️ **状态**: 等待图片输入\n\n"
                       f"💡 **支持的图片类型**:\n"
                       f"  • 参考图片 (风格、内容参考)\n"
                       f"  • 控制图片 (草图、线稿、姿势等)\n"
                       f"  • 混合模式 (多张图片)\n\n"
                       f"📷 请上传图片，然后发送 '开始' 指令生成图片！\n\n"
                       f"💡 **提示**: 您可以上传1-3张图片，系统会自动选择最适合的工作流。"
            )
            
        except Exception as e:
            self.ap.logger.error(f"处理等待图片工作流出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理等待图片工作流时出错: {str(e)}"
            )

    async def _handle_text_only_workflow(self, user_text: str, intent_analysis, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理纯文本生成工作流"""
        raise NotImplementedError("此方法依赖旧的 workflow_manager.workflows，建议迁移到 UnifiedAgent 或新架构。")

    async def _handle_image_control_workflow(self, user_text: str, user_images: List[bytes], 
                                           intent_analysis, user_id: str, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理图像控制工作流"""
        try:
            # 检查图片要求
            if len(user_images) < intent_analysis.min_images:
                yield llm_entities.Message(
                    role='assistant',
                    content=f"📷 需要至少 {intent_analysis.min_images} 张图片，当前只有 {len(user_images)} 张。\n"
                           f"💡 用途: {intent_analysis.image_purpose}\n"
                           f"📝 提示词: {user_text}\n\n"
                           f"请上传更多图片，然后发送 '开始' 指令。"
                )
                return
            
            if len(user_images) > intent_analysis.max_images:
                yield llm_entities.Message(
                    role='assistant',
                    content=f"📷 最多支持 {intent_analysis.max_images} 张图片，当前有 {len(user_images)} 张。\n"
                           f"将使用前 {intent_analysis.max_images} 张图片。"
                )
                user_images = user_images[:intent_analysis.max_images]
            
            # 创建或更新会话
            session = {
                'user_id': user_id,
                'workflow_type': intent_analysis.recommended_workflow,
                'prompt': user_text,
                'images': user_images,
                'image_types': intent_analysis.required_image_types,
                'min_images': intent_analysis.min_images,
                'max_images': intent_analysis.max_images,
                'image_purpose': intent_analysis.image_purpose,
                'suggested_params': intent_analysis.suggested_params,
                'created_at': time.time()
            }
            
            self.user_sessions[user_id] = session
            
            # 显示会话信息
            feedback_parts = [
                f"🎨 **{intent_analysis.content_type.value.upper()} 工作流已准备**\n",
                f"📝 **提示词**: {user_text}\n",
                f"🖼️ **图片数量**: {len(user_images)}/{intent_analysis.max_images}\n",
                f"💡 **图片用途**: {intent_analysis.image_purpose}\n",
                f"⚙️ **建议参数**: steps={intent_analysis.suggested_params.get('steps', 20)}, "
                f"guidance={intent_analysis.suggested_params.get('guidance', 3.5)}\n\n"
            ]
            
            if len(user_images) >= intent_analysis.min_images:
                feedback_parts.append("✅ **准备就绪**，发送 '开始' 指令生成图片！")
            else:
                feedback_parts.append(f"📷 还需要 {intent_analysis.min_images - len(user_images)} 张图片")
            
            yield llm_entities.Message(
                role='assistant',
                content="".join(feedback_parts)
            )
            
        except Exception as e:
            self.ap.logger.error(f"处理图像控制工作流出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理图像控制工作流时出错: {str(e)}"
            )

    async def _handle_hybrid_workflow(self, user_text: str, user_images: List[bytes], 
                                    intent_analysis, user_id: str, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理混合控制工作流"""
        try:
            # 混合工作流需要更多图片
            if len(user_images) < intent_analysis.min_images:
                yield llm_entities.Message(
                    role='assistant',
                    content=f"🔄 **混合控制工作流**\n\n"
                           f"📝 提示词: {user_text}\n"
                           f"📷 当前图片: {len(user_images)} 张\n"
                           f"📷 需要图片: {intent_analysis.min_images}-{intent_analysis.max_images} 张\n\n"
                           f"💡 **支持的图片类型**:\n"
                           f"  • ControlNet控制图片 (草图、线稿、姿势等)\n"
                           f"  • Redux参考图片 (风格参考)\n"
                           f"  • 普通参考图片\n\n"
                           f"请上传更多图片，然后发送 '开始' 指令。"
                )
                return
            
            # 创建混合会话
            session = {
                'user_id': user_id,
                'workflow_type': intent_analysis.recommended_workflow,
                'prompt': user_text,
                'images': user_images,
                'image_types': intent_analysis.required_image_types,
                'min_images': intent_analysis.min_images,
                'max_images': intent_analysis.max_images,
                'image_purpose': intent_analysis.image_purpose,
                'suggested_params': intent_analysis.suggested_params,
                'created_at': time.time()
            }
            
            self.user_sessions[user_id] = session
            
            yield llm_entities.Message(
                role='assistant',
                content=f"🔄 **混合控制工作流已准备**\n\n"
                       f"📝 提示词: {user_text}\n"
                       f"🖼️ 图片数量: {len(user_images)} 张\n"
                       f"💡 用途: {intent_analysis.image_purpose}\n\n"
                       f"✅ **准备就绪**，发送 '开始' 指令生成图片！"
            )
            
        except Exception as e:
            self.ap.logger.error(f"处理混合工作流出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理混合工作流时出错: {str(e)}"
            )

    async def _handle_default_workflow(self, user_text: str, user_images: List[bytes], 
                                     intent_analysis, user_id: str, query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """处理默认工作流"""
        try:
            # 如果有图片，按图像控制处理
            if user_images:
                async for message in self._handle_image_control_workflow(user_text, user_images, intent_analysis, user_id, query):
                    yield message
            else:
                # 没有图片，按纯文本处理
                async for message in self._handle_text_only_workflow(user_text, intent_analysis, query):
                    yield message
                    
        except Exception as e:
            self.ap.logger.error(f"处理默认工作流出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理默认工作流时出错: {str(e)}"
            )

    async def _execute_workflow_session(self, session: Dict[str, Any], query: core_entities.Query) -> AsyncGenerator[llm_entities.Message, None]:
        """执行工作流会话"""
        raise NotImplementedError("此方法依赖旧的 workflow_manager.workflows，建议迁移到 UnifiedAgent 或新架构。")

    async def _execute_workflow(self, workflow_config, prompt: str, images: List[bytes], query: core_entities.Query) -> WorkflowResult:
        """执行工作流"""
        try:
            # 获取 ComfyUI 配置
            comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
            api_url = comfyui_config.get('api-url', 'http://localhost:8188')
            timeout = comfyui_config.get('timeout', 180)
            
            # 获取工作流文件路径
            workflow_path = comfyui_config.get('workflow-path', 'workflows')
            workflow_file = os.path.join(workflow_path, workflow_config.file_path)
            
            if not os.path.exists(workflow_file):
                return WorkflowResult(success=False, error_message=f'工作流文件不存在: {workflow_file}')
            
            # 加载工作流文件
            with open(workflow_file, 'r', encoding='utf-8') as f:
                workflow = json.load(f)
            
            # 更新工作流参数
            updated_workflow = self._update_workflow_params(workflow, {
                'prompt': prompt,
                'steps': workflow_config.default_params.get('steps', 20),
                'guidance': workflow_config.default_params.get('guidance', 3.5),
                'width': workflow_config.default_params.get('width', 1024),
                'height': workflow_config.default_params.get('height', 1024),
                'seed': workflow_config.default_params.get('seed', -1)
            }, workflow_config)
            
            # 提交工作流到 ComfyUI
            async with aiohttp.ClientSession() as session:
                prompt_id = await self._submit_workflow(session, updated_workflow, api_url)
                if not prompt_id:
                    return WorkflowResult(success=False, error_message='提交工作流失败')
                
                # 等待工作流完成
                image_data = await self._wait_for_completion_via_api(prompt_id, api_url, timeout)
                
                if image_data:
                    return WorkflowResult(success=True, image_data=image_data, metadata={})
                else:
                    return WorkflowResult(success=False, error_message='工作流执行超时或失败')
                    
        except Exception as e:
            self.ap.logger.error(f"执行工作流失败: {str(e)}")
            return WorkflowResult(success=False, error_message=str(e))

    def _update_workflow_params(self, workflow: Dict[str, Any], params: Dict[str, Any], workflow_config) -> Dict[str, Any]:
        """更新工作流参数"""
        updated_workflow = workflow.copy()
        
        # 使用工作流配置中的参数映射
        param_mapping = workflow_config.param_mapping
        
        # 更新提示词
        if "prompt" in param_mapping and param_mapping["prompt"] in updated_workflow:
            node_id = param_mapping["prompt"]
            if "inputs" in updated_workflow[node_id] and "text" in updated_workflow[node_id]["inputs"]:
                updated_workflow[node_id]["inputs"]["text"] = params['prompt']
        
        # 更新采样器参数
        if "steps" in param_mapping and param_mapping["steps"] in updated_workflow:
            node_id = param_mapping["steps"]
            if "inputs" in updated_workflow[node_id]:
                inputs = updated_workflow[node_id]["inputs"]
                if "steps" in inputs:
                    inputs["steps"] = str(params['steps'])
                if "guidance" in inputs and "guidance" in params:
                    inputs["guidance"] = str(params['guidance'])
                if "seed" in inputs and params.get('seed', -1) > 0:
                    inputs["seed"] = str(params['seed'])
        
        # 更新图片尺寸
        if "width" in param_mapping and param_mapping["width"] in updated_workflow:
            node_id = param_mapping["width"]
            if "inputs" in updated_workflow[node_id]:
                inputs = updated_workflow[node_id]["inputs"]
                if "width" in inputs:
                    inputs["width"] = params['width']
                if "height" in inputs:
                    inputs["height"] = params['height']
        
        return updated_workflow

    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any], api_url: str) -> Optional[str]:
        """提交工作流到 ComfyUI"""
        try:
            async with session.post(
                f"{api_url}/prompt",
                json={"prompt": workflow},
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('prompt_id')
                else:
                    self.ap.logger.error(f"提交工作流失败: {response.status}")
                    return None
        except Exception as e:
            self.ap.logger.error(f"提交工作流出错: {str(e)}")
            return None

    async def _wait_for_completion_via_api(self, prompt_id: str, api_url: str, timeout: int) -> Optional[bytes]:
        """通过API等待工作流完成"""
        try:
            import aiohttp
            import asyncio
            
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{api_url}/history/{prompt_id}") as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            if prompt_id in result:
                                prompt_data = result[prompt_id]
                                status = prompt_data.get('status', {})
                                status_str = status.get('status_str', 'unknown')
                                
                                if status_str == 'error':
                                    self.ap.logger.error(f"工作流执行出错: {prompt_id}")
                                    return None
                                elif status_str == 'completed':
                                    # 获取生成的图片
                                    outputs = prompt_data.get('outputs', {})
                                    for node_id, node_output in outputs.items():
                                        if 'images' in node_output:
                                            for image_info in node_output['images']:
                                                filename = image_info.get('filename')
                                                if filename:
                                                    # 下载图片
                                                    image_data = await self._download_image(api_url, filename)
                                                    if image_data:
                                                        return image_data
                                    return None
                                
                        await asyncio.sleep(1)
            
            self.ap.logger.error(f"工作流执行超时: {prompt_id}")
            return None
            
        except Exception as e:
            self.ap.logger.error(f"等待工作流完成时出错: {str(e)}")
            return None

    async def _download_image(self, api_url: str, filename: str) -> Optional[bytes]:
        """下载图片"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{api_url}/view?filename={filename}") as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        self.ap.logger.error(f"下载图片失败: {response.status}")
                        return None
        except Exception as e:
            self.ap.logger.error(f"下载图片时出错: {str(e)}")
            return None

    async def _send_image_to_wechat(self, image_data: bytes, query: core_entities.Query) -> bool:
        """发送图片到微信"""
        temp_path = None
        try:
            self.ap.logger.info(f"开始发送图片到微信，图片大小: {len(image_data)} bytes")
            
            # 保存图片到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name
            
            # 创建微信图片消息
            image_message = platform_types.message.Image(path=temp_path)
            message_chain = platform_types.message.MessageChain([image_message])
            
            # 发送消息
            await query.adapter.reply_message(
                message_source=query.message_event,
                message=message_chain,
                quote_origin=False
            )
            
            # 删除临时文件
            if temp_path and os.path.exists(temp_path):
                os.unlink(temp_path)
            
            self.ap.logger.info("✅ 图片已成功发送到微信")
            return True
            
        except Exception as e:
            self.ap.logger.error(f"发送图片到微信出错: {str(e)}")
            
            # 确保临时文件被删除
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                except:
                    pass
            return False 