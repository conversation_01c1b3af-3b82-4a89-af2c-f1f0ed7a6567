"""
工作流路由数据模型
统一的工作流路由数据结构和枚举定义
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple
import time

from ..session.models import WorkflowType
from ..intent.models import ContentType, InputMode, QualityLevel


class WorkflowEnvironment(Enum):
    """工作流运行环境枚举"""
    LOCAL = "local"                    # 本地环境
    API = "api"                        # 远程API环境
    HYBRID = "hybrid"                  # 混合环境


class ImageRole(Enum):
    """图片角色枚举"""
    UNKNOWN = "unknown"                # 未知角色
    REFERENCE = "reference"            # 参考图像（风格、内容参考）
    CONTROL = "control"                # 控制图像（ControlNet）
    SKETCH = "sketch"                  # 草图/线稿
    MASK = "mask"                      # 蒙版图像
    DEPTH = "depth"                    # 深度图
    NORMAL = "normal"                  # 法线图
    POSE = "pose"                      # 姿态图
    CANNY = "canny"                    # Canny边缘图
    OPENPOSE = "openpose"              # OpenPose姿态图


class RouteStrategy(Enum):
    """路由策略枚举"""
    PREFIX_BASED = "prefix_based"      # 基于前缀指令
    INTENT_BASED = "intent_based"      # 基于意图分析
    IMAGE_BASED = "image_based"        # 基于图像分析
    HYBRID_ANALYSIS = "hybrid_analysis" # 混合分析
    DEFAULT = "default"                # 默认策略


@dataclass
class WorkflowRoute:
    """工作流路由配置"""
    workflow_type: WorkflowType
    environment: WorkflowEnvironment
    min_images: int = 0
    max_images: int = 10
    required_prompt: bool = True
    supports_text_only: bool = True
    priority: int = 0
    enabled: bool = True
    
    # 路由条件
    prefixes: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    content_types: List[ContentType] = field(default_factory=list)
    image_roles: List[ImageRole] = field(default_factory=list)
    
    # 工作流参数
    default_params: Dict[str, Any] = field(default_factory=dict)
    param_mapping: Dict[str, str] = field(default_factory=dict)
    
    def matches_prefix(self, text: str) -> bool:
        """检查文本是否匹配前缀"""
        text_lower = text.lower().strip()
        return any(text_lower.startswith(prefix.lower()) for prefix in self.prefixes)
    
    def matches_keywords(self, text: str) -> bool:
        """检查文本是否包含关键词"""
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in self.keywords)
    
    def matches_content_type(self, content_type: ContentType) -> bool:
        """检查内容类型是否匹配"""
        return not self.content_types or content_type in self.content_types
    
    def matches_image_count(self, image_count: int) -> bool:
        """检查图片数量是否匹配"""
        return self.min_images <= image_count <= self.max_images
    
    def calculate_score(self, context: 'RouteContext') -> float:
        """计算路由匹配分数"""
        score = 0.0
        
        # 前缀匹配（最高优先级）
        if self.matches_prefix(context.user_text):
            score += 100.0
        
        # 关键词匹配
        if self.matches_keywords(context.user_text):
            score += 50.0
        
        # 内容类型匹配
        if context.intent_analysis and self.matches_content_type(context.intent_analysis.content_type):
            score += 30.0
        
        # 图片数量匹配
        if self.matches_image_count(context.image_count):
            score += 20.0
        else:
            score -= 50.0  # 图片数量不匹配，严重降分
        
        # 图片角色匹配
        if context.image_roles:
            matching_roles = set(context.image_roles) & set(self.image_roles)
            if matching_roles:
                score += len(matching_roles) * 10.0
        
        # 优先级调整
        score += self.priority
        
        # 禁用路由得分为0
        if not self.enabled:
            score = 0.0
        
        return max(0.0, score)


@dataclass
class RouteContext:
    """路由上下文信息"""
    user_text: str
    image_count: int = 0
    image_data: List[bytes] = field(default_factory=list)
    image_roles: List[ImageRole] = field(default_factory=list)
    intent_analysis: Optional[Any] = None  # IntentAnalysis类型，避免循环导入
    
    # 会话信息
    user_id: str = ""
    chat_id: str = ""
    session_id: str = ""
    
    # 额外上下文
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


@dataclass 
class RoutingDecision:
    """路由决策结果"""
    workflow_type: WorkflowType
    environment: WorkflowEnvironment
    route: WorkflowRoute
    confidence: float
    reasoning: str
    strategy: RouteStrategy
    
    # 图片角色分配
    image_assignments: Dict[str, ImageRole] = field(default_factory=dict)
    
    # 建议参数
    suggested_params: Dict[str, Any] = field(default_factory=dict)
    suggested_prompt: str = ""
    
    # 决策元数据
    timestamp: float = field(default_factory=time.time)
    fallback_reason: str = ""
    alternative_routes: List[Tuple[WorkflowRoute, float]] = field(default_factory=list)
    
    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """是否高置信度决策"""
        return self.confidence >= threshold
    
    def get_route_info(self) -> Dict[str, Any]:
        """获取路由信息摘要"""
        return {
            'workflow_type': self.workflow_type.value,
            'environment': self.environment.value,
            'confidence': self.confidence,
            'strategy': self.strategy.value,
            'reasoning': self.reasoning,
            'image_count': len(self.image_assignments),
            'suggested_params': self.suggested_params
        }


@dataclass
class RouteResult:
    """路由执行结果"""
    success: bool
    decision: Optional[RoutingDecision] = None
    error_message: str = ""
    processing_time: float = 0.0
    
    # 分析结果
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'success': self.success,
            'processing_time': self.processing_time,
            'analysis_results': self.analysis_results
        }
        
        if self.decision:
            result['decision'] = self.decision.get_route_info()
        
        if self.error_message:
            result['error'] = self.error_message
        
        return result 


class TransmissionMode(Enum):
    API_ONLY = "api_only"
    WEBSOCKET_ONLY = "websocket_only"
    HYBRID = "hybrid" 