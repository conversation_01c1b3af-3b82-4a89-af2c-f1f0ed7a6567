apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: ollama-chat
  label:
    en_US: Ollama
    zh_Hans: Ollama
  icon: ollama.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "http://127.0.0.1:11434"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./ollamachat.py
    attr: OllamaChatCompletions
