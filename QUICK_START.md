# 🚀 LangBot 快速启动指南

## 📋 服务器重启后的启动步骤

### 方法1: 一键启动 (推荐)

```bash
cd /home/<USER>/Workspace/langbot
./start-all-services.sh
```

### 方法2: 手动启动

```bash
cd /home/<USER>/Workspace/langbot

# 1. 启动 WeChatPadPro
docker-compose -f wechatpad-docker-compose.yml up -d

# 2. 等待15秒，然后启动 LangBot
docker run -d --name langbot --network host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/pkg:/app/pkg \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/plugins:/app/plugins \
  -v $(pwd)/workflows:/app/workflows \
  -v $(pwd)/templates:/app/templates \
  -v $(pwd)/res:/app/res \
  --restart on-failure \
  -e TZ=Asia/Shanghai \
  docker.langbot.app/langbot-public/rockchin/langbot:latest
```

## 🌐 访问地址

- **微信管理**: http://localhost:1239 (扫码登录)
- **LangBot管理**: http://localhost:5300

## 🛠️ 常用命令

```bash
# 启动所有服务
./start-all-services.sh

# 停止所有服务  
./stop-all-services.sh

# 查看服务状态
docker ps | grep -E "(langbot|wechatpadpro)"

# 查看日志
docker logs langbot -f
docker logs wechatpadpro -f
```

## ⚠️ 重要提醒

1. **启动顺序**: 必须先启动 WeChatPadPro，再启动 LangBot
2. **微信登录**: 服务启动后需要访问 http://localhost:1239 重新扫码登录
3. **网络模式**: LangBot 使用 host 网络模式确保与 WeChatPadPro 通信

---
详细说明请查看: `docs/deployment/SERVER_RESTART_GUIDE.md` 