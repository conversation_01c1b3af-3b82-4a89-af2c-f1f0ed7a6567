{"trigger": {"group-respond-rules": {"at": false, "atbot-with-prefix": true, "prefix": ["aigen", "kontext", "kontext api"], "regexp": [], "random": 0.0}, "access-control": {"mode": "blacklist", "blacklist": [], "whitelist": []}, "ignore-rules": {"prefix": [], "regexp": []}, "misc": {"combine-quote-message": true}}, "safety": {"content-filter": {"scope": "all", "check-sensitive-words": true}, "rate-limit": {"window-length": 60, "limitation": 60, "strategy": "drop"}}, "ai": {"runner": {"runner": "comfyui-agent"}, "local-agent": {"model": "", "max-round": 10, "prompt": [{"role": "system", "content": "You are a helpful assistant."}]}, "dify-service-api": {"base-url": "https://api.dify.ai/v1", "app-type": "chat", "api-key": "your-api-key", "thinking-convert": "plain", "timeout": 30}, "dashscope-app-api": {"app-type": "agent", "api-key": "your-api-key", "app-id": "your-app-id", "references-quote": "参考资料来自:"}, "n8n-service-api": {"webhook-url": "http://your-n8n-webhook-url", "auth-type": "none", "basic-username": "", "basic-password": "", "jwt-secret": "", "jwt-algorithm": "HS256", "header-name": "", "header-value": "", "timeout": 120, "output-key": "response"}, "comfyui-agent": {"enabled": true, "api-url": "http://localhost:8188", "timeout": 180, "workflow-path": "workflows", "default-workflow": "default_workflow.json", "admin-sync-enabled": false, "kontext-enabled": true, "max-queue-size": 5, "repeat-detection-threshold": 4}}, "output": {"long-text-processing": {"threshold": 1000, "strategy": "forward", "font-path": ""}, "force-delay": {"min": 0, "max": 0}, "misc": {"hide-exception": true, "at-sender": true, "quote-origin": true, "track-function-calls": false}}}