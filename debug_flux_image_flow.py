#!/usr/bin/env python3
"""
调试Flux工作流图片数据传递过程
检查从会话到工作流的完整流程
"""

import base64
import json
import os
import sys
from typing import Optional, List

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

def validate_base64_image(base64_str: str) -> tuple[bool, str]:
    """
    验证base64图片数据的有效性
    """
    try:
        if not base64_str or len(base64_str.strip()) == 0:
            return False, "base64字符串为空"
        
        # 移除可能的data URL前缀
        if ',' in base64_str:
            base64_str = base64_str.split(',', 1)[1]
        
        # 检查长度
        if len(base64_str) < 100:
            return False, f"base64字符串过短: {len(base64_str)} 字符"
        
        # 尝试解码
        try:
            image_data = base64.b64decode(base64_str)
        except Exception as e:
            return False, f"base64解码失败: {e}"
        
        # 检查解码后的数据
        if len(image_data) == 0:
            return False, "解码后图片数据为空"
        
        if len(image_data) < 100:
            return False, f"图片文件过小: {len(image_data)} bytes"
        
        # 检查图片格式头部
        if image_data.startswith(b'\xff\xd8\xff'):
            format_name = "JPEG"
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            format_name = "PNG"
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            format_name = "GIF"
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
            format_name = "WEBP"
        else:
            return False, f"不支持的图片格式，文件头: {image_data[:16].hex()}"
        
        return True, f"有效 {format_name} 图片，大小: {len(image_data)} bytes"
        
    except Exception as e:
        return False, f"验证过程异常: {e}"

def check_workflow_image_nodes(workflow_data: dict) -> None:
    """
    检查工作流中的图片节点
    """
    print("🔍 检查工作流图片节点:")
    
    image_nodes = []
    for node_id, node_data in workflow_data.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "")
            if class_type in ["easy loadImageBase64", "LoadImage"]:
                title = node_data.get("_meta", {}).get("title", "")
                inputs = node_data.get("inputs", {})
                image_nodes.append((node_id, title, class_type, inputs))
    
    print(f"📷 找到 {len(image_nodes)} 个图片输入节点:")
    
    for node_id, title, class_type, inputs in image_nodes:
        print(f"\n  🔸 节点 {node_id}: {title} ({class_type})")
        
        if class_type == "easy loadImageBase64":
            base64_data = inputs.get("base64_data", "")
            if base64_data:
                is_valid, message = validate_base64_image(base64_data)
                if is_valid:
                    print(f"    ✅ {message}")
                else:
                    print(f"    ❌ {message}")
                    print(f"    📝 base64数据预览: {base64_data[:50]}...")
            else:
                print(f"    ⚠️  base64_data 为空")
        
        # 显示所有输入参数
        print(f"    📋 输入参数:")
        for key, value in inputs.items():
            if key == "base64_data" and value:
                preview = value[:30] + "..." if len(value) > 30 else value
                print(f"      {key}: {preview}")
            else:
                print(f"      {key}: {value}")

def simulate_flux_image_application(workflow_file: str, test_image_data: bytes) -> None:
    """
    模拟Flux图片应用过程
    """
    print(f"\n🧪 模拟Flux图片应用过程:")
    print(f"📁 工作流文件: {workflow_file}")
    print(f"📷 测试图片大小: {len(test_image_data)} bytes")
    
    # 加载工作流模板
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return
    
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        print(f"✅ 成功加载工作流模板，包含 {len(workflow_data)} 个节点")
        
        # 检查原始工作流
        print("\n📋 原始工作流图片节点:")
        check_workflow_image_nodes(workflow_data)
        
        # 模拟图片应用过程
        print("\n🔄 模拟图片应用过程:")
        
        # 查找ControlNet图片输入节点
        target_node_id = None
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and 'class_type' in node_data and '_meta' in node_data:
                title = node_data['_meta'].get('title', '')
                if title == 'controlnet_image_input':
                    target_node_id = node_id
                    print(f"✅ 找到ControlNet图片输入节点: {node_id}")
                    break
        
        if target_node_id:
            # 将图片转换为base64
            image_base64 = base64.b64encode(test_image_data).decode('utf-8')
            print(f"✅ 图片转换为base64，长度: {len(image_base64)} 字符")
            
            # 验证base64数据
            is_valid, message = validate_base64_image(image_base64)
            if is_valid:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                return
            
            # 应用图片到工作流
            workflow_data[target_node_id]["inputs"]["base64_data"] = image_base64
            print(f"✅ 成功应用图片到节点 {target_node_id}")
            
            # 检查应用后的工作流
            print("\n📋 应用图片后的工作流节点:")
            check_workflow_image_nodes(workflow_data)
            
            # 模拟提交到ComfyUI
            print("\n🚀 模拟提交到ComfyUI:")
            
            # 检查工作流数据是否完整
            prompt_data = {"prompt": workflow_data}
            
            # 验证关键节点
            if target_node_id in workflow_data:
                node_data = workflow_data[target_node_id]
                inputs = node_data.get("inputs", {})
                base64_data = inputs.get("base64_data", "")
                
                if base64_data:
                    print(f"✅ 节点 {target_node_id} 包含有效的base64数据")
                    print(f"📝 数据长度: {len(base64_data)} 字符")
                    
                    # 再次验证
                    is_valid, message = validate_base64_image(base64_data)
                    if is_valid:
                        print(f"✅ 最终验证: {message}")
                    else:
                        print(f"❌ 最终验证失败: {message}")
                else:
                    print(f"❌ 节点 {target_node_id} 的base64_data为空")
            else:
                print(f"❌ 找不到目标节点 {target_node_id}")
            
        else:
            print("❌ 未找到ControlNet图片输入节点")
    
    except Exception as e:
        print(f"❌ 模拟过程失败: {e}")

def test_session_image_objects():
    """
    测试SessionImage对象
    """
    print("\n🧪 测试SessionImage对象:")
    
    try:
        from pkg.core.session.models import SessionImage, FluxImageType
        
        # 创建测试图片数据
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        # 创建SessionImage对象
        session_image = SessionImage(
            data=test_image_data,
            purpose="control",
            source="upload",
            flux_image_type=FluxImageType.CONTROL
        )
        
        print(f"✅ 成功创建SessionImage对象")
        print(f"📷 图片大小: {session_image.get_size()} bytes")
        print(f"🔗 图片哈希: {session_image.get_hash()}")
        print(f"🎯 图片类型: {session_image.flux_image_type.value}")
        print(f"📋 是否为控制图: {session_image.is_control_image()}")
        print(f"📋 是否为参考图: {session_image.is_reference_image()}")
        
        # 测试base64转换
        image_base64 = base64.b64encode(session_image.data).decode('utf-8')
        print(f"✅ 成功转换为base64，长度: {len(image_base64)} 字符")
        
        # 验证base64数据
        is_valid, message = validate_base64_image(image_base64)
        if is_valid:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
        
        return session_image
        
    except Exception as e:
        print(f"❌ 测试SessionImage对象失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 Flux工作流图片数据调试工具")
    print("=" * 60)
    
    # 测试SessionImage对象
    test_session_image = test_session_image_objects()
    
    if test_session_image:
        # 模拟图片应用过程
        workflow_files = [
            "workflows/flux_controlnet.json",
            "workflows/flux_controlnet_redux.json"
        ]
        
        for workflow_file in workflow_files:
            simulate_flux_image_application(workflow_file, test_session_image.data)
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    main() 