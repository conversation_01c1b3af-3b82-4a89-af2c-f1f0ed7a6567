#!/usr/bin/env python3
"""
二次开发功能测试运行脚本
专门测试重构后的核心模块功能，不修改原生代码
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 确保在正确的目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 设置Python路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', str(project_root))
    os.environ.setdefault('TESTING', 'true')
    
    print(f"✅ 测试环境设置完成，项目根目录: {project_root}")

def run_unit_tests():
    """运行单元测试"""
    print("\n🧪 运行单元测试...")
    
    test_files = [
        "tests/integration/test_custom_features.py",
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"📋 运行测试文件: {test_file}")
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pytest", 
                    test_file, 
                    "-v", 
                    "--tb=short",
                    "--no-header"
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"✅ {test_file} 测试通过")
                else:
                    print(f"❌ {test_file} 测试失败")
                    print("错误输出:")
                    print(result.stderr)
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_file} 测试超时")
            except Exception as e:
                print(f"💥 {test_file} 测试异常: {e}")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")

def run_integration_tests():
    """运行集成测试"""
    print("\n🔗 运行集成测试...")
    
    # 运行自定义功能集成测试
    test_file = "tests/integration/test_custom_features.py"
    if os.path.exists(test_file):
        print(f"📋 运行集成测试: {test_file}")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                test_file,
                "-v",
                "--tb=short",
                "--no-header",
                "-m", "not slow"  # 排除慢速测试
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print(f"✅ 集成测试通过")
                print("测试输出:")
                print(result.stdout)
            else:
                print(f"❌ 集成测试失败")
                print("错误输出:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 集成测试超时")
        except Exception as e:
            print(f"💥 集成测试异常: {e}")
    else:
        print(f"⚠️  集成测试文件不存在: {test_file}")

def run_coverage_tests():
    """运行覆盖率测试"""
    print("\n📊 运行覆盖率测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/integration/test_custom_features.py",
            "--cov=pkg.core",
            "--cov-report=html:htmlcov/custom",
            "--cov-report=term-missing",
            "-v"
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 覆盖率测试完成")
            print("覆盖率报告:")
            print(result.stdout)
        else:
            print("❌ 覆盖率测试失败")
            print("错误输出:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ 覆盖率测试超时")
    except Exception as e:
        print(f"💥 覆盖率测试异常: {e}")

def check_core_modules():
    """检查核心模块是否可用"""
    print("\n🔍 检查核心模块...")
    
    core_modules = [
        "pkg.core.session.manager",
        "pkg.core.image.processor", 
        "pkg.core.intent.analyzer",
        "pkg.core.workflow.router",
        "pkg.core.message.processor"
    ]
    
    for module_name in core_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 可用")
        except ImportError as e:
            print(f"❌ {module_name} 不可用: {e}")
        except Exception as e:
            print(f"⚠️  {module_name} 检查异常: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="二次开发功能测试运行器")
    parser.add_argument("--unit", action="store_true", help="运行单元测试")
    parser.add_argument("--integration", action="store_true", help="运行集成测试")
    parser.add_argument("--coverage", action="store_true", help="运行覆盖率测试")
    parser.add_argument("--check", action="store_true", help="检查核心模块")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    
    args = parser.parse_args()
    
    print("🚀 二次开发功能测试运行器")
    print("=" * 50)
    
    # 设置测试环境
    setup_test_environment()
    
    # 检查核心模块
    if args.check or args.all:
        check_core_modules()
    
    # 运行测试
    if args.unit or args.all:
        run_unit_tests()
    
    if args.integration or args.all:
        run_integration_tests()
    
    if args.coverage or args.all:
        run_coverage_tests()
    
    # 如果没有指定参数，默认运行所有测试
    if not any([args.unit, args.integration, args.coverage, args.check, args.all]):
        print("\n📋 未指定测试类型，运行所有测试...")
        check_core_modules()
        run_unit_tests()
        run_integration_tests()
        run_coverage_tests()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main() 