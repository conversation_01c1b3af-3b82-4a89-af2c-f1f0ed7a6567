#!/usr/bin/env python3
"""
调试ComfyUI工作流提交过程
检查图片数据是否正确传递到ComfyUI
"""

import base64
import json
import os
import sys
import aiohttp
import asyncio
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

async def test_comfyui_connection():
    """测试ComfyUI连接"""
    print("🔗 测试ComfyUI连接:")
    
    api_url = "http://localhost:8188"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试基本连接
            async with session.get(f"{api_url}/system_stats") as response:
                if response.status == 200:
                    stats = await response.json()
                    print(f"✅ ComfyUI连接正常")
                    print(f"📊 系统状态: {stats}")
                    return True
                else:
                    print(f"❌ ComfyUI连接失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ ComfyUI连接异常: {e}")
        return False

def create_test_workflow_with_image(image_base64: str) -> Dict[str, Any]:
    """创建包含图片的测试工作流"""
    workflow = {
        "210": {
            "inputs": {
                "base64_data": image_base64,
                "image_output": "Preview",
                "save_prefix": "ComfyUI"
            },
            "class_type": "easy loadImageBase64",
            "_meta": {
                "title": "controlnet_image_input"
            }
        },
        "211": {
            "inputs": {
                "images": ["210", 0]
            },
            "class_type": "PreviewImage",
            "_meta": {
                "title": "Preview Image"
            }
        }
    }
    return workflow

async def submit_workflow_to_comfyui(workflow_data: Dict[str, Any]) -> Optional[str]:
    """提交工作流到ComfyUI"""
    api_url = "http://localhost:8188"
    
    try:
        prompt_data = {"prompt": workflow_data}
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    print(f"✅ 工作流提交成功: {prompt_id}")
                    return prompt_id
                else:
                    error_text = await response.text()
                    print(f"❌ 提交工作流失败: {response.status}, {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ 提交工作流异常: {e}")
        return None

async def check_workflow_status(prompt_id: str) -> bool:
    """检查工作流状态"""
    api_url = "http://localhost:8188"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{api_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history_data = await response.json()
                    prompt_data = history_data.get(prompt_id, {})
                    
                    # 检查执行状态
                    if 'outputs' in prompt_data:
                        print(f"✅ 工作流执行完成")
                        return True
                    else:
                        print(f"⏳ 工作流正在执行中...")
                        return False
                else:
                    print(f"❌ 获取工作流状态失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 检查工作流状态异常: {e}")
        return False

def validate_base64_image(base64_str: str) -> tuple[bool, str]:
    """验证base64图片数据的有效性"""
    try:
        if not base64_str or len(base64_str.strip()) == 0:
            return False, "base64字符串为空"
        
        # 移除可能的data URL前缀
        if ',' in base64_str:
            base64_str = base64_str.split(',', 1)[1]
        
        # 检查长度
        if len(base64_str) < 100:
            return False, f"base64字符串过短: {len(base64_str)} 字符"
        
        # 尝试解码
        try:
            image_data = base64.b64decode(base64_str)
        except Exception as e:
            return False, f"base64解码失败: {e}"
        
        # 检查解码后的数据
        if len(image_data) == 0:
            return False, "解码后图片数据为空"
        
        if len(image_data) < 100:
            return False, f"图片文件过小: {len(image_data)} bytes"
        
        # 检查图片格式头部
        if image_data.startswith(b'\xff\xd8\xff'):
            format_name = "JPEG"
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            format_name = "PNG"
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            format_name = "GIF"
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
            format_name = "WEBP"
        else:
            return False, f"不支持的图片格式，文件头: {image_data[:16].hex()}"
        
        return True, f"有效 {format_name} 图片，大小: {len(image_data)} bytes"
        
    except Exception as e:
        return False, f"验证过程异常: {e}"

async def main():
    """主函数"""
    print("🔍 ComfyUI工作流提交调试工具")
    print("=" * 60)
    
    # 1. 测试ComfyUI连接
    if not await test_comfyui_connection():
        print("❌ ComfyUI连接失败，无法继续测试")
        return
    
    # 2. 创建测试图片数据
    print("\n📷 创建测试图片数据:")
    test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
    
    # 转换为base64
    image_base64 = base64.b64encode(test_image_data).decode('utf-8')
    print(f"✅ 图片大小: {len(test_image_data)} bytes")
    print(f"✅ base64长度: {len(image_base64)} 字符")
    
    # 验证base64数据
    is_valid, message = validate_base64_image(image_base64)
    if is_valid:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
        return
    
    # 3. 创建测试工作流
    print("\n🔧 创建测试工作流:")
    workflow_data = create_test_workflow_with_image(image_base64)
    print(f"✅ 工作流包含 {len(workflow_data)} 个节点")
    
    # 检查工作流中的图片数据
    node_210 = workflow_data.get("210", {})
    inputs = node_210.get("inputs", {})
    base64_data = inputs.get("base64_data", "")
    
    if base64_data:
        print(f"✅ 节点210包含base64数据，长度: {len(base64_data)} 字符")
        
        # 再次验证
        is_valid, message = validate_base64_image(base64_data)
        if is_valid:
            print(f"✅ 工作流中的图片数据有效: {message}")
        else:
            print(f"❌ 工作流中的图片数据无效: {message}")
            return
    else:
        print(f"❌ 节点210的base64_data为空")
        return
    
    # 4. 提交工作流到ComfyUI
    print("\n🚀 提交工作流到ComfyUI:")
    prompt_id = await submit_workflow_to_comfyui(workflow_data)
    
    if not prompt_id:
        print("❌ 工作流提交失败")
        return
    
    # 5. 等待并检查工作流状态
    print("\n⏳ 等待工作流执行:")
    for i in range(10):  # 最多等待10次
        await asyncio.sleep(1)
        if await check_workflow_status(prompt_id):
            break
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    asyncio.run(main()) 