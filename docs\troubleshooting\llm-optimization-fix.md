# LLM优化功能修复说明

## 问题描述

在Kontext工作流中发现重复的LLM优化调用问题，导致：
1. 同一个prompt被优化两次，浪费资源
2. 可能产生不一致的优化结果
3. 用户体验不佳（看到重复的优化过程）

## 修复内容

### 1. 修复重复LLM优化调用

**问题位置**：
- `kontext_image_handler.py` 中的 `handle_kontext_session_interaction` 方法
- `_execute_kontext_workflow` 方法

**修复方案**：
- 在 `handle_kontext_session_interaction` 中先进行LLM优化
- 调用 `_execute_kontext_workflow` 时设置 `skip_optimization=True`
- 确保每个prompt只被优化一次

### 2. 统一优化调用策略

**修复后的调用逻辑**：

```python
# 1. 正常生成流程
if kontext_manager.is_generation_trigger(user_text):
    # 先优化提示词
    optimized_prompt = await self._optimize_kontext_prompt_with_llm(session.prompt, query)
    session.prompt = optimized_prompt
    
    # 执行工作流，跳过重复优化
    async for result_message in self._execute_kontext_workflow(session, query, skip_optimization=True):
        yield result_message

# 2. 再次生成流程（使用上次参数）
async for result_message in self._execute_kontext_workflow(session, query, skip_optimization=True):
    yield result_message

# 3. 修改提示词流程（需要重新优化）
async for result_message in self._execute_kontext_workflow(session, query, skip_optimization=False):
    yield result_message
```

### 3. 增强日志记录

**新增日志**：
- 优化开始和完成的日志
- 跳过优化的原因说明
- 最终使用的提示词记录

```python
if not skip_optimization:
    self.ap.logger.info(f"开始优化Kontext提示词: {session.prompt}")
    optimized_prompt = await self._optimize_kontext_prompt_with_llm(session.prompt, query)
    session.prompt = optimized_prompt
    self.ap.logger.info(f"Kontext提示词优化完成: {optimized_prompt}")
else:
    self.ap.logger.info(f"跳过LLM优化，使用现有提示词: {session.prompt}")
```

## 修复后的工作流程

### 标准图片生成工作流
✅ **正常**：使用 `StandardImageHandler.analyze_params_with_llm()` 进行LLM优化

### Kontext工作流
✅ **已修复**：
1. **首次生成**：LLM优化 → 执行工作流（跳过重复优化）
2. **再次生成**：直接执行工作流（跳过优化，使用上次结果）
3. **修改提示词**：重新LLM优化 → 执行工作流（跳过重复优化）

## 验证方法

1. **检查日志**：确认没有重复的"开始优化Kontext提示词"日志
2. **测试流程**：
   - 发送 `kontext 测试提示词` 启动工作流
   - 上传图片后发送 `开始` 指令
   - 检查是否只看到一次优化过程
3. **再次生成**：发送 `again` 指令，确认跳过优化
4. **修改提示词**：发送 `修改提示词为xxx`，确认重新优化

## 注意事项

1. **LLM模型配置**：确保pipeline配置中正确设置了LLM模型UUID
2. **错误处理**：如果LLM优化失败，会自动降级到基于规则的优化
3. **性能优化**：避免重复调用LLM，提高响应速度

## 相关文件

- `pkg/provider/runners/kontext_image_handler.py` - 主要修复文件
- `pkg/provider/runners/standard_image_handler.py` - 标准工作流（无需修改）
- `pkg/provider/runners/comfyui_agent.py` - 主入口（无需修改） 