"""
Kontext 工作流执行模块
负责Kontext相关的工作流执行、API调用、错误处理等
"""
from typing import Dict, Any, List
import random
from .kontext_base_executor import BaseKontextExecutor
from .local_executor import LocalKontextExecutor
from .api_executor import ApiKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class KontextWorkflowExecutor:
    """
    Kontext工作流调度与执行
    """
    def __init__(self, mode: str = 'local', api_url: str | None = None, timeout: int = 180):
        executor: BaseKontextExecutor
        if mode == 'local':
            # 本地模式使用本地ComfyUI
            local_api_url = api_url or "http://localhost:8188"
            executor = LocalKontextExecutor(api_url=local_api_url, timeout=timeout)
        else:
            # API模式使用远程ComfyUI
            remote_api_url = api_url or "https://api.comfyui.com"
            executor = ApiKontextExecutor(api_url=remote_api_url, timeout=timeout)
        self.executor = executor
        self.mode = mode

    async def prepare_workflow_data(self, config: Dict[str, Any], images: List[bytes], prompt: str, aspect_ratio: str, generation_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        组装最终的工作流数据
        """
        # 生成随机种子
        seed = random.randint(100000000000000, 999999999999999)
        
        # 组装参数
        params = {
            'prompt': prompt,
            'aspect_ratio': aspect_ratio,
            'guidance': generation_params.get('guidance', 4.0),
            'steps': generation_params.get('steps', 60),
            'seed': seed,
            'prompt_upsampling': generation_params.get('prompt_upsampling', False),
            'images': images
        }
        
        # 添加负面提示词（如果有）
        if 'negative_prompt' in generation_params:
            params['negative_prompt'] = generation_params['negative_prompt']
        
        return params

    async def execute_workflow(self, workflow_file: str, params: Dict[str, Any], *args, **kwargs) -> WorkflowResult:
        """
        调用底层executor执行工作流
        """
        try:
            result = await self.executor.execute_workflow(workflow_file, params, *args, **kwargs)
            return result if result is not None else WorkflowResult(success=False, error_message='执行器返回空结果')
        except Exception as e:
            return WorkflowResult(success=False, error_message=f'执行工作流失败: {str(e)}')
    
    async def close(self):
        """关闭执行器"""
        if hasattr(self.executor, 'close'):
            await self.executor.close()

kontext_workflow_executor = KontextWorkflowExecutor() 