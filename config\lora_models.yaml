# Lora模型分类配置文件
# 用于工作流智能选择和管理Lora模型

# 建筑风格类
architecture:
  name: "建筑风格"
  description: "专业建筑设计和渲染风格"
  models:
    - name: "ASTRA_Flux_OC_Vbeta-2"
      file: "ASTRA_Flux_OC_Vbeta-2.safetensors"
      description: "ASTRA建筑渲染风格"
      model_type: "flux"
      strength: 0.75
      enabled: true
      tags: ["建筑", "渲染", "专业"]
    
    - name: "黑格建筑效果表现"
      file: "黑格建筑效果表现FLUX_new.safetensors"
      description: "黑格建筑效果表现风格"
      model_type: "flux"
      strength: 0.8
      enabled: true
      tags: ["建筑", "效果图", "黑格"]
    
    - name: "artchitecture_Flux1"
      file: "artchitecture_Flux1.safetensors"
      description: "Flux1建筑艺术风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["建筑", "艺术", "Flux1"]

# 比鲁斯系列
billus_series:
  name: "比鲁斯系列"
  description: "比鲁斯专业建筑模型系列"
  models:
    - name: "比鲁斯商业建筑"
      file: "比鲁斯商业建筑_V0.2.safetensors"
      description: "比鲁斯商业建筑风格V0.2"
      model_type: "flux"
      strength: 0.75
      enabled: true
      tags: ["商业建筑", "比鲁斯", "现代"]
    
    - name: "比鲁斯商业建筑现代"
      file: "比鲁斯商业建筑modern architecture_V0.2.safetensors"
      description: "比鲁斯现代商业建筑风格"
      model_type: "flux"
      strength: 0.75
      enabled: true
      tags: ["商业建筑", "现代", "比鲁斯"]
    
    - name: "比鲁斯现代酒店"
      file: "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors"
      description: "比鲁斯现代酒店设计风格"
      model_type: "sdxl"
      strength: 0.8
      enabled: true
      tags: ["酒店", "现代", "比鲁斯"]
    
    - name: "比鲁斯别墅度假酒店"
      file: "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors"
      description: "比鲁斯别墅度假酒店风格"
      model_type: "sdxl"
      strength: 0.8
      enabled: true
      tags: ["别墅", "度假", "比鲁斯"]
    
    - name: "比鲁斯中古室内"
      file: "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors"
      description: "比鲁斯中古室内风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["室内", "中古", "比鲁斯"]

# 住宅类
residential:
  name: "住宅设计"
  description: "住宅和别墅设计风格"
  models:
    - name: "加州别墅"
      file: "加州别墅-1.0.safetensors"
      description: "加州别墅设计风格"
      model_type: "flux"
      strength: 0.75
      enabled: true
      tags: ["别墅", "加州", "住宅"]
    
    - name: "NeoChinese现代室内"
      file: "NeoChineseModernInterior.safetensors"
      description: "新中式现代室内设计"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["室内", "新中式", "现代"]

# 细节增强类
detail_enhancement:
  name: "细节增强"
  description: "提升图片细节和质量的模型"
  models:
    - name: "detail_aidmafluxpro"
      file: "detail_aidmafluxproultra-FLUX-v0.1.safetensors"
      description: "FLUX细节增强模型"
      model_type: "flux"
      strength: 0.6
      enabled: true
      tags: ["细节", "增强", "FLUX"]
    
    - name: "add-detail-xl"
      file: "add-detail-xl.safetensors"
      description: "SDXL细节增强模型"
      model_type: "sdxl"
      strength: 0.5
      enabled: false  # 已禁用，存在兼容性问题
      tags: ["细节", "增强", "SDXL"]

# 艺术风格类
artistic_styles:
  name: "艺术风格"
  description: "各种艺术和渲染风格"
  models:
    - name: "Minimalism_Flux"
      file: "Minimalism_Flux.safetensors"
      description: "Flux极简主义风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["极简", "艺术", "Flux"]

    - name: "ShallowDepth"
      file: "ShallowDepth.safetensors"
      description: "浅深度控制"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["浅景深", "镜头模糊", "小景深"]

    - name: "flat2100"
      file: "flat2100.safetensors"
      description: "扁平化2100风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["扁平化", "勾线平涂", "平面化"]
    

# 动漫风格类
anime:
  name: "动漫风格"
  description: "动漫和二次元风格"
  models:
    - name: "Anime_niji"
      file: "Anime_niji.safetensors"
      description: "Niji动漫风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["动漫", "Niji"]
    
    - name: "niji_flux"
      file: "niji_flux.safetensors"
      description: "Niji Flux风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["动漫", "Niji", "Flux"]
    
    - name: "Niji半写实"
      file: "Niji_semi-realism_flux.safetensors"
      description: "Niji半写实风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["动漫", "半写实", "Niji"]

    - name: "Flux Flat Anime"
      file: "Flux Flat Anime.safetensors"
      description: "Flux扁平化动漫风格"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["动漫", "扁平化", "Flux"]

# 实用工具类
utility:
  name: "实用工具"
  description: "功能性工具模型"
  models:
    - name: "outfit-generator"
      file: "outfit-generator.safetensors"
      description: "服装生成器"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["服装", "生成器"]
    
    - name: "goodhands"
      file: "goodhands_Beta_Gtonero.safetensors"
      description: "手部优化模型"
      model_type: "flux"
      strength: 0.6
      enabled: true
      tags: ["手部", "优化"]

# 快速生成类
fast_generation:
  name: "快速生成"
  description: "快速生成和优化模型"
  models:
    - name: "Hyper-FLUX-8steps"
      file: "Hyper-FLUX.1-dev-8steps-lora.safetensors"
      description: "Hyper FLUX 8步快速生成"
      model_type: "flux"
      strength: 0.8
      enabled: true
      tags: ["快速", "8步", "Hyper"]
    
    - name: "Flux Turbo"
      file: "Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors"
      description: "Flux Turbo加速模型"
      model_type: "flux"
      strength: 0.7
      enabled: true
      tags: ["加速", "Turbo", "Flux"]
    
    - name: "SDXL-Lightning"
      file: "SDXL-Lightning/sdxl_lightning_8step_lora.safetensors"
      description: "SDXL Lightning 8步快速生成"
      model_type: "sdxl"
      strength: 0.8
      enabled: true
      tags: ["快速", "8步", "SDXL", "Lightning"]
    
    - name: "LCM SD1.5"
      file: "lcm/SD1.5/pytorch_lora_weights.safetensors"
      description: "LCM SD1.5快速生成"
      model_type: "sd15"
      strength: 0.8
      enabled: true
      tags: ["快速", "LCM", "SD1.5"]

# 特殊效果类
special_effects:
  name: "特殊效果"
  description: "特殊渲染和效果模型"
  models:
    
    - name: "Flux_RA_curtainWwall"
      file: "Flux_RA_curtainWwall_rk16_bf16.safetensors"
      description: "Flux窗帘墙效果"
      model_type: "flux"
      strength: 0.6
      enabled: true
      tags: ["窗帘", "墙", "Flux"]
    
    - name: "flux-RA-NewShikumen"
      file: "flux-RA-NewShikumen.safetensors"
      description: "Flux新石库门风格"
      model_type: "flux"
      strength: 0.6
      enabled: true
      tags: ["石库门", "砖墙", "老建筑"]

# SDXL专用类
sdxl_specific:
  name: "SDXL专用"
  description: "专门为SDXL优化的模型"
  models:
    - name: "ASTRA_KC_Interior_Living"
      file: "SDXL/ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors"
      description: "ASTRA室内生活卧室极简风格"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["室内", "卧室", "极简", "SDXL"]
    
    - name: "ASTRA_KC_Urban_Renewal"
      file: "SDXL/ASTRA_KC_Urban_Renewal_V1.1.safetensors"
      description: "ASTRA城市更新风格"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["城市", "更新", "SDXL"]
    
    - name: "ASTRA_KC_XL_PlantGroups"
      file: "SDXL/ASTRA_KC_XL_PlantGroups_V1.safetensors"
      description: "ASTRA植物群组风格"
      model_type: "sdxl"
      strength: 0.6
      enabled: true
      tags: ["植物", "群组", "SDXL"]
    
    - name: "ASTRA_KC_XL_Residential"
      file: "SDXL/ASTRA_KC_XL_Residential_V1.safetensors"
      description: "ASTRA住宅风格"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["住宅", "SDXL"]
    
    - name: "ASTRA_KC_XL_RSD_Landscape"
      file: "SDXL/ASTRA_KC_XL_RSD_Landscape_V1.safetensors"
      description: "ASTRA景观设计风格"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["景观", "设计", "SDXL"]
    
    - name: "ASTRA_XL_AerialView_Photography"
      file: "SDXL/ASTRA_XL_AerialView_Photography_V1.safetensors"
      description: "ASTRA航拍摄影风格"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["航拍", "摄影", "SDXL"]
    
    - name: "ASTRA_XL_Artistic_Rendering"
      file: "SDXL/ASTRA_XL_Artistic_Rendering_Style_V2.safetensors"
      description: "ASTRA艺术渲染风格V2"
      model_type: "sdxl"
      strength: 0.7
      enabled: true
      tags: ["艺术", "渲染", "SDXL"]
    
    - name: "ASTRA_XL_OC_V3"
      file: "SDXL/ASTRA_XL_OC_V3.safetensors"
      description: "ASTRA建筑渲染V3"
      model_type: "sdxl"
      strength: 0.75
      enabled: true
      tags: ["建筑", "渲染", "SDXL"]

# 全局配置
global_config:
  default_strength: 0.7
  max_strength: 1.0
  min_strength: 0.1
  auto_enable_new_models: false
  enable_civitai_integration: true
  civitai_api_key: ""  # 从环境变量或配置文件读取
  
  # 模型类型说明
  model_types:
    flux: "Flux模型 - 当前工作流使用的基础模型"
    sdxl: "SDXL模型 - 需要SDXL基础模型支持"
    sd15: "SD1.5模型 - 需要SD1.5基础模型支持"
  
  # 当前工作流类型
  current_workflow_type: "flux"
  
# 工作流预设
workflow_presets:
  architecture_rendering:
    name: "建筑渲染"
    description: "专业建筑效果图渲染"
    model_type: "flux"  # 指定工作流类型
    recommended_models:
      - "ASTRA_Flux_OC_Vbeta-2"
      - "黑格建筑效果表现"
      - "detail_aidmafluxpro"
    strength_multiplier: 1.0
  
  residential_design:
    name: "住宅设计"
    description: "住宅和别墅设计"
    model_type: "flux"  # 指定工作流类型
    recommended_models:
      - "比鲁斯商业建筑"
      - "加州别墅"
      - "NeoChinese现代室内"
    strength_multiplier: 0.9
  
  fast_generation:
    name: "快速生成"
    description: "快速生成模式"
    model_type: "flux"  # 指定工作流类型
    recommended_models:
      - "Hyper-FLUX-8steps"
      - "Flux Turbo"
    strength_multiplier: 1.1
  
  anime_style:
    name: "动漫风格"
    description: "动漫和二次元风格"
    model_type: "flux"  # 指定工作流类型
    recommended_models:
      - "Anime_niji"
      - "niji_flux"
      - "Flux Flat Anime"
    strength_multiplier: 0.8
  
  artistic_rendering:
    name: "艺术渲染"
    description: "艺术风格和特殊效果渲染"
    model_type: "flux"  # 指定工作流类型
    recommended_models:
      - "Minimalism_Flux"
      - "flat2100"
      - "ShallowDepth"
    strength_multiplier: 0.8
  
 