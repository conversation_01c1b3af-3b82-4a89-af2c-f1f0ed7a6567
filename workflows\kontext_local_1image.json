{"6": {"inputs": {"text": "beautiful landscape with mountains and lakes, high quality, detailed", "clip": ["40", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "blurry, low quality, distorted, ugly, bad anatomy", "clip": ["40", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "40": {"inputs": {"clip_name": "clip_l.safetensors"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "42": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "50": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "55": {"inputs": {"seed": 123456789, "steps": 60, "cfg": 4.0, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1.0, "model": ["45", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["50", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "45": {"inputs": {"ckpt_name": "flux1e.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "60": {"inputs": {"samples": ["55", 0], "vae": ["42", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "70": {"inputs": {"image": ["60", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "80": {"inputs": {"image": "kontext_input_1.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Reference Image"}}, "85": {"inputs": {"image": ["80", 0], "strength": 0.8}, "class_type": "ImageStrength", "_meta": {"title": "Image Strength"}}, "90": {"inputs": {"samples": ["55", 0], "image": ["85", 0], "strength": 0.7}, "class_type": "ImageToLatent", "_meta": {"title": "Image To Latent"}}}