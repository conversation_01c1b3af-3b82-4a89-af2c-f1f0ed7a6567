const enUS = {
  common: {
    login: 'Login',
    logout: 'Logout',
    email: 'Email',
    password: 'Password',
    welcome: 'Welcome back to LangBot 👋',
    continueToLogin: 'Login to continue',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed, please check your email and password',
    enterEmail: 'Enter email address',
    enterPassword: 'Enter password',
    invalidEmail: 'Please enter a valid email address',
    emptyPassword: 'Please enter your password',
    language: 'Language',
    helpDocs: 'Get Help',
    create: 'Create',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    select: 'Select',
    cancel: 'Cancel',
    submit: 'Submit',
    error: 'Error',
    success: 'Success',
    save: 'Save',
    saving: 'Saving...',
    confirm: 'Confirm',
    confirmDelete: 'Confirm Delete',
    deleteConfirmation: 'Are you sure you want to delete this?',
    selectOption: 'Select an option',
    required: 'Required',
    enable: 'Enable',
    name: 'Name',
    description: 'Description',
    close: 'Close',
    deleteSuccess: 'Deleted successfully',
    deleteError: 'Delete failed: ',
    addRound: 'Add Round',
    copySuccess: 'Copy Successfully',
    test: 'Test',
  },
  notFound: {
    title: 'Page not found',
    description: 'The page you are looking for does not exist.',
    back: 'Back',
    home: 'Home',
    help: 'Get Help',
  },
  models: {
    title: 'Models',
    description: 'Configure and manage models that can be used in pipelines',
    createModel: 'Create Model',
    editModel: 'Edit Model',
    getModelListError: 'Failed to get model list: ',
    modelName: 'Model Name',
    modelProvider: 'Model Provider',
    modelBaseURL: 'Base URL',
    modelAbilities: 'Model Abilities',
    saveSuccess: 'Saved successfully',
    saveError: 'Save failed: ',
    createSuccess: 'Created successfully',
    createError: 'Creation failed: ',
    deleteSuccess: 'Deleted successfully',
    deleteError: 'Delete failed: ',
    deleteConfirmation: 'Are you sure you want to delete this model?',
    modelNameRequired: 'Model name cannot be empty',
    modelProviderRequired: 'Model provider cannot be empty',
    requestURLRequired: 'Request URL cannot be empty',
    apiKeyRequired: 'API Key cannot be empty',
    keyNameRequired: 'Key name cannot be empty',
    mustBeValidNumber: 'Must be a valid number',
    mustBeTrueOrFalse: 'Must be true or false',
    requestURL: 'Request URL',
    apiKey: 'API Key',
    abilities: 'Abilities',
    selectModelAbilities: 'Select model abilities',
    visionAbility: 'Vision Ability',
    functionCallAbility: 'Function Call',
    extraParameters: 'Extra Parameters',
    addParameter: 'Add Parameter',
    keyName: 'Key Name',
    type: 'Type',
    value: 'Value',
    string: 'String',
    number: 'Number',
    boolean: 'Boolean',
    extraParametersDescription:
      'Will be attached to the request body, such as max_tokens, temperature, top_p, etc.',
    selectModelProvider: 'Select Model Provider',
    modelProviderDescription:
      'Please fill in the model name provided by the supplier',
    selectModel: 'Select Model',
    testSuccess: 'Test successful',
    testError: 'Test failed, please check your model configuration',
  },
  bots: {
    title: 'Bots',
    description:
      'Create and manage bots, which are the entry points for LangBot to connect with various platforms',
    createBot: 'Create Bot',
    editBot: 'Edit Bot',
    getBotListError: 'Failed to get bot list: ',
    botName: 'Bot Name',
    botDescription: 'Bot Description',
    botNameRequired: 'Bot name cannot be empty',
    botDescriptionRequired: 'Bot description cannot be empty',
    adapterRequired: 'Adapter cannot be empty',
    defaultDescription: 'A bot',
    getBotConfigError: 'Failed to get bot configuration: ',
    saveSuccess: 'Saved successfully',
    saveError: 'Save failed: ',
    createSuccess:
      'Created successfully. Please enable or modify the bound pipeline',
    createError: 'Creation failed: ',
    deleteSuccess: 'Deleted successfully',
    deleteError: 'Delete failed: ',
    deleteConfirmation: 'Are you sure you want to delete this bot?',
    platformAdapter: 'Platform/Adapter Selection',
    selectAdapter: 'Select Adapter',
    adapterConfig: 'Adapter Configuration',
    bindPipeline: 'Bind Pipeline',
    selectPipeline: 'Select Pipeline',
    botLogTitle: 'Bot Log',
    enableAutoRefresh: 'Enable Auto Refresh',
    session: 'Session',
    yesterday: 'Yesterday',
    earlier: 'Earlier',
    dateFormat: '{{month}}/{{day}}',
    setBotEnableError: 'Failed to set bot enable status',
    log: 'Log',
  },
  plugins: {
    title: 'Plugins',
    description:
      'Install and configure plugins to extend LangBot functionality',
    createPlugin: 'Create Plugin',
    editPlugin: 'Edit Plugin',
    installed: 'Installed',
    marketplace: 'Marketplace',
    arrange: 'Sort Plugins',
    install: 'Install',
    installFromGithub: 'Install Plugin from GitHub',
    onlySupportGithub: 'Currently only supports installation from GitHub',
    enterGithubLink: 'Enter GitHub link of the plugin',
    installing: 'Installing plugin...',
    installSuccess: 'Plugin installed successfully',
    installFailed: 'Plugin installation failed:',
    searchPlugin: 'Search plugins',
    sortBy: 'Sort by',
    mostStars: 'Most stars',
    recentlyAdded: 'Recently added',
    recentlyUpdated: 'Recently updated',
    noMatchingPlugins: 'No matching plugins found',
    loading: 'Loading...',
    getPluginListError: 'Failed to get plugin list:',
    noPluginInstalled: 'No plugins installed',
    pluginConfig: 'Plugin Configuration',
    pluginSort: 'Plugin Sort',
    pluginSortDescription:
      'Plugin order affects the processing order within the same event, please drag the plugin card to sort',
    pluginSortSuccess: 'Plugin sort successful',
    pluginSortError: 'Plugin sort failed: ',
    pluginNoConfig: 'The plugin has no configuration items.',
    deleting: 'Deleting...',
    deletePlugin: 'Delete Plugin',
    cancel: 'Cancel',
    saveConfig: 'Save Config',
    saving: 'Saving...',
    confirmDeletePlugin:
      'Are you sure you want to delete the plugin ({{author}}/{{name}})?',
    confirmDelete: 'Confirm Delete',
    deleteError: 'Delete failed: ',
    close: 'Close',
    deleteConfirm: 'Delete Confirmation',
    modifyFailed: 'Modify failed: ',
    eventCount: 'Events: {{count}}',
    toolCount: 'Tools: {{count}}',
    starCount: 'Stars: {{count}}',
  },
  pipelines: {
    title: 'Pipelines',
    description:
      'Pipelines define the processing flow for message events, used to bind to bots',
    createPipeline: 'Create Pipeline',
    editPipeline: 'Edit Pipeline',
    chat: 'Chat',
    getPipelineListError: 'Failed to get pipeline list: ',
    daysAgo: 'days ago',
    today: 'Today',
    updateTime: 'Updated ',
    defaultBadge: 'Default',
    basicInfo: 'Basic',
    aiCapabilities: 'AI',
    triggerConditions: 'Trigger',
    safetyControls: 'Safety',
    outputProcessing: 'Output',
    nameRequired: 'Name cannot be empty',
    descriptionRequired: 'Description cannot be empty',
    createSuccess: 'Created successfully. Please edit pipeline parameters',
    createError: 'Creation failed: ',
    saveSuccess: 'Saved successfully',
    saveError: 'Save failed: ',
    deleteConfirmation:
      'Are you sure you want to delete this pipeline? Bots bound to this pipeline will not work.',
    defaultPipelineCannotDelete: 'Default pipeline cannot be deleted',
    debugDialog: {
      title: 'Pipeline Chat',
      selectPipeline: 'Select Pipeline',
      sessionType: 'Session Type',
      privateChat: 'Private Chat',
      groupChat: 'Group Chat',
      send: 'Send',
      reset: 'Reset Conversation',
      inputPlaceholder: 'Enter message...',
      noMessages: 'No messages',
      userMessage: 'User',
      botMessage: 'Bot',
      sendFailed: 'Send failed',
      resetSuccess: 'Conversation reset successfully',
      resetFailed: 'Reset failed',
      loadMessagesFailed: 'Failed to load messages',
      loadPipelinesFailed: 'Failed to load pipelines',
      atTips: 'Mention the bot',
    },
  },
  register: {
    title: 'Initialize LangBot 👋',
    description: 'This is your first time starting LangBot',
    adminAccountNote:
      'The email and password you fill in will be used as the initial administrator account',
    register: 'Register',
    initSuccess: 'Initialization successful, please login',
    initFailed: 'Initialization failed: ',
  },
};

export default enUS;
