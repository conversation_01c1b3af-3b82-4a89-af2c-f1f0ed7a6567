/* 主布局容器 */
.homeLayoutContainer {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: row;
  background-color: #eee;
}

/* 主内容区域 */
.main {
  background-color: #fafafa;
  flex: 1;
  display: flex;
  flex-direction: column;
  /* height: 100vh; */
  width: calc(100% - 1.2rem);
  height: calc(100% - 1.2rem);
  overflow: hidden;
  border-radius: 1.5rem 0 0 1.5rem;
  margin-left: 0.6rem;
  margin-top: 0.6rem;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.05);
}

.mainContent {
  padding: 1.5rem;
  padding-left: 2rem;
  flex: 1;
  overflow-y: auto;
  background-color: #fafafa;
}
