"""
网络重试处理器

提供网络请求的重试机制和错误处理
"""

import asyncio
import logging
from typing import Callable, Any, Optional
from functools import wraps


class NetworkRetryHandler:
    """网络重试处理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.logger = logging.getLogger(__name__)
    
    async def execute_with_retry(
        self, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """
        执行带重试的函数
        
        Args:
            func: 要执行的异步函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            Exception: 所有重试都失败后的最后一个异常
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(
                        f"第 {attempt + 1} 次尝试失败: {e}, "
                        f"{delay:.1f}秒后重试..."
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"所有 {self.max_retries + 1} 次尝试都失败了")
        
        # 所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise Exception("Unknown error occurred during retry")
    
    def retry_on_exception(
        self, 
        max_retries: Optional[int] = None,
        base_delay: Optional[float] = None
    ):
        """
        装饰器：为函数添加重试机制
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间
            
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                retry_handler = NetworkRetryHandler(
                    max_retries or self.max_retries,
                    base_delay or self.base_delay
                )
                return await retry_handler.execute_with_retry(func, *args, **kwargs)
            return wrapper
        return decorator
    
    async def execute_with_circuit_breaker(
        self,
        func: Callable,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        *args,
        **kwargs
    ) -> Any:
        """
        执行带熔断器的函数
        
        Args:
            func: 要执行的异步函数
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        # 简化的熔断器实现
        # 在实际应用中，这里应该维护状态和计数器
        try:
            return await self.execute_with_retry(func, *args, **kwargs)
        except Exception as e:
            self.logger.error(f"熔断器触发: {e}")
            raise
    
    def is_retryable_error(self, exception: Exception) -> bool:
        """
        判断错误是否可重试
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否可重试
        """
        # 网络相关错误通常可以重试
        retryable_errors = (
            ConnectionError,
            TimeoutError,
            OSError,
        )
        
        # 检查异常类型
        if isinstance(exception, retryable_errors):
            return True
        
        # 检查异常名称（字符串匹配）
        error_name = type(exception).__name__.lower()
        retryable_names = [
            'connection', 'timeout', 'network', 'temporary',
            'unavailable', 'server_error', 'gateway'
        ]
        
        return any(name in error_name for name in retryable_names) 