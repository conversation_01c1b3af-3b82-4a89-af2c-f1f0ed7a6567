#!/bin/bash

echo "🚀 启动LangBot开发环境..."

# 检查Docker服务状态
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker服务未运行，正在启动..."
    sudo systemctl start docker
fi

# 启动LangBot服务
echo "🤖 启动LangBot服务..."
docker-compose up -d

# 等待LangBot服务启动
echo "⏳ 等待LangBot服务启动..."
sleep 5

echo "✅ 服务启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   - LangBot WebUI: http://localhost:5300"
echo ""
echo "📝 微信集成方案："
echo "   方案1 - WeChatPad（推荐）："
echo "     docker-compose -f wechatpad-compose.yaml up -d"
echo "     访问: http://localhost:8080"
echo ""
echo "   方案2 - QQ官方机器人："
echo "     修改 config/qq_official.yaml 配置"
echo "     重启LangBot: docker-compose restart"
echo ""
echo "🔍 查看服务状态："
echo "   docker-compose ps"
echo "   docker-compose logs -f" 