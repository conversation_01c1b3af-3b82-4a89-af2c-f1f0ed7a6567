#!/usr/bin/env python3
"""
完整的flux_controlnet LoadImage测试脚本
包括提交到ComfyUI并等待执行完成
"""

import json
import os
import asyncio
import aiohttp

def test_workflow_loading():
    """测试工作流加载"""
    print("📋 测试工作流加载:")
    
    try:
        workflow_file = "workflows/flux_controlnet.json"
        if not os.path.exists(workflow_file):
            print(f"❌ 工作流文件不存在: {workflow_file}")
            return None
        
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        print(f"✅ 成功加载工作流，包含 {len(workflow_data)} 个节点")
        
        # 检查节点210
        node_210 = workflow_data.get("210", {})
        if node_210:
            class_type = node_210.get("class_type", "")
            title = node_210.get("_meta", {}).get("title", "")
            
            if class_type == "LoadImage" and title == "controlnet_image_input":
                print(f"✅ 节点210修改成功: {class_type}")
                return workflow_data
            else:
                print(f"❌ 节点210修改失败: {class_type}")
                return None
        else:
            print(f"❌ 找不到节点210")
            return None
            
    except Exception as e:
        print(f"❌ 测试工作流加载异常: {e}")
        return None

def prepare_test_image():
    """准备测试图片"""
    print("\n🧪 准备测试图片:")
    
    try:
        from pkg.workers.flux.image_file_manager import get_image_file_manager
        
        # 获取图片文件管理器
        image_manager = get_image_file_manager("/tmp/comfyui_test_uploads")
        print(f"✅ 成功获取图片文件管理器")
        
        # 创建测试图片数据（1x1像素的JPEG）
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        file_path = image_manager.save_image_to_local(test_image_data, "test_control.png")
        print(f"✅ 测试图片保存成功: {file_path}")
        
        return file_path, image_manager
        
    except Exception as e:
        print(f"❌ 准备测试图片失败: {e}")
        return None, None

async def test_comfyui_connection():
    """测试ComfyUI连接"""
    print("\n🔌 测试ComfyUI连接:")
    
    try:
        api_url = "http://localhost:8188"
        
        async with aiohttp.ClientSession() as session:
            # 测试连接
            async with session.get(f"{api_url}/system_stats") as response:
                if response.status == 200:
                    stats = await response.json()
                    print(f"✅ ComfyUI连接成功")
                    print(f"📊 系统状态: {stats.get('ram', 'N/A')} RAM, {stats.get('vram', 'N/A')} VRAM")
                    return True
                else:
                    print(f"❌ ComfyUI连接失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ ComfyUI连接异常: {e}")
        return False

async def submit_workflow_to_comfyui(workflow_data, image_path):
    """提交工作流到ComfyUI"""
    print(f"\n🚀 提交工作流到ComfyUI:")
    
    try:
        api_url = "http://localhost:8188"
        
        # 更新LoadImage节点的图片路径
        if "210" in workflow_data:
            workflow_data["210"]["inputs"]["image"] = image_path
            print(f"✅ 更新节点210的图片路径: {image_path}")
        
        # 准备提交数据
        prompt_data = {"prompt": workflow_data}
        
        async with aiohttp.ClientSession() as session:
            # 提交工作流
            async with session.post(f"{api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    print(f"✅ 工作流提交成功: {prompt_id}")
                    return prompt_id
                else:
                    error_text = await response.text()
                    print(f"❌ 提交工作流失败: {response.status}, {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ 提交工作流异常: {e}")
        return None

async def wait_for_workflow_completion(prompt_id):
    """等待工作流执行完成"""
    print(f"\n⏳ 等待工作流执行完成 (ID: {prompt_id}):")
    
    try:
        api_url = "http://localhost:8188"
        
        async with aiohttp.ClientSession() as session:
            for i in range(120):  # 最多等待120秒
                await asyncio.sleep(1)
                
                # 检查状态
                async with session.get(f"{api_url}/history/{prompt_id}") as status_response:
                    if status_response.status == 200:
                        history_data = await status_response.json()
                        if prompt_id in history_data:
                            prompt_data = history_data[prompt_id]
                            
                            if 'outputs' in prompt_data:
                                print("✅ 工作流执行完成")
                                
                                # 显示输出信息
                                outputs = prompt_data['outputs']
                                print(f"📊 输出节点数量: {len(outputs)}")
                                
                                for node_id, output_data in outputs.items():
                                    if 'images' in output_data:
                                        images = output_data['images']
                                        print(f"📷 节点 {node_id} 生成 {len(images)} 张图片")
                                        for img in images:
                                            print(f"  - {img.get('filename', 'N/A')} ({img.get('type', 'N/A')})")
                                
                                return True
                                
                            elif 'error' in prompt_data:
                                error_msg = prompt_data.get('error', {}).get('message', '未知错误')
                                print(f"❌ 工作流执行失败: {error_msg}")
                                return False
                                
                            elif 'exec_info' in prompt_data:
                                exec_info = prompt_data['exec_info']
                                if exec_info.get('queue_remaining', 0) > 0:
                                    print(f"⏳ 队列中等待: {exec_info['queue_remaining']} 个任务")
                                else:
                                    print(f"⚙️  正在执行...")
                    
                    if i % 10 == 0:  # 每10秒显示一次进度
                        print(f"⏰ 已等待 {i+1} 秒...")
            
            print("⏰ 工作流执行超时")
            return False
            
    except Exception as e:
        print(f"❌ 等待工作流完成异常: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 flux_controlnet LoadImage完整测试")
    print("=" * 60)
    
    # 1. 测试工作流加载
    workflow_data = test_workflow_loading()
    if not workflow_data:
        print("❌ 工作流加载测试失败")
        return
    
    # 2. 准备测试图片
    image_path, image_manager = prepare_test_image()
    if not image_path:
        print("❌ 准备测试图片失败")
        return
    
    # 3. 测试ComfyUI连接
    connection_ok = await test_comfyui_connection()
    if not connection_ok:
        print("❌ ComfyUI连接失败")
        return
    
    # 4. 提交工作流
    prompt_id = await submit_workflow_to_comfyui(workflow_data, image_path)
    if not prompt_id:
        print("❌ 提交工作流失败")
        return
    
    # 5. 等待执行完成
    success = await wait_for_workflow_completion(prompt_id)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！LoadImage节点工作正常！")
    else:
        print("❌ 工作流执行失败")

if __name__ == "__main__":
    asyncio.run(main()) 