#!/usr/bin/env python3
"""
详细调试FluxWorkflowManager初始化问题
"""

import asyncio
import sys
import traceback

# 添加项目路径
sys.path.append('.')

async def debug_flux_init():
    """调试FluxWorkflowManager初始化"""
    
    print("🔍 开始调试FluxWorkflowManager初始化...")
    
    try:
        # 1. 检查FluxWorkflowManager导入
        print("📦 检查FluxWorkflowManager导入...")
        from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
        print("✅ FluxWorkflowManager导入成功")
        
        # 2. 检查依赖模块导入
        print("📦 检查依赖模块导入...")
        try:
            from pkg.workers.flux.parameter_analyzer import get_parameter_analyzer
            print("✅ parameter_analyzer导入成功")
        except Exception as e:
            print(f"❌ parameter_analyzer导入失败: {e}")
            return
        
        try:
            from pkg.workers.flux.seed_manager import get_seed_manager
            print("✅ seed_manager导入成功")
        except Exception as e:
            print(f"❌ seed_manager导入失败: {e}")
            return
        
        try:
            from pkg.workers.flux.standard_nodes import get_standard_node_mapper
            print("✅ standard_nodes导入成功")
        except Exception as e:
            print(f"❌ standard_nodes导入失败: {e}")
            return
        
        try:
            from pkg.workers.flux.lora_integration import get_lora_integration
            print("✅ lora_integration导入成功")
        except Exception as e:
            print(f"❌ lora_integration导入失败: {e}")
            return
        
        # 3. 创建测试配置
        print("⚙️ 创建测试配置...")
        pipeline_config = {
            'ai': {
                'comfyui-agent': {
                    'api-url': 'http://localhost:8188',
                    'timeout': 180,
                    'workflow-path': 'workflows'
                }
            }
        }
        print("✅ 测试配置创建成功")
        
        # 4. 模拟ap对象
        print("🔧 创建模拟ap对象...")
        class MockApplication:
            def __init__(self):
                self.logger = None
                self.model_mgr = None
                self.pipeline_config = pipeline_config
        
        # 使用None来避免类型检查问题
        mock_ap = None
        print("✅ 模拟ap对象创建成功（使用None）")
        
        # 5. 测试各个依赖模块的初始化
        print("🔧 测试依赖模块初始化...")
        
        # 测试parameter_analyzer（跳过，因为需要真实的ap对象）
        print("  - 跳过parameter_analyzer测试（需要真实的ap对象）")
        
        # 测试seed_manager
        try:
            print("  - 测试seed_manager...")
            seed_mgr = get_seed_manager()
            print("    ✅ seed_manager初始化成功")
        except Exception as e:
            print(f"    ❌ seed_manager初始化失败: {e}")
            print(f"    错误详情: {traceback.format_exc()}")
        
        # 测试node_mapper
        try:
            print("  - 测试node_mapper...")
            node_mapper = get_standard_node_mapper("workflows")
            print("    ✅ node_mapper初始化成功")
        except Exception as e:
            print(f"    ❌ node_mapper初始化失败: {e}")
            print(f"    错误详情: {traceback.format_exc()}")
        
        # 测试lora_integration
        try:
            print("  - 测试lora_integration...")
            lora_int = get_lora_integration()
            print("    ✅ lora_integration初始化成功")
        except Exception as e:
            print(f"    ❌ lora_integration初始化失败: {e}")
            print(f"    错误详情: {traceback.format_exc()}")
        
        # 6. 测试FluxWorkflowManager初始化
        print("🔧 测试FluxWorkflowManager初始化...")
        try:
            flux_manager = FluxWorkflowManager(mock_ap, pipeline_config)
            print("✅ FluxWorkflowManager初始化成功")
            
            # 检查各个属性
            print("📊 检查FluxWorkflowManager属性...")
            print(f"  - parameter_analyzer: {flux_manager.parameter_analyzer is not None}")
            print(f"  - seed_manager: {flux_manager.seed_manager is not None}")
            print(f"  - node_mapper: {flux_manager.node_mapper is not None}")
            print(f"  - lora_integration: {flux_manager.lora_integration is not None}")
            print(f"  - api_url: {flux_manager.api_url}")
            print(f"  - timeout: {flux_manager.timeout}")
            
        except Exception as e:
            print(f"❌ FluxWorkflowManager初始化失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            return
        
        # 7. 测试无ap对象的初始化
        print("🔧 测试无ap对象的FluxWorkflowManager初始化...")
        try:
            flux_manager_no_ap = FluxWorkflowManager(pipeline_config=pipeline_config)
            print("✅ 无ap对象的FluxWorkflowManager初始化成功")
            
            # 检查parameter_analyzer是否为None
            print(f"  - parameter_analyzer: {flux_manager_no_ap.parameter_analyzer is None}")
            
        except Exception as e:
            print(f"❌ 无ap对象的FluxWorkflowManager初始化失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
        
        print("\n🏁 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程中出现异常: {e}")
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_flux_init()) 