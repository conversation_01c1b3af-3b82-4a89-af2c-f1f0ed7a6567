#!/usr/bin/env python3
"""
工作流调试分析脚本
专门用于分析发送给ComfyUI的工作流数据
"""

import json
import os
import asyncio
from typing import Dict, Any, Optional, List
from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
from pkg.workers.flux.flux_workflow_models import FluxParameters, SeedInstruction, LoRAConfig, QualityLevel
from pkg.core import app, entities as core_entities


async def analyze_workflow_data():
    """分析工作流数据"""
    print("🔍 开始分析工作流数据...")
    
    # 创建管理器
    pipeline_config = {
        'ai': {
            'comfyui-agent': {
                'api-url': 'http://localhost:8188',
                'timeout': 180,
                'workflow-path': 'workflows'
            }
        }
    }
    
    manager = FluxWorkflowManager(ap=None, pipeline_config=pipeline_config)
    
    # 模拟用户参数
    user_text = "画一只黑猫"
    
    # 1. 加载工作流模板
    workflow_file = "workflows/flux_controlnet.json"
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return
    
    print(f"📂 加载工作流模板: {workflow_file}")
    with open(workflow_file, 'r', encoding='utf-8') as f:
        workflow_data = json.load(f)
    
    # 2. 分析采样器节点
    print("\n🎯 分析采样器节点:")
    sampler_count = 0
    for node_id, node_data in workflow_data.items():
        if isinstance(node_data, dict) and node_data.get("class_type") in ["KSampler", "KSamplerAdvanced //Inspire"]:
            sampler_count += 1
            inputs = node_data.get("inputs", {})
            steps = inputs.get("steps", "未设置")
            cfg = inputs.get("cfg", "未设置")
            sampler_name = inputs.get("sampler_name", "未设置")
            print(f"  📌 第{sampler_count}个采样器 - 节点{node_id}:")
            print(f"    - 类型: {node_data.get('class_type')}")
            print(f"    - 步数: {steps}")
            print(f"    - CFG: {cfg}")
            print(f"    - 采样器: {sampler_name}")
    
    # 3. 模拟参数更新
    print("\n🔧 模拟参数更新:")
    
    # 创建Flux参数对象
    params = FluxParameters(
        prompt=user_text,
        width=1024,
        height=1024,
        steps=20,
        guidance=3.5,
        seed=12345,
        seed_instruction=SeedInstruction.SPECIFIC,
        quality_level=QualityLevel.STANDARD
    )
    
    print(f"  📝 输入参数: {params}")
    
    # 应用参数到工作流
    if manager.node_mapper:
        updated_workflow = manager.node_mapper.apply_parameters_to_workflow(workflow_data, params)
        
        print("\n🎯 更新后的采样器节点:")
        sampler_count = 0
        for node_id, node_data in updated_workflow.items():
            if isinstance(node_data, dict) and node_data.get("class_type") in ["KSampler", "KSamplerAdvanced //Inspire"]:
                sampler_count += 1
                inputs = node_data.get("inputs", {})
                steps = inputs.get("steps", "未设置")
                cfg = inputs.get("cfg", "未设置")
                seed = inputs.get("seed", "未设置")
                print(f"  📌 第{sampler_count}个采样器 - 节点{node_id}:")
                print(f"    - 步数: {steps}")
                print(f"    - CFG: {cfg}")
                print(f"    - 种子: {seed}")
        
        # 4. 保存完整的工作流数据
        output_file = "temp/workflow_debug_analysis.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(updated_workflow, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 完整工作流数据已保存到: {output_file}")
        
        # 5. 分析关键节点
        print("\n🔍 关键节点分析:")
        
        # 检查EasyGlobalSeed节点
        if "217" in updated_workflow:
            node_217 = updated_workflow["217"]
            print(f"  📌 节点217: {node_217.get('class_type', '未知')}")
            if "inputs" in node_217:
                seed_value = node_217["inputs"].get("value", "未设置")
                print(f"    - 种子值: {seed_value}")
        
        # 检查prompt节点
        if "211" in updated_workflow:
            node_211 = updated_workflow["211"]
            print(f"  📌 节点211: {node_211.get('class_type', '未知')}")
            if "inputs" in node_211:
                prompt_text = node_211["inputs"].get("prompt", "未设置")
                print(f"    - 提示词: {prompt_text}")
        
        # 检查图像输入节点
        if "216" in updated_workflow:
            node_216 = updated_workflow["216"]
            print(f"  📌 节点216: {node_216.get('class_type', '未知')}")
            if "inputs" in node_216:
                has_image = "base64_data" in node_216["inputs"]
                print(f"    - 有图像数据: {has_image}")
    
    print("\n✅ 工作流分析完成!")


def analyze_existing_workflow_file():
    """分析现有的工作流文件"""
    print("\n🔍 分析现有工作流文件...")
    
    workflow_file = "workflows/flux_controlnet.json"
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        workflow_data = json.load(f)
    
    print(f"📂 分析文件: {workflow_file}")
    print(f"📊 总节点数: {len(workflow_data)}")
    
    # 统计节点类型
    node_types = {}
    for node_id, node_data in workflow_data.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "未知")
            node_types[class_type] = node_types.get(class_type, 0) + 1
    
    print("\n📈 节点类型统计:")
    for class_type, count in sorted(node_types.items()):
        print(f"  - {class_type}: {count}个")
    
    # 详细分析采样器节点
    print("\n🎯 详细采样器节点分析:")
    for node_id, node_data in workflow_data.items():
        if isinstance(node_data, dict) and node_data.get("class_type") in ["KSampler", "KSamplerAdvanced //Inspire"]:
            inputs = node_data.get("inputs", {})
            print(f"  📌 节点{node_id}:")
            print(f"    - 类型: {node_data.get('class_type')}")
            print(f"    - 标题: {node_data.get('_meta', {}).get('title', '无')}")
            print(f"    - 步数: {inputs.get('steps', '未设置')}")
            print(f"    - CFG: {inputs.get('cfg', '未设置')}")
            print(f"    - 采样器名称: {inputs.get('sampler_name', '未设置')}")
            print(f"    - 调度器: {inputs.get('scheduler', '未设置')}")
            print(f"    - 种子: {inputs.get('seed', '未设置')}")
            print(f"    - 所有输入: {list(inputs.keys())}")
            print()


if __name__ == "__main__":
    print("🚀 开始工作流调试分析...")
    
    # 1. 分析现有工作流文件
    analyze_existing_workflow_file()
    
    # 2. 分析参数应用过程
    asyncio.run(analyze_workflow_data())
    
    print("\n🎉 调试分析完成!") 