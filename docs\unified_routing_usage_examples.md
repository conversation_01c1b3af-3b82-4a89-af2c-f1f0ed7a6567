# 统一路由系统使用示例

基于PRD-********-统一路由系统设计规划，本文档提供统一路由系统的使用示例。

## 概述

统一路由系统实现了两级路由架构：
- **第一级路由**：关键词触发（快速、可靠）
- **第二级路由**：LLM智能分析（智能、灵活）

## 基本使用

### 1. 直接使用统一路由系统

```python
from pkg.core.workflow.unified_routing_system import get_unified_router

# 获取路由器实例
router = get_unified_router(ap)

# 执行统一路由
result = await router.route_unified(
    user_text="生成一只可爱的猫咪",
    has_images=False,
    image_count=0,
    query=query
)

print(f"工作流类型: {result.workflow_type.value}")
print(f"路由级别: {result.routing_level.value}")
print(f"置信度: {result.confidence.value}")
print(f"推理: {result.reasoning}")
print(f"处理时间: {result.processing_time_ms:.1f}ms")
```

### 2. 在Runner中使用Mixin

```python
from pkg.provider.runners.unified_routing_mixin_v2 import UnifiedRoutingMixinV2

class MyRunner(RequestRunner, UnifiedRoutingMixinV2):
    def __init__(self, ap, pipeline_config):
        super().__init__(ap, pipeline_config)
        UnifiedRoutingMixinV2.__init__(self)
    
    async def run(self, query):
        # 智能路由
        routing_result = await self.route_workflow_intelligently(
            user_text="编辑这张图片",
            query=query,
            attached_images=images
        )
        
        # 根据路由结果处理
        if routing_result.workflow_type == WorkflowType.AIGEN:
            # 处理文生图
            pass
        elif routing_result.workflow_type == WorkflowType.KONTEXT:
            # 处理图生图
            pass
```

## 使用场景示例

### 场景1：传统用户明确指令（第一级路由）

```python
# 用户输入: "aigen 一只可爱的猫咪"
result = await router.route_unified("aigen 一只可爱的猫咪")

# 结果:
# - routing_level: LEVEL_1
# - workflow_type: AIGEN
# - confidence: HIGH
# - processing_time_ms: <5ms
# - reasoning: "匹配关键词: AIGEN"
```

### 场景2：新用户自然语言（第二级路由）

```python
# 用户输入: "帮我画一个漂亮的风景"
result = await router.route_unified("帮我画一个漂亮的风景")

# 结果:
# - routing_level: LEVEL_2
# - workflow_type: AIGEN
# - confidence: MEDIUM
# - processing_time_ms: <2s (LLM) 或 <10ms (启发式)
# - reasoning: "检测到生成类关键词，选择AIGEN工作流"
```

### 场景3：图片编辑需求（第二级路由）

```python
# 用户输入: "把这张图片调亮一些" + 图片
result = await router.route_unified(
    user_text="把这张图片调亮一些",
    has_images=True,
    image_count=1
)

# 结果:
# - routing_level: LEVEL_2
# - workflow_type: KONTEXT
# - confidence: MEDIUM
# - reasoning: "检测到1张图片，选择本地Kontext工作流"
```

### 场景4：复杂处理需求（第二级路由）

```python
# 用户输入: "对这张图片进行复杂的风格转换和修复"
result = await router.route_unified(
    user_text="对这张图片进行复杂的风格转换和修复",
    has_images=True,
    image_count=1
)

# 结果:
# - routing_level: LEVEL_2
# - workflow_type: KONTEXT_API
# - confidence: MEDIUM
# - reasoning: "检测到复杂处理需求，选择API工作流"
```

## 配置示例

### 1. 基础配置

```yaml
# config/unified_routing.yaml
unified_routing:
  level_1:
    enabled: true
    keywords:
      aigen: "AIGEN"
      kontext: "KONTEXT"
      kontext_api: "KONTEXT_API"
  
  level_2:
    enabled: true
    llm:
      enabled: true
      timeout_ms: 2000
    heuristic:
      enabled: true
      timeout_ms: 10
```

### 2. 自定义关键词

```python
# 添加自定义关键词
router = get_unified_router(ap)
router.level_1_keywords["imagine"] = WorkflowType.AIGEN
router.level_1_keywords["edit"] = WorkflowType.KONTEXT
```

### 3. 自定义启发式规则

```python
# 修改启发式规则
def custom_heuristic_routing(self, user_text, has_images, image_count):
    # 自定义逻辑
    if "动漫" in user_text:
        return WorkflowType.AIGEN
    elif "照片" in user_text and has_images:
        return WorkflowType.KONTEXT
    # ... 其他规则
```

## 错误处理

### 1. 超时处理

```python
try:
    result = await router.route_unified(user_text, has_images, image_count, query)
except asyncio.TimeoutError:
    # LLM超时，使用启发式路由
    result = router._route_with_heuristics(user_text, has_images, image_count)
```

### 2. 异常处理

```python
try:
    result = await router.route_unified(user_text, has_images, image_count, query)
except Exception as e:
    # 使用默认路由
    result = UnifiedRoutingResult(
        routing_level=RoutingLevel.LEVEL_2,
        workflow_type=WorkflowType.AIGEN,
        confidence=RoutingConfidence.UNKNOWN,
        reasoning=f"路由异常: {e}",
        processing_time_ms=0,
        fallback_used=True
    )
```

## 监控和日志

### 1. 性能监控

```python
# 获取路由统计
stats = router.get_routing_stats()
print(f"第一级关键词: {stats['level_1_keywords']}")
print(f"第二级LLM启用: {stats['level_2_enabled']}")
print(f"启发式启用: {stats['heuristic_enabled']}")
```

### 2. 日志记录

```python
# 路由决策日志
logger.info(f"🎯 统一路由决策: {result.workflow_type.value}")
logger.info(f"   路由级别: {result.routing_level.value}")
logger.info(f"   置信度: {result.confidence.value}")
logger.info(f"   推理: {result.reasoning}")
logger.info(f"   处理时间: {result.processing_time_ms:.1f}ms")

if result.fallback_used:
    logger.warning("   使用了后备路由")

if result.needs_clarification:
    logger.warning(f"   需要用户确认: {result.clarification_question}")
```

## 测试示例

### 1. 单元测试

```python
import pytest
from pkg.core.workflow.unified_routing_system import UnifiedRoutingSystem

class TestUnifiedRouting:
    def setup_method(self):
        self.router = UnifiedRoutingSystem()
    
    def test_level_1_routing(self):
        result = self.router._route_level_1("aigen 测试")
        assert result == WorkflowType.AIGEN
    
    @pytest.mark.asyncio
    async def test_unified_routing(self):
        result = await self.router.route_unified("aigen 测试")
        assert result.routing_level == RoutingLevel.LEVEL_1
        assert result.workflow_type == WorkflowType.AIGEN
```

### 2. 集成测试

```python
# 测试完整的工作流
async def test_complete_workflow():
    # 1. 路由决策
    result = await router.route_unified("生成猫咪")
    
    # 2. 根据结果执行工作流
    if result.workflow_type == WorkflowType.AIGEN:
        # 执行AIGEN工作流
        pass
    elif result.workflow_type == WorkflowType.KONTEXT:
        # 执行KONTEXT工作流
        pass
```

## 最佳实践

### 1. 性能优化

- 第一级路由应该保持简单快速（<5ms）
- 第二级LLM路由设置合理的超时时间（<2s）
- 使用启发式路由作为后备方案

### 2. 用户体验

- 对于高置信度路由，直接执行
- 对于低置信度路由，提供用户确认
- 提供清晰的路由决策解释

### 3. 错误处理

- 实现优雅的降级机制
- 记录详细的错误日志
- 提供用户友好的错误信息

### 4. 扩展性

- 支持自定义关键词配置
- 支持自定义启发式规则
- 支持新的工作流类型

## 总结

统一路由系统通过两级路由架构，实现了：
1. **兼容性**：保留现有用户习惯
2. **智能化**：新增自然语言理解
3. **可靠性**：多层降级机制
4. **性能优化**：关键词快速路由，LLM按需调用

通过合理配置和使用，可以显著提升用户体验和系统性能。 