import { BotCardVO } from '@/app/home/<USER>/components/bot-card/BotCardVO';
import styles from './botCard.module.css';
import { httpClient } from '@/app/infra/http/HttpClient';
import { Switch } from '@/components/ui/switch';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

export default function BotCard({
  botCardVO,
  clickLogIconCallback,
  setBotEnableCallback,
}: {
  botCardVO: BotCardVO;
  clickLogIconCallback: (id: string) => void;
  setBotEnableCallback: (id: string, enable: boolean) => void;
}) {
  const { t } = useTranslation();
  function onClickLogIcon() {
    clickLogIconCallback(botCardVO.id);
  }

  function setBotEnable(enable: boolean) {
    return httpClient.updateBot(botCardVO.id, {
      name: botCardVO.name,
      description: botCardVO.description,
      adapter: botCardVO.adapter,
      adapter_config: botCardVO.adapterConfig,
      enable: enable,
    });
  }

  return (
    <div className={`${styles.cardContainer}`}>
      <div className={`${styles.iconBasicInfoContainer}`}>
        <img
          className={`${styles.iconImage}`}
          src={botCardVO.iconURL}
          alt="icon"
        />

        <div className={`${styles.basicInfoContainer}`}>
          <div className={`${styles.basicInfoNameContainer}`}>
            <div className={`${styles.basicInfoName}`}>{botCardVO.name}</div>
            <div className={`${styles.basicInfoDescription}`}>
              {botCardVO.description}
            </div>
          </div>

          <div className={`${styles.basicInfoAdapterContainer}`}>
            <svg
              className={`${styles.basicInfoAdapterIcon}`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M2 8.99374C2 5.68349 4.67654 3 8.00066 3H15.9993C19.3134 3 22 5.69478 22 8.99374V21H8.00066C4.68659 21 2 18.3052 2 15.0063V8.99374ZM20 19V8.99374C20 6.79539 18.2049 5 15.9993 5H8.00066C5.78458 5 4 6.78458 4 8.99374V15.0063C4 17.2046 5.79512 19 8.00066 19H20ZM14 11H16V13H14V11ZM8 11H10V13H8V11Z"></path>
            </svg>
            <span className={`${styles.basicInfoAdapterLabel}`}>
              {botCardVO.adapterLabel}
            </span>
          </div>

          <div className={`${styles.basicInfoPipelineContainer}`}>
            <svg
              className={`${styles.basicInfoPipelineIcon}`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M6 21.5C4.067 21.5 2.5 19.933 2.5 18C2.5 16.067 4.067 14.5 6 14.5C7.5852 14.5 8.92427 15.5539 9.35481 16.9992L15 16.9994V15L17 14.9994V9.24339L14.757 6.99938H9V9.00003H3V3.00003H9V4.99939H14.757L18 1.75739L22.2426 6.00003L19 9.24139V14.9994L21 15V21H15V18.9994L9.35499 19.0003C8.92464 20.4459 7.58543 21.5 6 21.5ZM6 16.5C5.17157 16.5 4.5 17.1716 4.5 18C4.5 18.8285 5.17157 19.5 6 19.5C6.82843 19.5 7.5 18.8285 7.5 18C7.5 17.1716 6.82843 16.5 6 16.5ZM19 17H17V19H19V17ZM18 4.58581L16.5858 6.00003L18 7.41424L19.4142 6.00003L18 4.58581ZM7 5.00003H5V7.00003H7V5.00003Z"></path>
            </svg>
            <span className={`${styles.basicInfoPipelineLabel}`}>
              {botCardVO.usePipelineName}
            </span>
          </div>
        </div>

        <div className={`${styles.botOperationContainer}`}>
          <Switch
            checked={botCardVO.enable}
            onCheckedChange={(e) => {
              setBotEnable(e)
                .then(() => {
                  setBotEnableCallback(botCardVO.id, e);
                })
                .catch((err) => {
                  console.error(err);
                  toast.error(t('bots.setBotEnableError'));
                });
            }}
            onClick={(e) => {
              e.stopPropagation();
            }}
          />
          <Button
            variant="outline"
            className="w-auto h-[40px]"
            onClick={(e) => {
              onClickLogIcon();
              e.stopPropagation();
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              width="48"
              height="48"
              fill="currentColor"
            >
              <path d="M21 8V20.9932C21 21.5501 20.5552 22 20.0066 22H3.9934C3.44495 22 3 21.556 3 21.0082V2.9918C3 2.45531 3.4487 2 4.00221 2H14.9968L21 8ZM19 9H14V4H5V20H19V9ZM8 7H11V9H8V7ZM8 11H16V13H8V11ZM8 15H16V17H8V15Z"></path>
            </svg>
            {t('bots.log')}
          </Button>
        </div>
      </div>
    </div>
  );
}
