# 双触发条件修复说明

## 🎯 问题描述

原来的langbot在启用群消息@功能后，所有@机器人的消息都会触发路由决策，无法正确实现**双触发条件**（@机器人 + 包含前缀）。

## 🔧 修复内容

本修复解决了以下问题：
1. **前缀匹配逻辑错误**：修复了`atbot-with-prefix`规则在包含@机器人的消息上无法正确匹配前缀的问题
2. **路由逻辑简化**：简化了ComfyUI Agent的处理逻辑，使其更稳定
3. **会话管理增强**：为WorkflowSession添加了routing_result属性

## 📁 文件列表

### 修复文件
- `fix-trigger-conditions.patch` - 包含所有修复的补丁文件
- `apply-trigger-fix.sh` - 应用修复的脚本
- `rollback-trigger-fix.sh` - 回滚修复的脚本

### 涉及的核心文件
- `pkg/pipeline/resprule/rules/atbot_with_prefix.py` - 规则文件
- `pkg/provider/runners/comfyui_agent.py` - ComfyUI代理
- `pkg/core/session/models.py` - 会话模型

## 🚀 使用方法

### 首次应用修复
```bash
# 1. 给脚本添加执行权限
chmod +x apply-trigger-fix.sh

# 2. 应用修复
./apply-trigger-fix.sh
```

### 更新langbot后重新应用
```bash
# 1. 拉取最新的langbot代码
git pull origin main

# 2. 重新应用修复
./apply-trigger-fix.sh
```

### 回滚修复（如果需要）
```bash
# 给脚本添加执行权限
chmod +x rollback-trigger-fix.sh

# 回滚修复
./rollback-trigger-fix.sh
```

## 🧪 测试场景

修复后，请测试以下场景以确认双触发条件正常工作：

### ❌ 不应该触发的情况
- 普通群聊消息：`今天天气不错`
- 只@机器人：`@机器人 你好`
- 只发前缀：`aigen 生成图片`

### ✅ 应该触发的情况
- 双触发条件：`@机器人 aigen 生成图片`
- 双触发条件：`@机器人 kontext 编辑这张图`
- 双触发条件：`@机器人 kontext_api 处理图片`

## 📋 工作流程

正确的工作流程应该是：
1. 用户发送：`@机器人 aigen 一只可爱的猫咪`
2. 系统响应：工作流启动消息
3. 用户发送：`开始` 或 `go`
4. 系统开始生成图片

## ⚠️ 注意事项

1. **备份重要**：每次应用或回滚都会自动备份文件
2. **版本兼容**：补丁是基于当前版本创建的，更新langbot后可能需要手动调整
3. **冲突处理**：如果补丁无法应用，说明代码有冲突，需要手动合并

## 🆘 问题排查

### 补丁无法应用
1. 检查langbot版本是否与补丁兼容
2. 查看git状态，确保工作目录干净
3. 手动检查冲突文件的差异

### 修复后仍有问题
1. 检查配置文件`templates/default-pipeline-config.json`
2. 确认`atbot-with-prefix: true`已启用
3. 检查前缀列表是否包含所需的工作流前缀

### 回滚失败
1. 使用备份文件手动恢复
2. 重新从git仓库拉取干净的文件

## 📞 技术支持

如果遇到问题，请提供：
1. 错误信息
2. langbot版本
3. 配置文件内容
4. 测试场景和实际结果 