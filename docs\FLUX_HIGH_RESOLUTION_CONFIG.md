# Flux模型高分辨率配置指南

## 概述

Flux模型支持高分辨率图像生成，我们按照约1百万像素的策略来配置不同宽高比的尺寸，以在保持高质量的同时优化性能和资源使用。

## 分辨率策略

### 目标：约1百万像素
- **正方形 (1:1)**: 1024×1024 = 1,048,576像素
- **横版 (16:10)**: 1280×800 = 1,024,000像素
- **横版 (2:1)**: 1440×720 = 1,036,800像素
- **竖版 (5:8)**: 800×1280 = 1,024,000像素
- **竖版 (1:2)**: 720×1440 = 1,036,800像素

### 支持的尺寸列表
```python
valid_sizes = [720, 800, 1024, 1280, 1440]
```

## 工作流配置

### 1. 默认工作流 (default_workflow.json)
- **尺寸**: 1024×1024 (正方形)
- **用途**: 通用文生图，适合各种内容

### 2. 横版工作流 (landscape_workflow.json)
- **尺寸**: 1280×800 (16:10比例)
- **用途**: 风景、建筑、宽屏内容

### 3. 竖版工作流 (portrait_workflow.json)
- **尺寸**: 800×1280 (5:8比例)
- **用途**: 人像、竖屏内容

### 4. 动漫工作流 (anime_workflow.json)
- **尺寸**: 800×1280 (5:8比例)
- **用途**: 动漫风格内容

### 5. 摄影工作流 (photography_workflow.json)
- **尺寸**: 1024×1024 (正方形)
- **用途**: 专业摄影风格

## 智能尺寸选择

### LLM分析规则
系统会根据用户输入自动选择合适的尺寸：

1. **明确指定比例**:
   - "横版" → 1280×800 或 1440×720
   - "竖版" → 800×1280 或 720×1440
   - "正方形" → 1024×1024

2. **内容类型推断**:
   - 风景、建筑 → 横版
   - 人像、动漫 → 竖版
   - 通用内容 → 正方形

3. **质量关键词**:
   - "高质量" → 增加steps和guidance
   - "快速" → 减少steps

## 性能优化

### 内存使用
- 1百万像素约占用4MB显存
- 相比2K分辨率节省约75%显存

### 生成速度
- 标准设置: 20步，约30-60秒
- 高质量设置: 30步，约60-90秒

### 质量平衡
- 在1百万像素下，Flux模型能提供优秀的细节表现
- 适合大多数应用场景

## 配置更新

### 已更新的文件
1. `pkg/provider/runners/comfyui_agent_backup.py`
2. `pkg/provider/runners/standard_image_handler.py`
3. `pkg/workers/comfyui_workflow_manager.py`
4. `pkg/workers/intent_analyzer.py`
5. `workflows/landscape_workflow.json`
6. `workflows/portrait_workflow.json`
7. `workflows/anime_workflow.json`

### 验证方法
```python
# 检查尺寸是否在有效范围内
valid_sizes = [720, 800, 1024, 1280, 1440]
if width in valid_sizes and height in valid_sizes:
    pixel_count = width * height
    print(f"像素数: {pixel_count:,}")
```

## 注意事项

1. **Flux模型限制**: 确保ComfyUI中已正确安装Flux模型文件
2. **显存要求**: 建议至少8GB显存用于稳定运行
3. **比例保持**: 系统会自动选择最接近用户意图的比例
4. **质量设置**: 可根据需要调整steps和guidance参数

## 未来扩展

- 支持更多自定义比例
- 动态分辨率调整
- 批量生成优化
- 质量预设配置 