#!/usr/bin/env python3
"""查看当前Lora模型状态"""

from pkg.workers.shared.shared_lora_manager import SharedLoraManager

def main():
    lm = SharedLoraManager()
    
    print("=== 当前Lora模型分类统计 ===")
    stats = lm.get_statistics()
    print(f"总计: {stats['total']}个模型")
    print(f"启用: {stats['active']}个模型")
    print(f"本地: {stats['local']}个模型")
    print(f"远程: {stats['remote']}个模型")
    print(f"优先: {stats['priority']}个模型")
    
    print("\n=== 按分类统计 ===")
    for category, count in stats['by_category'].items():
        print(f"{category}: {count}个模型")
    
    print("\n=== 建筑类模型（启用） ===")
    for model in lm.lora_models.values():
        if model.category.value == 'architecture' and model.is_active:
            print(f"- {model.name}")
    
    print("\n=== 细节增强模型（启用） ===")
    for model in lm.lora_models.values():
        if model.category.value == 'detail' and model.is_active:
            print(f"- {model.name}")
    
    print("\n=== 动漫类模型（启用） ===")
    for model in lm.lora_models.values():
        if model.category.value == 'anime' and model.is_active:
            print(f"- {model.name}")
    
    print("\n=== 风格类模型（启用） ===")
    for model in lm.lora_models.values():
        if model.category.value == 'style' and model.is_active:
            print(f"- {model.name}")
    
    print("\n=== 其他分类模型（启用） ===")
    for model in lm.lora_models.values():
        if model.category.value not in ['architecture', 'detail', 'anime', 'style'] and model.is_active:
            print(f"- {model.name} ({model.category.value})")
    
    print(f"\n=== 已禁用的模型 ===")
    disabled_models = [model for model in lm.lora_models.values() if not model.is_active]
    if disabled_models:
        for model in disabled_models:
            print(f"- {model.name} ({model.category.value}) - 已禁用")
    else:
        print("无已禁用的模型")
    
    print(f"\n总计: {len(lm.lora_models)}个模型，其中{stats['active']}个启用")

if __name__ == "__main__":
    main() 