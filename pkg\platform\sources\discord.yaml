apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: discord
  label:
    en_US: Discord
    zh_Hans: Discord
  description:
    en_US: Discord Adapter
    zh_Hans: Discord 适配器，请查看文档了解使用方式
  icon: discord.svg
spec:
  config:
    - name: client_id
      label:
        en_US: Client ID
        zh_Hans: 客户端ID
      type: string
      required: true
      default: ""
    - name: token
      label:
        en_US: Token
        zh_Hans: 令牌
      type: string
      required: true
      default: ""
execution:
  python:
    path: ./discord.py
    attr: DiscordAdapter
