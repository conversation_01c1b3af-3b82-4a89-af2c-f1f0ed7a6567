"""
二次开发功能测试配置文件
专门为重构后的核心模块提供测试配置和fixture
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from typing import Dict, Any

# 导入核心模块
try:
    from pkg.core.session.manager import SessionManager
    from pkg.core.image.processor import ImageProcessor
    from pkg.core.intent.analyzer import IntentAnalyzer
    from pkg.core.workflow.unified_routing_system import get_unified_router
    from pkg.core.message.processor import MessageProcessor
except ImportError as e:
    print(f"⚠️  无法导入核心模块: {e}")
    # 创建Mock对象作为fallback
    SessionManager = Mock()
    ImageProcessor = Mock()
    IntentAnalyzer = Mock()
    get_unified_router = Mock()
    MessageProcessor = Mock()


@pytest.fixture(scope="session")
def test_config():
    """测试配置"""
    return {
        "session_timeout": 3600,
        "max_sessions": 100,
        "image_quality": "high",
        "workflow_timeout": 300
    }


@pytest.fixture
def temp_dir():
    """临时目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_session():
    """模拟会话对象"""
    session = Mock()
    session.user_id = "test_user"
    session.is_active = True
    session.data = {}
    session.created_at = "2024-01-01T00:00:00Z"
    return session


@pytest.fixture
def mock_workflow_request():
    """模拟工作流请求"""
    return {
        "type": "image_generation",
        "parameters": {
            "prompt": "测试图片",
            "aspect_ratio": "1:1",
            "quality": "high"
        },
        "user_id": "test_user"
    }


@pytest.fixture
def mock_message():
    """模拟消息对象"""
    return {
        "content": "生成一张风景图片",
        "user_id": "test_user",
        "message_type": "text",
        "timestamp": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def session_manager():
    """会话管理器实例"""
    return SessionManager()


@pytest.fixture
def image_processor():
    """图片处理器实例"""
    return ImageProcessor()


@pytest.fixture
def intent_analyzer():
    """意图分析器实例"""
    return IntentAnalyzer()


@pytest.fixture
def unified_router():
    """统一路由器实例"""
    return get_unified_router()


@pytest.fixture
def message_processor():
    """消息处理器实例"""
    return MessageProcessor()


@pytest.fixture
def integrated_system():
    """集成系统实例"""
    return {
        "session_manager": SessionManager(),
        "image_processor": ImageProcessor(),
        "intent_analyzer": IntentAnalyzer(),
        "unified_router": get_unified_router(),
        "message_processor": MessageProcessor()
    }


# 标记定义
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "slow: 标记为慢速测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记为集成测试"
    )
    config.addinivalue_line(
        "markers", "unit: 标记为单元测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为所有测试添加默认标记
        if "test_custom_features" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # 为慢速测试添加标记
        if any(keyword in item.name.lower() for keyword in ["slow", "timeout", "concurrent"]):
            item.add_marker(pytest.mark.slow)


# 测试运行钩子
def pytest_runtest_setup(item):
    """测试运行前设置"""
    # 检查是否需要跳过测试
    if hasattr(item, 'get_closest_marker'):
        slow_marker = item.get_closest_marker('slow')
        if slow_marker and os.environ.get('SKIP_SLOW_TESTS'):
            pytest.skip("跳过慢速测试")


def pytest_runtest_teardown(item, nextitem):
    """测试运行后清理"""
    # 清理测试数据
    pass


# 自定义断言
class CustomAssertions:
    """自定义断言方法"""
    
    @staticmethod
    def assert_session_valid(session):
        """断言会话有效"""
        assert session is not None
        assert hasattr(session, 'user_id')
        assert hasattr(session, 'is_active')
        assert hasattr(session, 'data')
    
    @staticmethod
    def assert_workflow_result_valid(result):
        """断言工作流结果有效"""
        assert result is not None
        assert hasattr(result, 'status') or hasattr(result, 'success')
    
    @staticmethod
    def assert_intent_valid(intent):
        """断言意图分析结果有效"""
        assert intent is not None
        assert hasattr(intent, 'intent_type') or hasattr(intent, 'type') 