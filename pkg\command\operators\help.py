from __future__ import annotations

import typing
import os

from .. import operator, entities


@operator.operator_class(name='help', help='显示帮助', usage='!help\n/help\n/帮助\n!help <命令名称>')
class HelpOperator(operator.CommandOperator):
    async def execute(self, context: entities.ExecuteContext) -> typing.AsyncGenerator[entities.CommandReturn, None]:
        # 检查是否有参数，如果有参数则显示特定命令的帮助
        if len(context.crt_params) > 0:
            help = 'LangBot - 大语言模型原生即时通信机器人平台\n链接：https://langbot.app'
            help += '\n发送命令 !cmd 可查看命令列表'
            yield entities.CommandReturn(text=help)
            return
        
        # 没有参数时，返回用户指南
        try:
            # 读取用户指南文件
            guide_path = os.path.join(os.getcwd(), 'docs', 'KONTEXT_USER_GUIDE.md')
            
            if os.path.exists(guide_path):
                with open(guide_path, 'r', encoding='utf-8') as f:
                    guide_content = f.read()
                
                # 移除Markdown格式，保留纯文本内容
                guide_text = self._convert_markdown_to_text(guide_content)
                
                yield entities.CommandReturn(text=guide_text)
            else:
                # 如果文件不存在，返回简化的指南
                yield entities.CommandReturn(text=self._get_simple_guide())
                
        except Exception as e:
            self.ap.logger.error(f"读取用户指南失败: {e}")
            # 返回简化的指南作为备用
            yield entities.CommandReturn(text=self._get_simple_guide())
    
    def _convert_markdown_to_text(self, markdown_content: str) -> str:
        """将Markdown格式转换为纯文本"""
        lines = markdown_content.split('\n')
        text_lines = []
        
        for line in lines:
            # 移除Markdown标记
            line = line.strip()
            
            # 跳过空行
            if not line:
                continue
            
            # 移除标题标记
            if line.startswith('#'):
                # 保留标题内容，添加换行
                title = line.lstrip('#').strip()
                if title:
                    text_lines.append(f"\n{title}")
                    text_lines.append("=" * len(title))
                continue
            
            # 移除代码块标记
            if line.startswith('```'):
                continue
            
            # 移除列表标记
            if line.startswith('- ') or line.startswith('* '):
                line = '  • ' + line[2:]
            
            # 移除数字列表标记
            if line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
                line = '  ' + line
            
            # 保留普通文本
            if line:
                text_lines.append(line)
        
        return '\n'.join(text_lines)
    
    def _get_simple_guide(self) -> str:
        """返回简化的用户指南"""
        return """🎨 图片生成用户指南

📖 三种触发指令

系统支持三种简单的触发指令来启动图片生成服务：

🎯 Aigen本地工作流
aigen 一只可爱的猫咪
- 使用本地ComfyUI环境
- 支持纯文生图、控制图+文生图、参考图+文生图、混合模式
- 根据上传图片类型自动选择工作流

🏠 Kontext本地模式
kontext 编辑这张图片
- 使用本地ComfyUI环境
- 支持1图、2图、3图编辑
- 根据图片数量自动选择工作流

☁️ Kontext API模式
kontext api 编辑这张图片
- 使用云端ComfyUI API
- 支持1图、2图、3图编辑
- 适合快速生成

🎯 使用示例

Aigen工作流示例（单次生成）
用户: "aigen 生成一个科幻机器人"
[上传草图]
机器人: "🎨 检测到草图，选择ControlNet工作流"
用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]

基于生成图片的二次生成
用户: "aigen 一只可爱的猫咪"
机器人: "🎨 开始Aigen工作流..."
用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回图片]
[工作流结束]
用户: [引用刚才生成的图片] "aigen 让它更可爱一些"
机器人: "🎨 开始Aigen工作流..."
[检测到引用图片，选择参考图工作流]
用户: "开始"
机器人: "🚀 开始生成图片..."
[生成并返回新图片]
[工作流结束]

💡 使用技巧

1. 智能工作流选择：系统会根据图片类型和数量自动选择最适合的工作流
2. 会话式交互：启动后无需重复前缀，直接发送指令即可（仅在同一会话内）
3. 多轮调整：可以连续调整提示词，最后发送"开始"执行（仅在同一会话内）
4. 基于生成图片的编辑：引用生成的图片，重新发送前缀指令进行二次生成
5. 超时保护：会话超时自动中断，避免资源浪费

⚠️ 注意事项

- 本地模式需要ComfyUI服务运行
- API模式需要有效的API Key
- 会话超时时间：5分钟
- 支持"开始"、"go"、"执行"等指令触发生成
- 支持"取消"、"cancel"等指令中断工作流
- 支持引用消息中的图片和文本内容
- 生成完成后会话自动结束
- 后续消息会被视为普通聊天
- 如需基于生成图片继续编辑，请引用图片并重新发送前缀指令

🔧 工作流类型说明

Aigen工作流
• 纯文生图：无图片输入
• 控制图+文生图：草图、线稿等控制图像
• 参考图+文生图：风格、内容参考图像
• 混合模式：控制图+参考图+文字

Kontext工作流
• 单图编辑：1张图片输入
• 双图编辑：2张图片输入
• 多图编辑：3张图片输入

就是这么简单！记住三个指令，开始创作吧！ 🎨✨"""
