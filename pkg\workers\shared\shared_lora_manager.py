"""
共享 LoRA 管理器

提供跨工作流的 LoRA 模型管理功能
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class LoraCategory(Enum):
    """Lora模型分类"""
    ARCHITECTURE = "architecture"  # 建筑类
    PORTRAIT = "portrait"          # 人像类
    LANDSCAPE = "landscape"        # 风景类
    ANIME = "anime"                # 动漫类
    DETAIL = "detail"              # 细节增强类
    STYLE = "style"                # 风格类
    OBJECT = "object"              # 物体类
    OTHER = "other"                # 其他类


@dataclass
class LoraModel:
    """Lora模型配置"""
    name: str                    # 模型名称
    filename: str                # 文件名
    file_path: str               # 文件路径
    category: LoraCategory       # 分类
    weight: float                # 默认权重
    trigger_words: List[str]     # 触发词
    description: str             # 描述
    civitai_id: Optional[str]    # Civitai ID
    civitai_url: Optional[str]   # Civitai URL
    rating: Optional[float]      # 评分
    downloads: Optional[int]     # 下载次数
    is_local: bool               # 是否为本地模型
    is_active: bool              # 是否启用
    is_priority: bool            # 是否为优先模型（通用性强）


class SharedLoraManager:
    """共享 LoRA 管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lora_models: Dict[str, LoraModel] = {}
        self._initialized = False
        
    def initialize(self):
        """初始化管理器，加载配置文件中的模型"""
        if self._initialized:
            return
            
        try:
            # 尝试从JSON配置文件加载
            self._load_from_json_config()
            self.logger.info(f"从JSON配置文件加载了 {len(self.lora_models)} 个LoRA模型")
        except Exception as e:
            self.logger.warning(f"从JSON配置文件加载失败: {e}")
            try:
                # 尝试从YAML配置文件加载
                self._load_from_yaml_config()
                self.logger.info(f"从YAML配置文件加载了 {len(self.lora_models)} 个LoRA模型")
            except Exception as e2:
                self.logger.error(f"从YAML配置文件加载也失败: {e2}")
                # 加载默认模型
                self._load_default_models()
                self.logger.info("加载了默认LoRA模型")
        
        self._initialized = True
    
    def _load_from_json_config(self):
        """从JSON配置文件加载模型"""
        config_path = "config/lora_models.json"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        models_data = config_data.get('models', [])
        for model_data in models_data:
            try:
                # 映射分类字符串到枚举
                category_str = model_data.get('category', 'other')
                category = self._map_category_string(category_str)
                
                model = LoraModel(
                    name=model_data['name'],
                    filename=model_data['filename'],
                    file_path=model_data.get('file_path', ''),
                    category=category,
                    weight=model_data.get('weight', 0.8),
                    trigger_words=model_data.get('trigger_words', []),
                    description=model_data.get('description', ''),
                    civitai_id=model_data.get('civitai_id'),
                    civitai_url=model_data.get('civitai_url'),
                    rating=model_data.get('rating'),
                    downloads=model_data.get('downloads'),
                    is_local=model_data.get('is_local', True),
                    is_active=model_data.get('is_active', True),
                    is_priority=model_data.get('is_priority', False)
                )
                
                self.lora_models[model.name] = model
                
            except Exception as e:
                self.logger.warning(f"加载模型 {model_data.get('name', 'unknown')} 失败: {e}")
    
    def _load_from_yaml_config(self):
        """从YAML配置文件加载模型（简化版本）"""
        import yaml
        
        config_path = "config/lora_models.yaml"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 遍历所有分类
        for category_name, category_data in config_data.items():
            if category_name in ['global_config', 'workflow_presets']:
                continue
                
            if isinstance(category_data, dict) and 'models' in category_data:
                category_enum = self._map_category_string(category_name)
                
                for model_data in category_data['models']:
                    try:
                        model = LoraModel(
                            name=model_data['name'],
                            filename=model_data['file'],
                            file_path=f"/home/<USER>/Workspace/ComfyUI/models/loras/{model_data['file']}",
                            category=category_enum,
                            weight=model_data.get('strength', 0.8),
                            trigger_words=model_data.get('tags', []),
                            description=model_data.get('description', ''),
                            civitai_id=None,
                            civitai_url=None,
                            rating=None,
                            downloads=None,
                            is_local=True,
                            is_active=model_data.get('enabled', True),
                            is_priority=False
                        )
                        
                        self.lora_models[model.name] = model
                        
                    except Exception as e:
                        self.logger.warning(f"加载模型 {model_data.get('name', 'unknown')} 失败: {e}")
    
    def _load_default_models(self):
        """加载默认模型"""
        default_models = [
            LoraModel(
                name="detail_aidmafluxproultra-FLUX-v0.1",
                filename="detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                category=LoraCategory.DETAIL,
                weight=0.8,
                trigger_words=["细节", "detail", "高清", "high quality"],
                description="FLUX细节增强模型",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=True
            ),
            LoraModel(
                name="Anime_niji",
                filename="Anime_niji.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors",
                category=LoraCategory.ANIME,
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Niji动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            ),
            LoraModel(
                name="Flux Flat Anime",
                filename="Flux Flat Anime.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors",
                category=LoraCategory.ANIME,
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Flux扁平化动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            )
        ]
        
        for model in default_models:
            self.lora_models[model.name] = model
    
    def _map_category_string(self, category_str: str) -> LoraCategory:
        """映射分类字符串到枚举"""
        category_mapping = {
            "architecture": LoraCategory.ARCHITECTURE,
            "portrait": LoraCategory.PORTRAIT,
            "landscape": LoraCategory.LANDSCAPE,
            "anime": LoraCategory.ANIME,
            "detail": LoraCategory.DETAIL,
            "style": LoraCategory.STYLE,
            "object": LoraCategory.OBJECT,
            "other": LoraCategory.OTHER
        }
        return category_mapping.get(category_str.lower(), LoraCategory.OTHER)
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        total = len(self.lora_models)
        active = sum(1 for model in self.lora_models.values() if model.is_active)
        local = sum(1 for model in self.lora_models.values() if model.is_local)
        remote = total - local
        priority = sum(1 for model in self.lora_models.values() if model.is_priority)
        
        # 按分类统计
        by_category = {}
        for model in self.lora_models.values():
            category = model.category.value
            if category not in by_category:
                by_category[category] = 0
            by_category[category] += 1
        
        return {
            "total": total,
            "active": active,
            "local": local,
            "remote": remote,
            "priority": priority,
            "by_category": by_category
        }
    
    def get_models_by_category(self, category: LoraCategory) -> List[LoraModel]:
        """根据分类获取模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        return [model for model in self.lora_models.values() if model.category == category]
    
    def get_models_by_trigger(self, user_input: str) -> List[LoraModel]:
        """根据触发词获取模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        matching_models = []
        user_input_lower = user_input.lower()
        
        for model in self.lora_models.values():
            if not model.is_active:
                continue
                
            # 检查模型名称
            if user_input_lower in model.name.lower():
                matching_models.append(model)
                continue
            
            # 检查触发词
            for trigger_word in model.trigger_words:
                if trigger_word.lower() in user_input_lower:
                    matching_models.append(model)
                    break
            
            # 检查描述
            if user_input_lower in model.description.lower():
                matching_models.append(model)
        
        # 按优先级和评分排序
        matching_models.sort(key=lambda x: (x.is_priority, x.rating or 0), reverse=True)
        
        return matching_models
    
    def update_from_civitai(self, api_key: Optional[str] = None, query: str = "flux", limit: int = 50):
        """从Civitai更新模型信息（占位符实现）"""
        self.logger.info(f"从Civitai更新模型信息: {query} (限制: {limit})")
        # 这里应该实现实际的Civitai API调用
        return f"已更新 {limit} 个模型信息"
    
    def update_model(self, name: str, **kwargs):
        """更新模型配置"""
        if name not in self.lora_models:
            raise ValueError(f"模型不存在: {name}")
        
        model = self.lora_models[name]
        for key, value in kwargs.items():
            if hasattr(model, key):
                setattr(model, key, value)
        
        self.logger.info(f"更新模型配置: {name} - {kwargs}")
        return f"模型 {name} 配置已更新"


# 全局实例
_lora_manager = None


def get_lora_manager() -> SharedLoraManager:
    """获取全局 LoRA 管理器实例"""
    global _lora_manager
    if _lora_manager is None:
        _lora_manager = SharedLoraManager()
        _lora_manager.initialize()  # 自动初始化
    return _lora_manager 