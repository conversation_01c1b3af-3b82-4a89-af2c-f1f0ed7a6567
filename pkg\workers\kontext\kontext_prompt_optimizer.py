"""
Kontext 提示词优化模块
负责LLM润色、提示词增强等
"""
import re
import logging
from typing import Dict, Optional, Any

class KontextPromptOptimizer:
    """
    LLM提示词优化与增强
    """
    def __init__(self, lora_dict: Optional[Dict[str, str]] = None):
        self.lora_dict = lora_dict or {}
        self.logger = logging.getLogger(__name__)

    def optimize_prompt(self, prompt: str) -> str:
        """
        对输入的prompt进行优化（如润色、去噪、增强等）
        """
        if not prompt or not prompt.strip():
            return prompt

        # 基础清理
        cleaned_prompt = self._clean_prompt(prompt)

        # 简单的英文优化（如果是中文，建议使用LLM翻译）
        if self._is_chinese(cleaned_prompt):
            # 如果是中文，返回原文（需要LLM翻译）
            self.logger.info("检测到中文提示词，建议使用LLM翻译")
            return cleaned_prompt

        # 英文提示词优化
        optimized = self._enhance_english_prompt(cleaned_prompt)
        return optimized

    def _clean_prompt(self, prompt: str) -> str:
        """清理提示词"""
        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', prompt.strip())

        # 移除特殊字符（保留基本标点）
        cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)

        return cleaned

    def _is_chinese(self, text: str) -> bool:
        """检测是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))

    def _enhance_english_prompt(self, prompt: str) -> str:
        """增强英文提示词"""
        # 基础艺术风格增强
        enhanced = prompt

        # 如果没有质量词，添加基础质量描述
        quality_keywords = ['high quality', 'detailed', 'masterpiece', 'best quality']
        if not any(keyword in enhanced.lower() for keyword in quality_keywords):
            enhanced = f"high quality, detailed, {enhanced}"

        # 确保语法正确
        enhanced = enhanced.strip()
        if not enhanced.endswith('.'):
            enhanced += '.'

        return enhanced

    async def optimize_prompt_with_llm(self, prompt: str, query: Any) -> str:
        """
        使用LLM优化提示词（完整版本）
        """
        try:
            # 检测语言
            if self._is_chinese(prompt):
                # 中文需要翻译和优化
                return await self._translate_and_optimize(prompt, query)
            else:
                # 英文直接优化
                return await self._optimize_english_with_llm(prompt, query)
        except Exception as e:
            self.logger.error(f"LLM提示词优化失败: {e}")
            return self.optimize_prompt(prompt)  # 降级到基础优化

    async def _translate_and_optimize(self, chinese_prompt: str, query: Any) -> str:
        """翻译中文并优化"""
        try:
            # 构建翻译和优化的系统提示
            system_prompt = """你是一个专业的AI绘图提示词优化师。请将用户的中文描述转换为高质量的英文提示词。

要求：
1. 准确翻译原意
2. 增加艺术风格和技术细节
3. 使用专业的绘画术语
4. 优化语法和表达
5. 控制在200字以内
6. 只返回优化后的英文提示词，不要其他内容

示例：
输入：一只可爱的小猫
输出：a cute little kitten, fluffy fur, adorable expression, high quality, detailed, masterpiece, soft lighting, warm colors"""

            user_prompt = f"请优化这个中文提示词：{chinese_prompt}"

            # 调用LLM（需要传入query对象）
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)

            if optimized:
                return optimized.strip()
            else:
                # LLM调用失败，提示用户使用英文
                return self._prompt_for_english(chinese_prompt)

        except Exception as e:
            self.logger.error(f"翻译优化失败: {e}")
            return self._prompt_for_english(chinese_prompt)

    async def _optimize_english_with_llm(self, english_prompt: str, query: Any) -> str:
        """优化英文提示词"""
        try:
            system_prompt = """你是一个专业的AI绘图提示词优化师。请优化用户的英文提示词。

要求：
1. 保持原意不变
2. 增加艺术风格和技术细节
3. 优化语法和表达
4. 添加质量关键词
5. 控制在200字以内
6. 只返回优化后的英文提示词，不要其他内容"""

            user_prompt = f"请优化这个英文提示词：{english_prompt}"

            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)

            if optimized:
                return optimized.strip()
            else:
                return self._enhance_english_prompt(english_prompt)

        except Exception as e:
            self.logger.error(f"英文优化失败: {e}")
            return self._enhance_english_prompt(english_prompt)

    async def _call_llm_for_optimization(self, system_prompt: str, user_prompt: str, query: Any) -> Optional[str]:
        """调用LLM进行优化"""
        try:
            # 检查query对象
            if not query or not hasattr(query, 'pipeline_config') or not query.pipeline_config:
                self.logger.warning("无效的query对象，无法调用LLM")
                return None

            # 获取LLM模型配置
            model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
            if not model_uuid:
                self.logger.warning("未配置LLM模型，无法进行提示词优化")
                return None

            # 获取应用实例
            if not hasattr(query, 'ap') or not query.ap:
                self.logger.warning("无法获取应用实例，无法调用LLM")
                return None

            # 查找对应的RuntimeLLMModel
            runtime_llm_model = None
            for model in query.ap.model_mgr.llm_models:
                if model.model_entity.uuid == model_uuid:
                    runtime_llm_model = model
                    break

            if not runtime_llm_model:
                self.logger.warning(f"未找到模型 {model_uuid}，无法进行提示词优化")
                return None

            # 导入必要的模块
            from ...provider import entities as llm_entities

            # 创建消息
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]

            # 调用LLM
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )

            # 提取响应文本
            response_text = self._extract_response_text(result)
            if response_text:
                self.logger.info("LLM提示词优化成功")
                return response_text.strip()
            else:
                self.logger.warning("LLM返回空响应")
                return None

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""

        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)

        return response_text.strip()

    def _prompt_for_english(self, chinese_prompt: str) -> str:
        """提示用户使用英文提示词"""
        return f"⚠️ LLM翻译服务当前不可用，请直接提供英文提示词。原始输入：{chinese_prompt}"

    def apply_lora(self, prompt: str, lora_name: str) -> str:
        """
        根据lora_name将LoRA模型指令插入到prompt中
        """
        lora_tag = self.lora_dict.get(lora_name, f'<lora:{lora_name}>')
        return f"{lora_tag} {prompt}"

    def analyze_prompt(self, prompt: str) -> Dict[str, str]:
        """
        分析prompt，提取关键信息（如风格、主题等）
        """
        analysis = {
            "length": str(len(prompt)),
            "language": "chinese" if self._is_chinese(prompt) else "english",
            "has_quality_keywords": str(any(kw in prompt.lower() for kw in ['high quality', 'detailed', 'masterpiece'])),
            "word_count": str(len(prompt.split()))
        }
        return analysis

kontext_prompt_optimizer = KontextPromptOptimizer()