# 智能路由系统设计文档

## 问题背景

随着工作流类型的增加，原有的简单前缀路由机制（`aigen`、`kontext`）已经无法满足复杂需求：

1. **路由复杂性**：无法区分本地和API版本的工作流
2. **图像用途识别**：无法自动识别上传图片是控制图像还是参考图像
3. **多图处理**：多张图片时无法确定每张图片的具体用途
4. **用户认知负担**：需要用户记住复杂的参数和指令

## 解决方案：智能路由系统

### 核心设计理念

**基于意图分析 + 图像分析 + 上下文理解的智能路由系统**

### 系统架构

```
用户输入 → 智能路由系统 → 工作流选择 → 执行
    ↓           ↓           ↓         ↓
文本分析   图像分析    决策引擎   工作流执行
意图识别   类型识别    环境选择   结果返回
```

### 主要组件

#### 1. 智能路由器 (IntelligentRouter)

**功能**：
- 分析用户文本意图
- 分析图像类型和特征
- 做出智能路由决策
- 分配图像角色

**核心方法**：
```python
def analyze_user_intent(text: str, image_analyses: List[ImageAnalysis]) -> RoutingDecision
def analyze_images(images: List[bytes]) -> List[ImageAnalysis]
def _make_routing_decision(text, intent_analysis, image_types, image_count) -> Tuple[WorkflowType, WorkflowEnvironment, float, str]
```

#### 2. 图像分析器

**功能**：
- 使用PIL进行基础图像特征提取
- 基于特征分类图像类型
- 支持多种图像类型识别

**图像类型**：
- `REFERENCE`: 参考图像（风格、内容参考）
- `CONTROL`: 控制图像（ControlNet）
- `SKETCH`: 草图/线稿
- `MASK`: 蒙版图像
- `DEPTH`: 深度图
- `POSE`: 姿态图
- `CANNY`: Canny边缘图
- `OPENPOSE`: OpenPose姿态图

#### 3. 统一工作流处理器 (UnifiedWorkflowHandler)

**功能**：
- 整合所有工作流类型
- 提供统一的接口
- 管理用户会话
- 执行工作流

### 智能决策逻辑

#### 1. 明确指定工作流

```python
# Kontext工作流
if "kontext" in text_lower:
    return WorkflowType.KONTEXT, WorkflowEnvironment.LOCAL

# ControlNet工作流
if any(keyword in text_lower for keyword in ["controlnet", "控制", "引导", "草图"]):
    return WorkflowType.CONTROLNET, WorkflowEnvironment.LOCAL

# Redux工作流
if any(keyword in text_lower for keyword in ["redux", "参考", "风格"]):
    return WorkflowType.REDUX_REFERENCE, WorkflowEnvironment.API
```

#### 2. 基于图像类型智能判断

```python
# 多图混合模式
if image_count >= 2:
    if ImageType.SKETCH in image_types and ImageType.REFERENCE in image_types:
        return WorkflowType.HYBRID, WorkflowEnvironment.LOCAL

# 单图模式
if image_count == 1:
    if image_type == ImageType.SKETCH:
        return WorkflowType.CONTROLNET, WorkflowEnvironment.LOCAL
    elif image_type == ImageType.REFERENCE:
        return WorkflowType.REDUX_REFERENCE, WorkflowEnvironment.API
```

#### 3. 基于意图分析

```python
if intent_analysis.input_mode == "text_only":
    if intent_analysis.content_type == ContentType.PORTRAIT:
        return WorkflowType.PORTRAIT, WorkflowEnvironment.LOCAL
    elif intent_analysis.content_type == ContentType.LANDSCAPE:
        return WorkflowType.LANDSCAPE, WorkflowEnvironment.LOCAL
```

### 图像角色分配

#### Kontext工作流
- 所有图像都是参考图像

#### ControlNet工作流
- 草图图像 → SKETCH
- 其他图像 → CONTROL

#### Redux工作流
- 所有图像都是参考图像

#### 混合工作流
- 草图图像 → SKETCH
- 控制图像 → CONTROL
- 其他图像 → REFERENCE

### 使用示例

#### 示例1：草图控制生成
```
用户输入：上传一张草图 + "生成一个科幻机器人"
系统分析：
- 图像类型：SKETCH (置信度: 0.75)
- 意图：机器人生成
- 决策：ControlNet工作流 (置信度: 0.85)
- 环境：本地
```

#### 示例2：风格参考生成
```
用户输入：上传一张参考图 + "生成类似风格的风景"
系统分析：
- 图像类型：REFERENCE (置信度: 0.60)
- 意图：风格参考
- 决策：Redux工作流 (置信度: 0.80)
- 环境：API
```

#### 示例3：混合模式
```
用户输入：上传草图 + 参考图 + "生成科幻风格的角色"
系统分析：
- 图像1类型：SKETCH
- 图像2类型：REFERENCE
- 意图：混合生成
- 决策：混合工作流 (置信度: 0.85)
- 环境：本地
```

### 优势

#### 1. 降低用户认知负担
- 无需记忆复杂的前缀和参数
- 系统自动识别用户意图
- 智能选择最适合的工作流

#### 2. 提高准确性
- 基于图像内容分析
- 多维度决策逻辑
- 高置信度路由

#### 3. 支持复杂场景
- 多图混合模式
- 多种图像类型
- 灵活的环境选择

#### 4. 易于扩展
- 模块化设计
- 可配置的决策规则
- 支持新工作流类型

### 配置和扩展

#### 添加新工作流类型

1. 在 `WorkflowType` 枚举中添加新类型
2. 在 `ComfyUIWorkflowManager` 中注册工作流配置
3. 在 `IntelligentRouter` 中添加决策逻辑

#### 添加新图像类型

1. 在 `ImageType` 枚举中添加新类型
2. 在 `_classify_basic_image_type` 中添加分类逻辑
3. 在 `_assign_image_roles` 中添加角色分配

#### 自定义决策规则

```python
# 在 IntelligentRouter 中修改决策逻辑
def _make_routing_decision(self, text, intent_analysis, image_types, image_count):
    # 添加自定义规则
    if custom_condition:
        return custom_workflow_type, custom_environment, confidence, reasoning
```

### 部署和集成

#### 1. 依赖安装
```bash
pip install Pillow  # 图像处理
```

#### 2. 配置文件
```yaml
ai:
  comfyui-agent:
    api-url: "http://localhost:8188"
    timeout: 180
    workflow-path: "workflows"
```

#### 3. 集成到现有系统
```python
# 在 smart_hybrid_agent.py 中集成
from .unified_workflow_handler import UnifiedWorkflowHandler

class SmartHybridAgentRunner:
    def __init__(self, ap, pipeline_config):
        self.unified_handler = UnifiedWorkflowHandler(ap, pipeline_config)
    
    async def run(self, query):
        # 使用智能路由系统
        if self.unified_handler.is_workflow_request(user_text) or user_images:
            async for message in self.unified_handler.handle_workflow_request(
                user_text, user_images, user_id, query
            ):
                yield message
```

### 监控和调试

#### 日志输出
```python
# 路由决策日志
self.logger.info(f"智能路由决策: {workflow_type.value} (置信度: {confidence:.2f})")

# 图像分析日志
self.logger.info(f"图像分析结果: {image_type.value} (置信度: {confidence:.2f})")
```

#### 决策可视化
```python
# 格式化决策消息
message = intelligent_router.format_decision_message(decision, image_analyses)
```

### 未来改进

#### 1. 增强图像分析
- 集成OpenCV进行更精确的边缘检测
- 添加深度学习模型进行图像分类
- 支持更多图像类型识别

#### 2. 优化决策逻辑
- 使用机器学习模型进行意图分类
- 基于历史数据优化决策规则
- 支持个性化路由偏好

#### 3. 扩展工作流支持
- 支持更多工作流类型
- 动态工作流组合
- 自适应参数优化

#### 4. 用户体验优化
- 交互式确认机制
- 决策过程可视化
- 用户反馈学习

## 总结

智能路由系统通过结合意图分析、图像分析和上下文理解，成功解决了复杂工作流路由问题。系统具有以下特点：

1. **智能化**：自动分析用户意图和图像内容
2. **易用性**：降低用户认知负担，无需记忆复杂指令
3. **准确性**：基于多维度分析的智能决策
4. **扩展性**：模块化设计，易于添加新功能
5. **兼容性**：保持与现有系统的兼容性

这个系统为用户提供了更加自然和智能的工作流选择体验，同时为开发者提供了灵活和可扩展的架构。 