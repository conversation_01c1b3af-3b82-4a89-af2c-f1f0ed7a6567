# LLM路由系统修复总结

## 问题分析

通过对比旧版（`backup/old_routing_system/unified_llm_router.py`）和新版（`pkg/core/workflow/unified_routing_system.py`）的LLM调用实现，发现了导致新系统无法工作的关键差异。

## 主要问题

### 1. 错误的LLM调用方法

**问题位置**: `pkg/core/workflow/unified_routing_system.py` 第933行

**旧版（正确）**:
```python
result = await runtime_llm_model.requester.invoke_llm(
    query,
    runtime_llm_model,
    messages,
    [],  # 不需要工具调用
    extra_args={},
)
```

**新版（错误）**:
```python
response = await runtime_llm_model.chat(messages)  # 错误的方法调用
```

**修复**: 将错误的`runtime_llm_model.chat(messages)`改为正确的`runtime_llm_model.requester.invoke_llm(...)`

### 2. 缺少响应提取方法

**问题**: 新版缺少专门的`_extract_response_text`方法来处理LLM响应

**修复**: 添加了与旧版相同的响应提取方法:
```python
def _extract_response_text(self, result) -> str:
    """提取LLM响应文本 - 与旧版保持一致"""
    response_text = ""
    
    if hasattr(result, 'content') and result.content:
        if isinstance(result.content, list):
            for element in result.content:
                if hasattr(element, 'text') and element.text:
                    response_text += element.text
        elif isinstance(result.content, str):
            response_text = result.content
        else:
            response_text = str(result.content)
    
    return response_text.strip()
```

### 3. JSON解析错误处理不完整

**问题**: 新版的JSON解析错误处理不如旧版完善

**修复**: 
- 添加了更详细的错误日志
- 增加了JSON提取的备用方案（使用正则表达式）
- 改进了异常处理逻辑

### 4. 工作流文件名错误

**问题**: 代码中硬编码的工作流文件名与实际文件不匹配

**修复**:
- `flux_reference.json` → `flux_redux.json`
- `kontext_single.json` → `kontext_local_1image.json`
- `kontext_multiple.json` → `kontext_local_2images.json`
- `kontext_api_single.json` → `kontext_api_1image.json`
- `kontext_api_multiple.json` → `kontext_api_2images.json`

## 修复内容

### 1. 修复LLM调用方法 (`_route_with_llm`方法)

```python
# 修复前
response = await runtime_llm_model.chat(messages)

# 修复后
result = await runtime_llm_model.requester.invoke_llm(
    query,
    runtime_llm_model,
    messages,
    [],  # 不需要工具调用
    extra_args={},
)
```

### 2. 添加响应提取方法

```python
def _extract_response_text(self, result) -> str:
    """提取LLM响应文本 - 与旧版保持一致"""
    # ... 实现细节
```

### 3. 改进JSON解析

```python
# 清理和解析JSON
cleaned_text = self._clean_json_response(response_text)
parsed_result = json.loads(cleaned_text)

# 添加备用JSON提取方案
except json.JSONDecodeError as e:
    self.logger.warning(f"LLM响应不是有效的JSON格式: {e}, 响应内容: {response_text}")
    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
    if json_match:
        try:
            parsed_result = json.loads(json_match.group())
            # ... 处理提取的JSON
        except:
            pass
```

### 4. 修复工作流文件映射

所有硬编码的工作流文件名都已更正为实际存在的文件名。

## 测试验证

创建了测试脚本 `test_llm_routing_fix.py` 来验证修复效果：

```bash
python test_llm_routing_fix.py
```

测试结果显示：
- ✅ 参数分析功能正常
- ✅ 意图分析功能正常  
- ✅ 统一路由功能正常
- ✅ LLM调用方法正常

## 关键改进

1. **统一调用方式**: 新版现在使用与旧版相同的LLM调用方式
2. **完善错误处理**: 增加了更robust的JSON解析和错误恢复机制
3. **修复文件映射**: 确保所有工作流文件名正确
4. **保持兼容性**: 与旧版的响应处理逻辑保持一致

## 结论

通过这些修复，新版统一路由系统的LLM调用功能现在应该能够正常工作，与旧版保持相同的行为和可靠性。主要问题是错误的方法调用和不完善的响应处理，现在都已得到解决。
