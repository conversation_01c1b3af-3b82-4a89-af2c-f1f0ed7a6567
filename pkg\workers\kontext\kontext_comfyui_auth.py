"""
Kontext ComfyUI认证模块
负责ComfyUI API Key认证逻辑
"""
from typing import Optional
import os

class KontextComfyUIAuth:
    """
    ComfyUI API Key认证与管理
    """
    def __init__(self, api_key_env: str = 'API_KEY_COMFY_ORG'):
        self.api_key_env = api_key_env

    def get_api_key(self) -> Optional[str]:
        """
        获取API Key（从环境变量）
        """
        return os.environ.get(self.api_key_env)

    def is_authenticated(self) -> bool:
        """
        检查API Key是否存在
        """
        return bool(self.get_api_key())

    def set_api_key(self, key: str):
        """
        设置API Key到环境变量（仅本地测试用）
        """
        os.environ[self.api_key_env] = key

kontext_comfyui_auth = KontextComfyUIAuth()

# TODO: 实现ComfyUI认证相关方法 