#!/usr/bin/env python3
"""
调试工作流执行过程
检查提交到ComfyUI的实际数据
"""

import base64
import json
import os
import sys
import aiohttp
import asyncio
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

async def test_workflow_submission(workflow_data: Dict[str, Any], api_url: str = "http://localhost:8188"):
    """
    测试工作流提交到ComfyUI
    
    Args:
        workflow_data: 工作流数据
        api_url: ComfyUI API地址
    """
    print(f"🚀 测试工作流提交到ComfyUI: {api_url}")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # 准备提交数据
            prompt_data = {"prompt": workflow_data}
            
            print(f"📋 提交数据大小: {len(json.dumps(prompt_data))} 字符")
            
            # 检查关键节点
            print("\n🔍 检查关键节点:")
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict):
                    class_type = node_data.get("class_type", "")
                    if class_type == "easy loadImageBase64":
                        title = node_data.get("_meta", {}).get("title", "")
                        inputs = node_data.get("inputs", {})
                        base64_data = inputs.get("base64_data", "")
                        
                        print(f"  🔸 节点 {node_id}: {title}")
                        print(f"    📝 base64_data长度: {len(base64_data)} 字符")
                        
                        if base64_data:
                            # 验证base64数据
                            try:
                                decoded_data = base64.b64decode(base64_data)
                                print(f"    ✅ 解码成功，大小: {len(decoded_data)} bytes")
                                
                                # 检查图片格式
                                if decoded_data.startswith(b'\xff\xd8\xff'):
                                    print(f"    📷 格式: JPEG")
                                elif decoded_data.startswith(b'\x89PNG\r\n\x1a\n'):
                                    print(f"    📷 格式: PNG")
                                else:
                                    print(f"    ⚠️  未知格式，头部: {decoded_data[:16].hex()}")
                                    
                            except Exception as e:
                                print(f"    ❌ 解码失败: {e}")
                        else:
                            print(f"    ⚠️  base64_data为空")
            
            # 提交工作流
            print(f"\n📤 提交工作流...")
            async with session.post(f"{api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    print(f"✅ 提交成功，prompt_id: {prompt_id}")
                    
                    # 检查队列状态
                    print(f"\n📊 检查队列状态...")
                    async with session.get(f"{api_url}/queue") as queue_response:
                        if queue_response.status == 200:
                            queue_data = await queue_response.json()
                            print(f"✅ 队列状态: {json.dumps(queue_data, indent=2)}")
                        else:
                            print(f"❌ 获取队列状态失败: {queue_response.status}")
                    
                    return prompt_id
                else:
                    error_text = await response.text()
                    print(f"❌ 提交失败: {response.status}, {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        return None

def create_test_workflow_with_image(test_image_data: bytes) -> Dict[str, Any]:
    """
    创建包含测试图片的工作流
    
    Args:
        test_image_data: 测试图片数据
        
    Returns:
        Dict[str, Any]: 工作流数据
    """
    # 转换为base64
    image_base64 = base64.b64encode(test_image_data).decode('utf-8')
    
    # 创建简单的工作流
    workflow = {
        "1": {
            "inputs": {
                "base64_data": image_base64,
                "image_output": "Preview",
                "save_prefix": "ComfyUI"
            },
            "class_type": "easy loadImageBase64",
            "_meta": {
                "title": "test_image_input"
            }
        },
        "2": {
            "inputs": {
                "images": ["1", 0]
            },
            "class_type": "PreviewImage",
            "_meta": {
                "title": "Preview Image"
            }
        }
    }
    
    return workflow

async def test_comfyui_connection(api_url: str = "http://localhost:8188"):
    """
    测试ComfyUI连接
    
    Args:
        api_url: ComfyUI API地址
    """
    print(f"🔗 测试ComfyUI连接: {api_url}")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            # 测试基本连接
            async with session.get(f"{api_url}/system_stats") as response:
                if response.status == 200:
                    stats = await response.json()
                    print(f"✅ ComfyUI连接正常")
                    print(f"📊 系统状态: {json.dumps(stats, indent=2)}")
                    return True
                else:
                    print(f"❌ ComfyUI连接失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 工作流执行调试工具")
    print("=" * 60)
    
    # 测试ComfyUI连接
    api_url = "http://localhost:8188"
    if not await test_comfyui_connection(api_url):
        print("❌ ComfyUI连接失败，无法继续测试")
        return
    
    # 创建测试图片数据
    test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
    
    print(f"📷 测试图片大小: {len(test_image_data)} bytes")
    
    # 创建测试工作流
    workflow = create_test_workflow_with_image(test_image_data)
    print(f"📋 创建测试工作流，包含 {len(workflow)} 个节点")
    
    # 测试工作流提交
    prompt_id = await test_workflow_submission(workflow, api_url)
    
    if prompt_id:
        print(f"\n✅ 测试完成，prompt_id: {prompt_id}")
    else:
        print(f"\n❌ 测试失败")
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    asyncio.run(main()) 