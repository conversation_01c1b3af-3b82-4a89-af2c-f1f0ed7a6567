# 两级路由架构设计文档

## 📋 文档信息

- **创建时间**: 2025-01-07
- **版本**: 1.0
- **状态**: 已实现
- **目的**: 记录ComfyUI Agent的正确两级路由架构设计和完整执行流程

## 🎯 架构概述

LangBot的图片生成系统采用**两级路由架构**，将路由决策分为两个独立的阶段：

1. **第一级：管道路由** - 基于触发词确定生成管道
2. **第二级：工作流路由** - 基于完整信息选择具体工作流

这种设计确保了路由决策的清晰性和准确性，避免了混乱的"重新路由"概念。

## 🏗️ 第一级：管道路由（触发词阶段）

### 设计目标
- **简单可靠**：基于明确的触发词进行快速路由
- **高可用性**：99.99%的可靠性，<5ms响应时间
- **明确边界**：仅确定生成管道，不涉及具体工作流选择

### 路由规则

| 触发词 | 管道类型 | 描述 |
|---------|----------|------|
| `aigen` | AIGEN | Flux本地文生图管道 |
| `kontext` | KONTEXT | 本地图生图管道 |
| `kontext_api` | KONTEXT_API | 远程API图像处理管道 |

### 实现逻辑
```python
def _determine_pipeline_type(self, user_text: str) -> WorkflowType:
    """
    第一级管道路由：基于触发词确定管道类型
    """
    user_text_lower = user_text.lower().strip()
    
    # 检查精确触发词匹配
    if user_text_lower.startswith("aigen "):
        return WorkflowType.AIGEN
    elif user_text_lower.startswith("kontext "):
        return WorkflowType.KONTEXT
    elif user_text_lower.startswith("kontext_api "):
        return WorkflowType.KONTEXT_API
    
    # 默认返回AIGEN
    return WorkflowType.AIGEN
```

### 处理流程
1. 用户发送消息：`aigen一只小猫`
2. 验证触发条件：
   - **群消息**：@机器人 + 触发词前缀
   - **单独对话**：仅触发词前缀
3. 执行管道路由：提取触发词 `aigen` → 确定AIGEN管道
4. 创建会话：基于管道类型创建活跃会话
5. 等待指令：提示用户发送"开始"指令

## 🧠 第二级：工作流路由（开始指令阶段）

### 设计目标
- **智能分析**：利用LLM分析收集到的完整信息
- **精确选择**：在确定的管道内选择最合适的工作流
- **参数优化**：生成优化的英文提示词和工作流参数

### 分析维度
- **文本内容**：用户提示词的语义分析
- **图片数量**：0张、1张、2张、多张的不同处理策略
- **图片类型**：控制图 vs 参考图的智能识别
- **用户意图**：基于关键词和上下文的意图推断

### 各管道的工作流选择策略

#### AIGEN管道 - 复杂智能路由
基于LLM分析文本内容和图片类型进行智能选择：

| 条件 | 工作流子类型 | 工作流文件 | 说明 |
|------|-------------|------------|------|
| 无图片 | AIGEN_TEXT_ONLY | flux_default.json | 纯文生图 |
| 1张图片 + 控制关键词 | AIGEN_CONTROL_ONLY | flux_controlnet.json | ControlNet控制图 |
| 1张图片 + 参考关键词 | AIGEN_REFERENCE_ONLY | flux_redux.json | 参考图生成 |
| 2张图片 | AIGEN_CONTROL_REFERENCE | flux_controlnet_redux.json | 控制+参考混合 |
| 多张图片 | 需要用户确认 | - | 询问用户意图 |

#### KONTEXT管道 - 简单数量路由
基于图片数量进行简单选择：

| 图片数量 | 工作流子类型 | 工作流文件 | 说明 |
|----------|-------------|------------|------|
| 1张图片 | KONTEXT_1IMAGE | kontext_1image.json | 单图处理 |
| 2张图片 | KONTEXT_2IMAGE | kontext_2image.json | 双图处理 |
| 3张图片 | KONTEXT_3IMAGE | kontext_3image.json | 三图处理 |
| 无图片或多于3张 | 错误提示 | - | 不支持的图片数量 |

#### KONTEXT_API管道 - 简单数量路由
基于图片数量进行简单选择：

| 图片数量 | 工作流子类型 | API端点 | 说明 |
|----------|-------------|---------|------|
| 1张图片 | KONTEXT_API_1IMAGE | /api/v1/1image | 单图API处理 |
| 2张图片 | KONTEXT_API_2IMAGE | /api/v1/2image | 双图API处理 |
| 3张图片 | KONTEXT_API_3IMAGE | /api/v1/3image | 三图API处理 |
| 无图片或多于3张 | 错误提示 | - | 不支持的图片数量 |

### 关键词识别

**控制图关键词**：
- 中文：控制、轮廓、姿势、结构、保持形状、参考布局
- 英文：control、outline、pose、structure、shape、layout

**参考图关键词**：
- 中文：参考、风格、类似、像这样、参考风格
- 英文：reference、style、similar、like this、color

### 各管道的LLM分析流程

#### AIGEN管道 - 复杂智能分析
```python
async def _execute_aigen_workflow(self, session, query):
    # 1. 收集会话数据
    user_text = session.prompt
    session_images = session.images
    
    # 2. LLM智能路由分析（复杂分析）
    routing_result = await self.route_workflow_intelligently(
        user_text=user_text,
        query=query, 
        attached_images=[img.data for img in session_images]
    )
    
    # 3. 记录分析结果
    if routing_result:
        logger.info(f"工作流子类型: {routing_result.workflow_subtype.value}")
        logger.info(f"置信度: {routing_result.confidence.value}")
        logger.info(f"推理: {routing_result.reasoning}")
        logger.info(f"建议提示词: {routing_result.suggested_prompt}")
        logger.info(f"工作流文件: {routing_result.workflow_file}")
    
    # 4. 执行工作流...
```

#### KONTEXT/KONTEXT_API管道 - 简单数量分析
```python
async def _execute_kontext_workflow(self, session, query):
    # 1. 收集会话数据
    user_text = session.prompt
    session_images = session.images
    
    # 2. 简单数量路由（无需LLM分析）
    image_count = len(session_images)
    
    # 3. 基于图片数量选择工作流
    if image_count == 1:
        workflow_subtype = "KONTEXT_1IMAGE"
        workflow_file = "kontext_1image.json"
    elif image_count == 2:
        workflow_subtype = "KONTEXT_2IMAGE"
        workflow_file = "kontext_2image.json"
    elif image_count == 3:
        workflow_subtype = "KONTEXT_3IMAGE"
        workflow_file = "kontext_3image.json"
    else:
        # 错误处理：不支持的图片数量
        raise ValueError(f"不支持的图片数量: {image_count}")
    
    # 4. 执行工作流...
```

## 🔄 完整执行流程

### 流程图
整个两级路由架构的完整流程如下图所示：

```mermaid
graph TB
    A[用户发送生图指令] --> B{验证触发条件}
    B -->|群消息: @机器人 + 触发词<br/>单独对话: 仅触发词| C[第一级：管道路由]
    B -->|条件不满足| END1[忽略消息]
    
    C --> D{识别触发词}
    D -->|aigen| E1[AIGEN管道]
    D -->|kontext| E2[KONTEXT管道]
    D -->|kontext_api| E3[KONTEXT_API管道]
    
    E1 --> F[创建活跃会话]
    E2 --> F
    E3 --> F
    F --> G[状态: COLLECTING]
    G --> H[返回: 发送开始指令生成图片]
    
    H --> I[用户可继续发送]
    I --> J[补充文字/上传图片/修改需求]
    J --> K[更新会话信息]
    K --> I
    
    I --> L[用户发送: 开始]
    L --> M{检测执行指令}
    M -->|是开始指令| N[第二级：工作流路由]
    M -->|是取消指令| END2[取消会话]
    
    N --> O{判断管道类型}
    O -->|AIGEN管道| P1[AIGEN工作流选择]
    O -->|KONTEXT管道| P2[KONTEXT工作流选择]
    O -->|KONTEXT_API管道| P3[KONTEXT_API工作流选择]
    
    P1 --> Q1[LLM分析文本+图片类型]
    Q1 --> R1[复杂智能路由决策]
    R1 --> S1[TEXT_ONLY/CONTROL_ONLY/<br/>REFERENCE_ONLY/CONTROL_REFERENCE]
    
    P2 --> Q2[统计图片数量]
    Q2 --> R2[简单数量路由]
    R2 --> S2[1image/2image/3image<br/>工作流选择]
    
    P3 --> Q3[统计图片数量]
    Q3 --> R3[简单数量路由]
    R3 --> S3[1image/2image/3image<br/>工作流选择]
    
    S1 --> T[参数优化]
    S2 --> T
    S3 --> T
    
    T --> U[组织工作流数据]
    U --> V[提交ComfyUI API]
    V --> W[等待生成结果]
    W --> X[返回生成图片]
    X --> Y[清理会话]
    Y --> Z[流程结束]
```

### 阶段1：请求检测和管道路由

#### 群消息示例
```
用户输入: "@机器人 aigen一只小猫"
    ↓
验证触发条件: @机器人 + aigen前缀 ✓
    ↓
第一级管道路由: 识别"aigen" → AIGEN管道
    ↓
创建活跃会话: 
  - 管道类型: AIGEN
  - 提示词: "一只小猫"
  - 图片: 0张
  - 状态: COLLECTING
    ↓
返回响应: "🎨 Aigen工作流已启动，发送'开始'指令生成图片"
```

#### 单独对话示例
```
用户输入: "kontext处理这张图片"
    ↓
验证触发条件: kontext前缀 ✓ (无需@机器人)
    ↓
第一级管道路由: 识别"kontext" → KONTEXT管道
    ↓
创建活跃会话: 
  - 管道类型: KONTEXT
  - 提示词: "处理这张图片"
  - 图片: 0张
  - 状态: COLLECTING
    ↓
返回响应: "🎨 Kontext工作流已启动，发送'开始'指令生成图片"
```

### 阶段2：信息收集（可选）
```
用户可以继续发送:
  - 补充文字: "要很可爱的那种"
  - 上传图片: [参考图片]
  - 修改提示词: "改成小狗"
    ↓
会话状态更新:
  - 提示词: "一只小猫要很可爱的那种改成小狗"
  - 图片: 1张
  - 状态: 仍为COLLECTING
```

### 阶段3：工作流路由和执行

#### AIGEN管道 - 复杂智能路由
```
用户输入: "开始"
    ↓
检测执行指令: is_execution_command("开始") → True
    ↓
第二级工作流路由 (AIGEN管道):
  1. LLM分析完整会话数据
     - 文本: "一只小猫要很可爱的那种改成小狗"
     - 图片: 1张
     - 图片类型: 参考图
  
  2. 复杂智能路由决策
     - 工作流: AIGEN_REFERENCE_ONLY
     - 文件: flux_redux.json
     - 置信度: HIGH
  
  3. 参数优化
     - 英文提示词: "A very cute puppy, adorable, kawaii style"
     - 尺寸: 1024x1024
     - 种子: 随机
     - 引导值: 3.5
    ↓
工作流执行:
  1. 组织工作流数据
  2. 提交到ComfyUI API
  3. 等待生成结果
  4. 返回生成图片
    ↓
会话清理: 删除活跃会话
```

#### KONTEXT管道 - 简单数量路由
```
用户输入: "开始"
    ↓
检测执行指令: is_execution_command("开始") → True
    ↓
第二级工作流路由 (KONTEXT管道):
  1. 统计会话数据
     - 文本: "处理这张图片"
     - 图片: 2张
  
  2. 简单数量路由决策
     - 工作流: KONTEXT_2IMAGE
     - 文件: kontext_2image.json
     - 说明: 基于图片数量自动选择
  
  3. 参数设置
     - 保持原始提示词: "处理这张图片"
     - 使用默认参数配置
    ↓
工作流执行:
  1. 组织工作流数据
  2. 提交到ComfyUI API
  3. 等待生成结果
  4. 返回生成图片
    ↓
会话清理: 删除活跃会话
```

## 🏷️ 会话状态管理

### 会话生命周期
```
IDLE (空闲)
  ↓ 检测到触发词
COLLECTING (收集信息)
  ↓ 收到"开始"指令  
PROCESSING (执行工作流)
  ↓ 生成完成
COMPLETED (完成)
  ↓ 自动清理
IDLE (空闲)
```

### 状态转换规则
- `IDLE` → `COLLECTING`: 检测到有效的图片生成请求
- `COLLECTING` → `COLLECTING`: 用户继续添加内容
- `COLLECTING` → `PROCESSING`: 收到执行指令
- `COLLECTING` → `CANCELLED`: 收到取消指令
- `PROCESSING` → `COMPLETED`: 工作流执行成功
- `PROCESSING` → `CANCELLED`: 工作流执行失败或用户取消
- `COMPLETED` → `IDLE`: 自动清理会话

## 💡 关键设计原则

### 1. 职责分离
- **第一级**：只负责管道选择，不涉及具体工作流
- **第二级**：只负责工作流选择，基于完整信息分析

### 2. 信息完整性
- 第一级路由时信息不完整（只有触发词）
- 第二级路由时信息完整（文本+图片+上下文）

### 3. 决策时机
- **管道路由**：立即决策，基于明确规则
- **工作流路由**：延迟决策，基于LLM分析

### 4. 错误处理
- 第一级失败：返回错误，不创建会话
- 第二级失败：使用默认工作流，记录错误

## 🚀 实现优势

### 1. 架构清晰
- 避免了"重新路由"的混乱概念
- 每个阶段职责明确，易于维护

### 2. 性能优化
- 第一级基于规则，响应快速
- 第二级基于LLM，但只在必要时调用

### 3. 用户体验
- 支持分步操作，用户可以逐步完善需求
- 提供清晰的状态反馈

### 4. 扩展性强
- 新增管道：只需修改第一级路由
- 新增工作流：只需修改第二级路由
- 两级独立，互不影响

## 🔧 技术实现要点

### 1. 触发词检测
```python
def _is_valid_image_generation_request(self, user_text, query, user_id, chat_id):
    # 区分群消息和单独对话：
    # 群消息：检查 @机器人 + 触发词前缀
    # 单独对话：仅检查触发词前缀
    # 支持PrefixRule处理前后的文本检测
```

### 2. 管道类型确定
```python
def _determine_pipeline_type(self, user_text):
    # 基于触发词确定管道类型
    # 处理PrefixRule移除前缀的情况
```

### 3. LLM智能路由
```python
async def route_workflow_intelligently(self, user_text, query, attached_images):
    # 调用统一路由系统
    # 基于完整信息进行工作流选择
```

### 4. 工作流执行
```python
async def _execute_aigen_workflow(self, session, query):
    # 重新进行LLM路由分析
    # 基于最新的会话信息选择工作流
    # 组织参数并调用ComfyUI API
```

## 📊 性能指标

### 第一级管道路由
- **响应时间**: < 5ms
- **可靠性**: 99.99%
- **内存占用**: < 1MB

### 第二级工作流路由
- **响应时间**: < 2000ms (包含LLM调用)
- **准确率**: > 85%
- **置信度评估**: 支持HIGH/MEDIUM/LOW三级

## 🐛 常见问题和解决方案

### Q1: 用户发送触发词但没有后续操作怎么办？
**A**: 会话会在10分钟后自动超时清理，避免资源占用。

### Q2: LLM路由分析失败怎么办？
**A**: 使用默认工作流作为回退方案，确保流程不中断。

### Q3: 用户在收集阶段发送了错误的图片怎么办？
**A**: 支持重新上传，会话会保留最新的图片集合。

### Q4: 如何处理复杂的多图片场景？
**A**: LLM会分析图片用途，必要时询问用户确认。

## 📝 更新记录

| 版本 | 日期 | 更新内容 | 负责人 |
|------|------|----------|--------|
| 1.0 | 2025-01-07 | 初始版本，记录两级路由架构设计 | Claude |
| 1.1 | 2025-01-07 | 重要更新：<br/>1. 明确触发条件区分（群消息vs单独对话）<br/>2. 补充KONTEXT/KONTEXT_API管道的简单数量路由<br/>3. 更新流程图反映所有三个管道<br/>4. 添加各管道的详细测试用例 | Claude |

## 🔗 相关文档

- [统一路由系统设计](./PRD-********-UnifiedRoutingSystem.md)
- [ComfyUI集成指南](../COMFYUI_INTEGRATION.md)
- [工作流管理指南](../workflow-management-guide.md)
- [用户交互流程](../USER_INTERACTION_FLOW.md)

---

**注意**: 本文档记录的是当前已实现并验证的架构设计。如需修改路由逻辑，请先更新本文档，确保架构一致性。 