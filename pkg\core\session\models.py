"""
会话管理数据模型
统一的会话状态、工作流类型和会话对象定义
"""

import time
import hashlib
import uuid
from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime


class WorkflowType(Enum):
    """工作流类型枚举"""
    AIGEN = "aigen"           # 本地Flux工作流
    KONTEXT = "kontext"       # 本地kontext工作流
    KONTEXT_API = "kontext_api"  # 远程API工作流
    UNKNOWN = "unknown"       # 未知类型


class FluxImageType(Enum):
    """Flux工作流图片类型枚举"""
    CONTROL = "control"       # ControlNet控制图
    REFERENCE = "reference"   # Redux参考图
    MIXED = "mixed"           # 混合用途
    UNKNOWN = "unknown"       # 未知类型


class SessionState(Enum):
    """会话状态枚举"""
    IDLE = "idle"                      # 空闲状态
    COLLECTING = "collecting"          # 收集参数中
    WAITING_FOR_PROMPT = "waiting_for_prompt"     # 等待提示词
    WAITING_FOR_IMAGES = "waiting_for_images"     # 等待图片
    WAITING_FOR_IMAGE_TYPES = "waiting_for_image_types"  # 等待图片类型确认（Flux专用）
    READY = "ready"                    # 准备执行
    READY_FOR_GENERATION = "ready_for_generation"  # 准备生成
    PROCESSING = "processing"          # 执行中
    GENERATING = "generating"          # 正在生成
    COMPLETED = "completed"            # 已完成
    CANCELLED = "cancelled"            # 已取消
    TIMEOUT = "timeout"                # 超时


def session_state_from_string(state_str: str) -> SessionState:
    """从字符串转换为SessionState"""
    try:
        return SessionState(state_str)
    except ValueError:
        return SessionState.IDLE


@dataclass
class SessionImage:
    """会话中的图片信息"""
    data: bytes
    purpose: str = "reference"  # reference, sketch, control, input
    source: str = "upload"      # upload, quoted, generated
    flux_image_type: FluxImageType = FluxImageType.UNKNOWN  # Flux专用图片类型
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_hash(self) -> str:
        """获取图片哈希值"""
        return hashlib.md5(self.data).hexdigest()
    
    def get_size(self) -> int:
        """获取图片大小（字节）"""
        return len(self.data)
    
    def is_control_image(self) -> bool:
        """是否为控制图"""
        return self.flux_image_type == FluxImageType.CONTROL
    
    def is_reference_image(self) -> bool:
        """是否为参考图"""
        return self.flux_image_type == FluxImageType.REFERENCE
    
    def is_mixed_image(self) -> bool:
        """是否为混合用途图片"""
        return self.flux_image_type == FluxImageType.MIXED


@dataclass 
class WorkflowSession:
    """统一工作流会话对象"""
    session_id: str
    user_id: str
    chat_id: str = ""
    workflow_type: WorkflowType = WorkflowType.UNKNOWN
    state: SessionState = SessionState.COLLECTING
    
    # 核心数据
    prompt: str = ""
    images: List[SessionImage] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 引用消息相关
    quoted_text: str = ""
    quoted_images: List[bytes] = field(default_factory=list)
    
    # 路由结果
    routing_result: Optional[Any] = None  # 存储路由决策结果
    
    # 时间管理
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    timeout_minutes: int = 10
    
    # 工作流限制
    max_images: int = 3
    min_images: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
        
        # 根据工作流类型设置默认限制
        if self.workflow_type == WorkflowType.AIGEN:
            self.min_images = 0
            self.max_images = 3
        elif self.workflow_type == WorkflowType.KONTEXT:
            self.min_images = 1
            self.max_images = 3
        elif self.workflow_type == WorkflowType.KONTEXT_API:
            self.min_images = 1
            self.max_images = 3
    
    def is_active(self) -> bool:
        """检查会话是否活跃（未超时）"""
        return (time.time() - self.updated_at) < (self.timeout_minutes * 60)
    
    def is_expired(self) -> bool:
        """检查会话是否已过期"""
        return not self.is_active()
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = time.time()
    
    def update_activity(self):
        """更新最后活动时间"""
        self.update_timestamp()
    
    def add_image(self, image_data: bytes, purpose: str = "reference", source: str = "upload") -> bool:
        """
        添加图片到会话
        返回是否成功添加
        """
        # 检查是否已达到最大图片数
        if self.get_image_count() >= self.max_images:
            return False
        
        # 检查是否重复
        new_hash = hashlib.md5(image_data).hexdigest()
        for existing_image in self.images:
            if existing_image.get_hash() == new_hash:
                return False  # 图片已存在
        
        session_image = SessionImage(
            data=image_data,
            purpose=purpose,
            source=source
        )
        self.images.append(session_image)
        self.update_activity()
        return True
    
    def add_quoted_images(self, images: List[bytes]):
        """添加引用图片"""
        for image_data in images:
            self.add_image(image_data, purpose="reference", source="quoted")
    
    def set_quoted_text(self, text: str):
        """设置引用文本"""
        self.quoted_text = text
        self.update_activity()
    
    def set_prompt(self, prompt: str):
        """设置提示词"""
        self.prompt = prompt.strip()
        self.update_activity()
    
    def can_add_image(self) -> bool:
        """检查是否还能添加图片"""
        return self.get_image_count() < self.max_images
    
    def has_enough_images(self) -> bool:
        """检查是否有足够的图片"""
        return self.get_image_count() >= self.min_images
    
    def has_prompt(self) -> bool:
        """检查是否有提示词"""
        return bool(self.prompt.strip())
    
    def is_ready_for_execution(self) -> bool:
        """检查是否准备好执行"""
        # 新的逻辑：只要有触发词就可以执行，具体工作流选择在go指令时决定
        return (
            self.is_active() and
            self.has_prompt()  # 必须有提示词
        )
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        return self.is_ready_for_execution()
    
    def get_image_count(self) -> int:
        """获取图片数量"""
        return len(self.images)
    
    def get_image_purposes(self) -> List[str]:
        """获取图片用途列表"""
        return [img.purpose for img in self.images]
    
    def get_flux_image_types(self) -> List[FluxImageType]:
        """获取Flux图片类型列表"""
        return [img.flux_image_type for img in self.images]
    
    def get_control_images(self) -> List['SessionImage']:
        """获取控制图列表"""
        return [img for img in self.images if img.is_control_image()]
    
    def get_reference_images(self) -> List['SessionImage']:
        """获取参考图列表"""
        return [img for img in self.images if img.is_reference_image()]
    
    def get_mixed_images(self) -> List['SessionImage']:
        """获取混合用途图片列表"""
        return [img for img in self.images if img.is_mixed_image()]
    
    def has_unclassified_images(self) -> bool:
        """是否有未分类的图片"""
        return any(img.flux_image_type == FluxImageType.UNKNOWN for img in self.images)
    
    def set_image_flux_type(self, image_index: int, flux_type: FluxImageType) -> bool:
        """设置指定图片的Flux类型"""
        if 0 <= image_index < len(self.images):
            self.images[image_index].flux_image_type = flux_type
            self.update_activity()
            return True
        return False
    
    def needs_image_type_confirmation(self) -> bool:
        """是否需要图片类型确认（Flux工作流且有多张未分类图片）"""
        return (self.workflow_type == WorkflowType.AIGEN and 
                self.get_image_count() > 1 and 
                self.has_unclassified_images())
    
    def get_remaining_time(self) -> float:
        """获取剩余时间（秒）"""
        remaining = (self.timeout_minutes * 60) - (time.time() - self.updated_at)
        return max(0, remaining)
    
    def format_remaining_time(self) -> str:
        """格式化剩余时间"""
        remaining = self.get_remaining_time()
        if remaining <= 0:
            return "已超时"
        
        minutes = int(remaining // 60)
        seconds = int(remaining % 60)
        return f"{minutes}分{seconds}秒"
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取会话摘要信息"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'chat_id': self.chat_id,
            'workflow_type': self.workflow_type.value,
            'state': self.state.value,
            'prompt': self.prompt,
            'image_count': self.get_image_count(),
            'image_purposes': self.get_image_purposes(),
            'has_quoted_text': bool(self.quoted_text),
            'quoted_images_count': len(self.quoted_images),
            'remaining_time': self.format_remaining_time(),
            'is_ready': self.is_ready_for_execution(),
            'created_at': datetime.fromtimestamp(self.created_at).isoformat(),
            'updated_at': datetime.fromtimestamp(self.updated_at).isoformat()
        } 