"""
标准图片处理器
负责处理图片相关的辅助功能

功能边界：
- 将生成的图片发送到微信
- 创建图片消息
- 不负责工作流执行（由flux_workflow_manager负责）
- 不负责参数分析（由统一路由系统负责）
"""

from __future__ import annotations

import json
import typing
import asyncio
import aiohttp
import base64
import random
import os
import tempfile
import re
from typing import Dict, Any, Optional, List

from .. import entities as llm_entities
from ...core import app, entities as core_entities
from ...platform import types as platform_types
from ...platform.types import message as platform_message
from ...core.workflow.shared_enums import TransmissionMode
from pkg.core.workflow.manager_base import WorkflowResult


class StandardImageHandler:
    """标准图片处理器
    
    功能边界：
    - 将生成的图片发送到微信
    - 创建图片消息
    - 不负责工作流执行（由flux_workflow_manager负责）
    - 不负责参数分析（由统一路由系统负责）
    """
    
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.ap = ap
        self.pipeline_config = pipeline_config
        
        # 添加种子记录，用于"使用上一次种子"功能
        self.last_seed = None
        self.seed_history = []  # 记录最近的种子历史

    async def create_image_message(self, image_data: bytes) -> Optional[llm_entities.Message]:
        """创建图片消息（仅创建，不发送）"""
        try:
            # 将图片数据转换为 base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 创建图片内容元素
            image_content = llm_entities.ContentElement.from_image_base64(image_base64)
            
            # 创建消息
            message = llm_entities.Message(
                role='assistant',
                content=[image_content]
            )
            
            return message
            
        except Exception as e:
            self.ap.logger.error(f"创建图片消息出错: {str(e)}")
            return None

    async def send_image_to_wechat(self, image_data: bytes, query: core_entities.Query) -> bool:
        """发送图片到微信"""
        temp_path = None
        try:
            # 保存图片到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name
            
            self.ap.logger.info(f"图片已保存到临时文件: {temp_path}")
            
            # 创建微信图片消息
            image_message = platform_types.message.Image(path=temp_path)
            message_chain = platform_types.message.MessageChain([image_message])
            
            self.ap.logger.info(f"开始发送图片消息到微信...")
            
            # 发送消息
            await query.adapter.reply_message(
                message_source=query.message_event,
                message=message_chain,
                quote_origin=False
            )
            
            self.ap.logger.info("微信adapter.reply_message调用完成")
            
            # 删除临时文件
            if temp_path and os.path.exists(temp_path):
                os.unlink(temp_path)
                self.ap.logger.info(f"临时文件已删除: {temp_path}")
            
            self.ap.logger.info("图片已成功发送到微信")
            return True
            
        except Exception as e:
            self.ap.logger.error(f"发送图片到微信出错: {str(e)}")
            # 确保临时文件被删除
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    self.ap.logger.info(f"异常情况下删除临时文件: {temp_path}")
                except:
                    pass
            return False 