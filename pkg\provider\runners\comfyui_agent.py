"""
ComfyUI Agent请求运行器
重构版本 - 采用组合模式和统一路由系统
"""

from __future__ import annotations

import asyncio
import typing
from typing import List, Optional, Dict, Any, TYPE_CHECKING
import time

# 使用TYPE_CHECKING避免循环导入
if TYPE_CHECKING:
    from ...core import app

from .. import runner
from ...core import entities as core_entities
from .. import entities as llm_entities
from ...platform.types import message as platform_message
from ...provider.runners.admin_sync_handler import AdminSyncHandler
from ...provider.runners.standard_image_handler import StandardImageHandler
from ...provider.runners.kontext_image_handler import KontextImageHandler
from .unified_routing_mixin_v2 import UnifiedRoutingMixinV2
# 使用新架构的工作流管理器
# from ...workers.flux.flux_workflow_manager import FluxWorkflowManager
from ...workers.kontext.kontext_workflow_executor import KontextWorkflowExecutor
from ...core.session.manager import SessionManager
from ...core.session.models import WorkflowType, SessionState
from ...core.session.states import is_execution_command, is_cancel_command
from pkg.core.session.manager import SessionManager
from pkg.core.image.processor import ImageProcessor
from pkg.core.intent.analyzer import IntentAnalyzer

# 导入各个处理器
from .standard_image_handler import StandardImageHandler
from .kontext_image_handler import KontextImageHandler
from .admin_sync_handler import AdminSyncHandler
from .unified_routing_mixin_v2 import UnifiedRoutingMixinV2


@runner.runner_class('comfyui-agent')
class ComfyUIAgentRunner(runner.RequestRunner, UnifiedRoutingMixinV2):
    """重构后的ComfyUI Agent请求运行器
    
    采用组合模式，将功能分解为独立的处理器：
    - StandardImageHandler: 处理标准图片生成
    - KontextImageHandler: 处理Kontext工作流
    - AdminSyncHandler: 处理管理员同步
    - UnifiedRoutingMixinV2: 提供统一路由功能
    """

    def __init__(self, ap: 'app.Application', pipeline_config: dict):
        super().__init__(ap, pipeline_config)
        
        # 🔥 关键修复：手动调用 mixin 的 __init__ 方法
        UnifiedRoutingMixinV2.__init__(self)
        
        # 初始化处理器
        self._init_handlers()
        
        # 会话管理器 - 使用正确的参数
        from ...core.session.manager import SessionManager
        self.session_manager = SessionManager(cleanup_interval=300)
        
        # 缓存pipeline_config
        self.pipeline_config = pipeline_config
        
        # 缓存handlers
        self.workflow_manager = None
        self.standard_handler = None
        self.kontext_handler = None
        self.admin_sync_handler = None
        
        # 调试计数器
        self.debug_counter = 0
        
        # 🔥 新增：初始化状态标记
        self._handlers_initialized = False
        
        # 统一会话管理器
        if self.ap and self.ap.logger:
            self.session_manager.set_logger(self.ap.logger)
        
        # 统一路由系统
        from pkg.core.workflow.unified_routing_system import get_unified_router
        self.unified_router = get_unified_router(ap)

    async def run(self, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """处理查询请求"""
        try:
            # 调试计数器
            self.debug_counter += 1
            self.ap.logger.info(f"🔄 ComfyUIAgentRunner.run() 调用 #{self.debug_counter}")
            
            # 1. 基础数据提取
            user_text = self._extract_user_text(query)
            user_images = await self._extract_user_images(query)
            user_id = self._get_user_id(query)
            chat_id = self._get_chat_id(query)
            
            self.ap.logger.info(f"📝 用户消息: {user_text[:100]}...")
            self.ap.logger.info(f"🖼️ 用户图片: {len(user_images)} 张")
            self.ap.logger.info(f"👤 用户ID: {user_id}")
            self.ap.logger.info(f"💬 聊天ID: {chat_id}")
            
            # 2. 获取或创建会话
            session = self.session_manager.get_session(user_id, chat_id)
            
            # 3. 如果没有会话，使用路由系统创建会话
            if not session:
                self.ap.logger.info("🆕 没有活跃会话，检查是否为有效的工作流请求")
                
                # 🔥 关键修复：在调用路由系统之前，先验证双触发条件
                # 这确保只有@机器人+前缀的消息才会触发路由决策
                if not self._is_valid_image_generation_request(user_text, query, user_id, chat_id):
                    self.ap.logger.info(f"❌ 不是有效的工作流请求，跳过路由决策: {user_text[:50]}...")
                    return
                
                self.ap.logger.info("✅ 验证通过，启动管道路由")
                
                # 🔥 第一级：管道路由（仅基于触发词确定管道类型）
                pipeline_type = self._determine_pipeline_type(user_text)
                
                # 记录管道路由决策
                self.ap.logger.info(f"🎯 管道路由决策: {pipeline_type.value}")
                self.ap.logger.info(f"   基于触发词的管道选择，具体工作流将在用户发送'开始'指令时确定")
                
                # 创建新会话（仅基于管道类型，不包含具体工作流信息）
                session = self.session_manager.create_session(
                    user_id=user_id,
                    workflow_type=pipeline_type,
                    chat_id=chat_id,
                    prompt=user_text,
                    timeout_minutes=10
                )
                
                # 不保存路由结果，因为具体工作流选择将在"开始"指令时进行
                
                self.ap.logger.info(f"✅ 创建新会话: {session.session_id}")
                
                # 添加用户图片
                if user_images:
                    for img_data in user_images:
                        img_type = self._detect_image_type(img_data, user_text)
                        session.add_image(img_data, img_type)
                
                self.ap.logger.info(f"📋 会话创建完成: {session.workflow_type.value}")
                
                # 根据工作流类型提供不同的响应
                if session.workflow_type == WorkflowType.AIGEN:
                    yield llm_entities.Message(
                        role='assistant',
                        content=f"🎨 **Aigen工作流已启动**\n\n"
                               f"📝 **提示词**: {session.prompt}\n"
                               f"🖼️ **图片**: {len(session.images)} 张\n"
                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                               f"❌ 发送 '取消' 取消工作流"
                    )
                elif session.workflow_type == WorkflowType.KONTEXT:
                    yield llm_entities.Message(
                        role='assistant',
                        content=f"🎨 **Kontext工作流已启动**\n\n"
                               f"📝 **提示词**: {session.prompt}\n"
                               f"🖼️ **图片**: {len(session.images)} 张\n"
                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                               f"❌ 发送 '取消' 取消工作流"
                    )
                elif session.workflow_type == WorkflowType.KONTEXT_API:
                    yield llm_entities.Message(
                        role='assistant',
                        content=f"☁️ **远程API工作流已启动**\n\n"
                               f"📝 **提示词**: {session.prompt}\n"
                               f"🖼️ **图片**: {len(session.images)} 张\n"
                               f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                               f"❌ 发送 '取消' 取消工作流"
                    )
                return
            
            # 4. 处理有活跃会话的情况
            self.ap.logger.info(f"🔄 处理活跃会话: {session.session_id}")
            self.ap.logger.info(f"   工作流类型: {session.workflow_type.value}")
            self.ap.logger.info(f"   会话状态: {session.state.value}")
            
            # 初始化处理器
            self._initialize_handlers(query)
            
            # 检查是否是执行指令
            if is_execution_command(user_text):
                self.ap.logger.info("🚀 检测到执行指令")
                
                # 根据工作流类型执行不同的处理逻辑
                if session.workflow_type == WorkflowType.AIGEN:
                    async for message in self._execute_aigen_workflow(session, query):
                        yield message
                elif session.workflow_type == WorkflowType.KONTEXT:
                    async for message in self._execute_kontext_workflow(session, query):
                        yield message
                elif session.workflow_type == WorkflowType.KONTEXT_API:
                    async for message in self._execute_kontext_api_workflow(session, query):
                        yield message
                else:
                    yield llm_entities.Message(
                        role='assistant',
                        content=f"❌ 未知工作流类型: {session.workflow_type.value}"
                    )
                return
            
            # 检查是否是取消指令
            if is_cancel_command(user_text):
                self.ap.logger.info("❌ 检测到取消指令")
                self.session_manager.delete_session(user_id, chat_id)
                yield llm_entities.Message(
                    role='assistant',
                    content="❌ 工作流已取消"
                )
                return
            
            # 其他情况，更新会话内容
            session.set_prompt(user_text)
            if user_images:
                for img_data in user_images:
                    img_type = self._detect_image_type(img_data, user_text)
                    session.add_image(img_data, img_type)
            
            yield llm_entities.Message(
                role='assistant',
                content=f"✅ 已更新会话内容\n\n"
                       f"📝 **提示词**: {session.prompt}\n"
                       f"🖼️ **图片**: {len(session.images)} 张\n"
                       f"🚀 发送 '开始' 或 'go' 指令生成图片\n"
                       f"❌ 发送 '取消' 取消工作流"
            )
                
        except Exception as e:
            self.ap.logger.error(f"ComfyUIAgentRunner处理失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ 处理请求时出错: {str(e)}"
            )

    def _init_handlers(self):
        """初始化处理器（延迟加载）"""
        # 不在初始化时立即创建处理器，而是在需要时才创建
        pass

    def _initialize_handlers(self, query: core_entities.Query):
        """初始化处理器（需要query对象）"""
        if self._handlers_initialized:
            return
            
        try:
            try:
                comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
                self.workflow_path = comfyui_config.get('workflow-path', 'workflows')
                self.comfyui_enabled = comfyui_config.get('enabled', True)
                self.kontext_enabled = comfyui_config.get('kontext-enabled', True)
                self.ap.logger.info(f"ComfyUI Agent配置: enabled={self.comfyui_enabled}, kontext-enabled={self.kontext_enabled}")
            except Exception as e:
                self.ap.logger.error(f"ComfyUI配置初始化失败: {e}")
                self.workflow_path = 'workflows'
                self.comfyui_enabled = True
                self.kontext_enabled = True
            
            if self.standard_handler is None:
                self.standard_handler = StandardImageHandler(self.ap, self.pipeline_config)
                self.ap.logger.info("StandardImageHandler 初始化成功")
            
            if self.kontext_handler is None:
                self.kontext_handler = KontextImageHandler(self.ap, self.pipeline_config)
                self.ap.logger.info("KontextImageHandler 初始化成功")
            
            if self.admin_sync_handler is None:
                self.admin_sync_handler = AdminSyncHandler(self.ap, self.pipeline_config, query)
                self.ap.logger.info("AdminSyncHandler 初始化成功")
            
            if self.workflow_manager is None:
                try:
                    from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
                    self.ap.logger.info(f"检查ap对象属性:")
                    self.ap.logger.info(f"  - ap类型: {type(self.ap)}")
                    self.ap.logger.info(f"  - ap.logger: {self.ap.logger is not None}")
                    self.ap.logger.info(f"  - ap.model_mgr: {self.ap.model_mgr is not None}")
                    self.ap.logger.info(f"  - ap.pipeline_config: {hasattr(self.ap, 'pipeline_config')}")
                    self.workflow_manager = FluxWorkflowManager(self.ap, self.pipeline_config)
                    self.ap.logger.info("FluxWorkflowManager 初始化成功")
                    self.ap.logger.info(f"FluxWorkflowManager属性检查:")
                    self.ap.logger.info(f"  - parameter_analyzer: {self.workflow_manager.parameter_analyzer is not None}")
                    self.ap.logger.info(f"  - seed_manager: {self.workflow_manager.seed_manager is not None}")
                    self.ap.logger.info(f"  - node_mapper: {self.workflow_manager.node_mapper is not None}")
                    self.ap.logger.info(f"  - lora_integration: {self.workflow_manager.lora_integration is not None}")
                except Exception as e:
                    self.ap.logger.error(f"FluxWorkflowManager 初始化失败: {e}")
                    import traceback
                    self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
                    self.workflow_manager = None
            
            self._handlers_initialized = True
                    
        except Exception as e:
            self.ap.logger.error(f"处理器初始化失败: {e}")
            self.ap.logger.exception("处理器初始化异常详情：")

    def _extract_user_text(self, query: core_entities.Query) -> str:
        """提取用户文本"""
        user_text = ""
        if query.user_message and query.user_message.content:
            if isinstance(query.user_message.content, str):
                user_text = query.user_message.content
            elif isinstance(query.user_message.content, list):
                for content in query.user_message.content:
                    if hasattr(content, 'text') and content.text and hasattr(content, 'type') and content.type == 'text':
                        user_text += str(content.text)
        return user_text.strip()

    async def _extract_user_images(self, query: core_entities.Query) -> List[bytes]:
        """提取用户消息中的图片数据 - 使用统一的ImageProcessor"""
        try:
            # 使用统一的图片处理器
            from pkg.core.image.processor import image_processor
            images = await image_processor.extract_user_images(query)
            self.ap.logger.info(f"📷 统一图片处理器提取到 {len(images)} 张图片")
            return images
        except Exception as e:
            self.ap.logger.error(f"提取用户图片失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            return []

    def _get_user_id(self, query: core_entities.Query) -> str:
        """获取用户ID"""
        # 委托给kontext_handler处理
        return self.kontext_handler.get_user_id(query) if self.kontext_handler else "unknown_user"
    
    def _get_chat_id(self, query: core_entities.Query) -> str:
        """获取聊天ID"""
        # 委托给kontext_handler处理
        return self.kontext_handler.get_chat_id(query) if self.kontext_handler else "unknown_chat"

    def _is_traditional_image_request(self, user_text: str) -> bool:
        """检查是否是传统的图片生成请求（向后兼容）"""
        if not user_text:
            return False
            
        user_text_lower = user_text.lower().strip()
        
        # 检查传统的关键词
        traditional_keywords = ['生成', '画', '创建', '制作', '绘制', '图片', '图像']
        return any(keyword in user_text_lower for keyword in traditional_keywords)
    
    def _is_valid_image_generation_request(self, user_text: str, query: core_entities.Query, user_id: str, chat_id: str) -> bool:
        """验证是否是有效的图片生成请求
        
        检查用户文本是否包含图片生成相关的前缀或关键词
        这个方法复用pipeline配置中的前缀列表，确保一致性
        特别处理：如果用户有活跃的Kontext会话，则允许无前缀的交互
        """
        # 🔥 重要：如果用户有活跃的Kontext会话，允许任何交互（图片上传、"开始"等指令）
        try:
            self.ap.logger.info(f"检查Kontext会话 - user_id: '{user_id}', chat_id: '{chat_id}'")
            active_session = self.session_manager.get_session(user_id, chat_id)
            if active_session:
                self.ap.logger.info(f"用户有活跃的Kontext会话，允许交互: {user_text[:50] if user_text else '(图片上传)'}...")
                return True
            else:
                self.ap.logger.info(f"用户没有活跃的Kontext会话")
        except Exception as e:
            self.ap.logger.error(f"检查Kontext会话时出错: {e}")
        
        # 对于没有活跃Kontext会话的情况，检查前缀
        if not user_text:
            self.ap.logger.info("用户文本为空，返回False")
            return False
            
        # 🔥 重要：检查原始消息链，因为PrefixRule可能已经移除了前缀
        original_message_text = str(query.message_chain).strip()
        self.ap.logger.info(f"🔍 调试信息:")
        self.ap.logger.info(f"  - 原始消息文本: '{original_message_text}'")
        self.ap.logger.info(f"  - 处理后的用户文本: '{user_text}'")
        
        # 获取配置中的前缀列表
        try:
            pipeline_config = query.pipeline_config
            if not pipeline_config:
                self.ap.logger.warning("pipeline_config为空，返回False")
                return False
                
            trigger_config = pipeline_config.get('trigger', {})
            group_rules = trigger_config.get('group-respond-rules', {})
            prefixes = group_rules.get('prefix', [])
            
            self.ap.logger.info(f"  - 配置的前缀列表: {prefixes}")
            self.ap.logger.info(f"  - trigger_config: {trigger_config}")
            
            # 首先检查原始消息文本（PrefixRule处理前）
            original_text_lower = original_message_text.lower().strip()
            for prefix in prefixes:
                prefix_lower = prefix.lower()
                self.ap.logger.info(f"  - 检查原始消息前缀: '{prefix}' (lower: '{prefix_lower}')")
                
                # 检查精确匹配（带空格）
                if original_text_lower.startswith(prefix_lower + ' '):
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（原始消息，带空格）: '{prefix}', 原始消息: {original_message_text[:50]}...")
                    return True
                
                # 检查精确匹配（不带空格，但用户文本以前缀开头）
                if original_text_lower == prefix_lower:
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（原始消息，精确匹配）: '{prefix}', 原始消息: {original_message_text[:50]}...")
                    return True
                
                # 检查前缀匹配（用户文本以前缀开头，后面可能有其他字符）
                if original_text_lower.startswith(prefix_lower):
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（原始消息，前缀匹配）: '{prefix}', 原始消息: {original_message_text[:50]}...")
                    return True
            
            # 然后检查处理后的用户文本（PrefixRule处理后）
            user_text_lower = user_text.lower().strip()
            for prefix in prefixes:
                prefix_lower = prefix.lower()
                self.ap.logger.info(f"  - 检查处理后文本前缀: '{prefix}' (lower: '{prefix_lower}')")
                
                # 检查精确匹配（带空格）
                if user_text_lower.startswith(prefix_lower + ' '):
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（处理后文本，带空格）: '{prefix}', 处理后文本: {user_text[:50]}...")
                    return True
                
                # 检查精确匹配（不带空格，但用户文本以前缀开头）
                if user_text_lower == prefix_lower:
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（处理后文本，精确匹配）: '{prefix}', 处理后文本: {user_text[:50]}...")
                    return True
                
                # 检查前缀匹配（用户文本以前缀开头，后面可能有其他字符）
                if user_text_lower.startswith(prefix_lower):
                    self.ap.logger.info(f"✅ 检测到图片生成前缀（处理后文本，前缀匹配）: '{prefix}', 处理后文本: {user_text[:50]}...")
                    return True
            
            # 特殊检查：kontext前缀（即使不在配置中也应该通过）
            # 支持常见的拼写错误：kontext, kontex, context等
            kontext_variants = ['kontext ', 'kontex ', 'context ']
            for variant in kontext_variants:
                if original_text_lower.startswith(variant) or user_text_lower.startswith(variant):
                    self.ap.logger.info(f"✅ 检测到Kontext前缀变体: '{variant}', 原始消息: {original_message_text[:50]}...")
                    return True
            
            # 🔥 新增：特殊处理aigen命令
            # 如果原始消息包含aigen，即使PrefixRule已经移除，也应该通过
            if 'aigen' in original_text_lower:
                self.ap.logger.info(f"✅ 检测到aigen关键词在原始消息中: {original_message_text[:50]}...")
                return True
            
            # 🔥 新增：如果处理后的文本以"一只"等常见提示词开头，可能是aigen命令被处理后的结果
            common_prompts = ['一只', '一个', '生成', '创建', '制作', '画', '绘制']
            if any(user_text_lower.startswith(prompt) for prompt in common_prompts):
                self.ap.logger.info(f"✅ 检测到常见提示词开头，可能是aigen命令: {user_text[:50]}...")
                return True
                
            self.ap.logger.info(f"❌ 未检测到有效的图片生成前缀")
            self.ap.logger.info(f"  - 原始消息: {original_message_text[:50]}...")
            self.ap.logger.info(f"  - 处理后文本: {user_text[:50]}...")
            self.ap.logger.info(f"  - 配置的前缀: {prefixes}")
            return False
            
        except Exception as e:
            self.ap.logger.error(f"验证图片生成请求时出错: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            # 出错时保守处理，返回False
            return False

    # 🔥 新增：智能图片类型检测方法
    def _detect_image_type(self, image_data: bytes, user_prompt: str) -> str:
        """
        智能检测图片类型（控制图 vs 参考图）
        
        Args:
            image_data: 图片二进制数据
            user_prompt: 用户提示词
            
        Returns:
            图片类型: 'control' 或 'reference'
        """
        try:
            # 1. 基于用户提示词检测
            prompt_lower = user_prompt.lower()
            control_keywords = ['控制', '草图', '线稿', '姿势', 'pose', '深度', 'depth', '边缘', 'edge', '轮廓', 'outline']
            reference_keywords = ['参考', '风格', 'style', '类似', '像', '根据', '基于']
            
            # 检查控制图关键词
            if any(keyword in prompt_lower for keyword in control_keywords):
                return 'control'
            
            # 检查参考图关键词
            if any(keyword in prompt_lower for keyword in reference_keywords):
                return 'reference'
            
            # 2. 基于图片特征检测
            try:
                from PIL import Image
                import io
                
                with Image.open(io.BytesIO(image_data)) as img:
                    # 转换为灰度图分析
                    if img.mode != 'L':
                        gray_img = img.convert('L')
                    else:
                        gray_img = img
                    
                    # 计算图片特征
                    width, height = img.size
                    pixel_count = width * height
                    
                    # 分析图片复杂度（简单的草图通常颜色较少，边缘较多）
                    if img.mode in ['L', 'LA']:  # 灰度图
                        # 灰度图更可能是控制图
                        return 'control'
                    
                    # 检查图片大小和格式
                    if len(image_data) < 100 * 1024:  # 小于100KB
                        # 小图片更可能是控制图
                        return 'control'
                    
                    # 检查是否是PNG格式（通常用于线稿）
                    if img.format == 'PNG':
                        # 分析颜色数量
                        colors = img.getcolors(maxcolors=256)
                        if colors and len(colors) < 50:  # 颜色较少
                            return 'control'
                    
                    # 默认作为参考图
                    return 'reference'
                    
            except Exception as e:
                self.ap.logger.warning(f"图片特征分析失败: {e}")
                # 分析失败时，根据提示词长度判断
                if len(user_prompt.strip()) < 20:
                    return 'control'  # 短提示词更可能是控制图
                else:
                    return 'reference'  # 长提示词更可能是参考图
                    
        except Exception as e:
            self.ap.logger.error(f"图片类型检测失败: {e}")
            return 'reference'  # 出错时默认参考图

    def _determine_pipeline_type(self, user_text: str) -> WorkflowType:
        """
        第一级管道路由：基于触发词确定管道类型
        
        Args:
            user_text: 用户输入文本
            
        Returns:
            WorkflowType: 管道类型
        """
        if not user_text:
            return WorkflowType.AIGEN  # 默认
            
        user_text_lower = user_text.lower().strip()
        
        # 检查精确触发词匹配
        if user_text_lower.startswith("aigen "):
            return WorkflowType.AIGEN
        elif user_text_lower.startswith("kontext "):
            return WorkflowType.KONTEXT
        elif user_text_lower.startswith("kontext_api "):
            return WorkflowType.KONTEXT_API
        
        # 检查完整消息中是否包含触发词（处理PrefixRule移除前缀的情况）
        if "aigen" in user_text_lower:
            return WorkflowType.AIGEN
        elif "kontext_api" in user_text_lower:
            return WorkflowType.KONTEXT_API
        elif "kontext" in user_text_lower:
            return WorkflowType.KONTEXT
        
        # 默认返回AIGEN（不应该到达这里，因为前面已经验证过触发词）
        return WorkflowType.AIGEN

    # 🔥 新增：执行aigen工作流的方法
    async def _execute_aigen_workflow(self, session, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行aigen工作流"""
        try:
            # 获取相关数据
            user_text = session.prompt
            session_images = session.images
            
            self.ap.logger.info(f"🎯 开始LLM智能路由分析会话数据...")
            self.ap.logger.info(f"   文本: {user_text}")
            self.ap.logger.info(f"   图片数量: {len(session_images)}")
            
            # 🔥 关键修复：重新进行LLM智能路由，基于会话中的完整信息
            routing_result = await self.route_workflow_intelligently(
                user_text=user_text,
                query=query, 
                attached_images=[img.data for img in session_images] if session_images else None
            )
            
            # 🔥 记录路由决策结果
            if routing_result:
                self.ap.logger.info(f"📊 LLM智能路由结果:")
                self.ap.logger.info(f"   工作流子类型: {routing_result.workflow_subtype.value}")
                self.ap.logger.info(f"   置信度: {routing_result.confidence.value}")
                self.ap.logger.info(f"   推理: {routing_result.reasoning}")
                self.ap.logger.info(f"   建议提示词: {routing_result.suggested_prompt}")
                self.ap.logger.info(f"   工作流文件: {routing_result.workflow_file}")
                
                # 获取工作流说明
                workflow_info = routing_result.reasoning
                
                # 🔥 修复：用户go指令后的三段式消息（生成前）
                if routing_result.suggested_prompt and routing_result.suggested_prompt.strip():
                    optimized_prompt = routing_result.suggested_prompt
                else:
                    optimized_prompt = user_text

                # 创建生成前的三段式消息
                pre_generation_message = self._create_pre_generation_three_part_message(
                    optimized_prompt,
                    routing_result,
                    workflow_info
                )

                yield llm_entities.Message(
                    role='assistant',
                    content=pre_generation_message
                )
            else:
                self.ap.logger.warning("❌ LLM智能路由失败，使用回退方案")
                workflow_info = "LLM路由失败，使用默认文生图工作流"
                optimized_prompt = user_text

                # 创建回退方案的三段式消息
                pre_generation_message = self._create_fallback_three_part_message(
                    optimized_prompt,
                    workflow_info
                )

                yield llm_entities.Message(
                    role='assistant',
                    content=pre_generation_message
                )
            
            # 使用Flux工作流管理器执行
            if self.workflow_manager:
                # 准备图片数据
                image_data_list = [img.data for img in session_images] if session_images else []
                
                # 执行工作流
                result = await self.workflow_manager.execute_workflow(
                    prompt=user_text,
                    query=query,
                    session_images=session_images
                )
                
                if result.success and result.image_data:
                    # 确保处理器已经初始化
                    if self.standard_handler is None:
                        self._initialize_handlers(query)
                    
                    # 发送图片
                    send_success = await self.standard_handler.send_image_to_wechat(
                        result.image_data, query
                    )
                    if send_success:
                        image_message = await self.standard_handler.create_image_message(
                            result.image_data
                        )
                        if image_message:
                            yield image_message
                    else:
                        yield llm_entities.Message(
                            role='assistant',
                            content="图片生成成功，但发送到微信失败"
                        )
                    
                    # 发送简洁的完成消息（详细信息已在生成前显示）
                    metadata = result.metadata or {}
                    execution_time = metadata.get('execution_time', 0)
                    execution_time_rounded = round(execution_time) if execution_time else 0

                    # 构建简洁的完成消息
                    success_msg = self._create_simple_completion_message(execution_time_rounded, metadata)

                    yield llm_entities.Message(
                        role='assistant',
                        content=success_msg
                    )
                else:
                    yield llm_entities.Message(
                        role='assistant',
                        content=f"❌ **图片生成失败**: {result.error_message or '未知错误'}"
                    )
            else:
                yield llm_entities.Message(
                    role='assistant',
                    content="工作流管理器未初始化，无法执行工作流"
                )
                
        except Exception as e:
            self.ap.logger.error(f"执行aigen工作流失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            yield llm_entities.Message(
                role='assistant',
                content=f"执行工作流时出错: {str(e)}"
            )

    # 🔥 新增：执行图片转文本工作流的方法
    async def _execute_image_to_text_workflow(self, session, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行图片转文本工作流"""
        try:
            import base64
            import json
            import aiohttp
            from aiohttp import ClientTimeout
            
            # 检查是否有图片
            if not session.images:
                yield llm_entities.Message(
                    role='assistant',
                    content="❌ 没有图片数据，无法执行图片转文本"
                )
                return
            
            # 获取第一张图片
            img_data = session.images[0].data
            
            # 加载image_to_text_workflow.json
            workflow_file = "workflows/image_to_text_workflow.json"
            try:
                with open(workflow_file, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
            except Exception as e:
                self.ap.logger.error(f"加载工作流文件失败: {e}")
                yield llm_entities.Message(
                    role='assistant',
                    content=f"❌ 加载图片转文本工作流失败: {str(e)}"
                )
                return
            
            # 将图片转为base64
            img_base64 = base64.b64encode(img_data).decode('utf-8')
            
            # 更新工作流中的图片输入节点
            for node_id, node_data in workflow_data.items():
                if node_data.get("class_type") == "easy loadImageBase64":
                    node_data["inputs"]["base64_data"] = img_base64
                    break
            
            # 获取ComfyUI配置
            ai_config = query.pipeline_config.get("ai", {}) if query.pipeline_config else {}
            comfyui_config = ai_config.get("comfyui-agent", {}) if ai_config else {}
            api_url = comfyui_config.get("api_url", "http://localhost:8188")
            
            # 提交工作流到ComfyUI
            self.ap.logger.info("开始执行图片转文本工作流...")
            
            timeout = ClientTimeout(total=60)  # 60秒超时
            connector = aiohttp.TCPConnector(limit=10)
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as http_session:
                # 提交工作流
                submit_url = f"{api_url}/prompt"
                submit_data = {"prompt": workflow_data}
                
                async with http_session.post(submit_url, json=submit_data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        self.ap.logger.error(f"提交工作流失败: {response.status}, {error_text}")
                        yield llm_entities.Message(
                            role='assistant',
                            content=f"❌ 提交图片转文本工作流失败: HTTP {response.status}"
                        )
                        return
                    
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    if not prompt_id:
                        self.ap.logger.error("未获取到prompt_id")
                        yield llm_entities.Message(
                            role='assistant',
                            content="❌ 提交工作流失败：未获取到执行ID"
                        )
                        return
                
                # 等待执行完成
                self.ap.logger.info(f"工作流已提交，prompt_id: {prompt_id}")
                
                # 轮询执行状态
                max_attempts = 60  # 最多等待60次
                attempt = 0
                
                while attempt < max_attempts:
                    await asyncio.sleep(1)  # 等待1秒
                    attempt += 1
                    
                    # 检查执行状态
                    status_url = f"{api_url}/history/{prompt_id}"
                    async with http_session.get(status_url) as status_response:
                        if status_response.status == 200:
                            history_data = await status_response.json()
                            
                            if prompt_id in history_data:
                                # 执行完成，获取结果
                                execution_data = history_data[prompt_id]
                                outputs = execution_data.get("outputs", {})
                                
                                # 查找ShowText节点输出
                                text_result = None
                                for node_id, node_output in outputs.items():
                                    if "text" in node_output:
                                        text_result = node_output["text"]
                                        break
                                
                                if text_result:
                                    # 返回识别结果
                                    yield llm_entities.Message(
                                        role='assistant',
                                        content=f"📝 **图片内容识别结果**:\n\n{text_result}"
                                    )
                                    self.ap.logger.info("图片转文本工作流执行成功")
                                    return
                                else:
                                    self.ap.logger.warning("未找到ShowText节点输出")
                                    yield llm_entities.Message(
                                        role='assistant',
                                        content="❌ 未能识别图片内容，请尝试其他图片"
                                    )
                                    return
                            else:
                                # 还在执行中，继续等待
                                if attempt % 10 == 0:  # 每10秒记录一次日志
                                    self.ap.logger.info(f"工作流执行中... ({attempt}s)")
                        else:
                            self.ap.logger.warning(f"检查状态失败: {status_response.status}")
                
                # 超时
                self.ap.logger.error("图片转文本工作流执行超时")
                yield llm_entities.Message(
                    role='assistant',
                    content="❌ 图片转文本工作流执行超时，请稍后重试"
                )
                
        except Exception as e:
            self.ap.logger.error(f"执行图片转文本工作流失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ 执行图片转文本工作流时出错: {str(e)}"
            )



    def _create_pre_generation_three_part_message(self, optimized_prompt: str, routing_result, workflow_info: str) -> str:
        """
        创建生成前的三段式消息格式（用户go指令后立即返回）

        格式：
        第一段：工作流信息
        第二段：纯英文优化提示词（无前缀）
        第三段：开始生成状态

        Args:
            optimized_prompt: 优化后的英文提示词
            routing_result: 路由结果
            workflow_info: 工作流信息

        Returns:
            str: 三段式格式的消息
        """
        # 第一段：工作流信息
        part1 = "🎨 **Aigen工作流执行中**\n"

        if hasattr(routing_result, 'workflow_subtype') and routing_result.workflow_subtype:
            part1 += f"🔧 **工作流选择**: {routing_result.workflow_subtype.value}\n"
        else:
            part1 += f"🔧 **工作流选择**: 默认工作流\n"

        if hasattr(routing_result, 'confidence') and routing_result.confidence:
            part1 += f"📝 **置信度**: {routing_result.confidence.value}\n"
        else:
            part1 += f"📝 **置信度**: medium\n"

        part1 += f"💡 **选择原因**: {workflow_info}"

        # 第二段：纯英文优化提示词（无前缀）
        part2 = optimized_prompt.strip() if optimized_prompt else ""

        # 第三段：开始生成状态
        part3 = "🚀 开始生成图片，请稍等..."

        # 组合三段式消息
        if part2:
            return f"{part1}\n\n{part2}\n\n{part3}"
        else:
            return f"{part1}\n\n{part3}"

    def _create_fallback_three_part_message(self, optimized_prompt: str, workflow_info: str) -> str:
        """
        创建回退方案的三段式消息格式

        Args:
            optimized_prompt: 优化后的提示词
            workflow_info: 工作流信息

        Returns:
            str: 三段式格式的消息
        """
        # 第一段：工作流信息（回退方案）
        part1 = "🎨 **Aigen工作流执行中**\n"
        part1 += f"🔧 **工作流选择**: 默认文生图工作流\n"
        part1 += f"📝 **置信度**: low\n"
        part1 += f"💡 **选择原因**: {workflow_info}"

        # 第二段：纯英文优化提示词（无前缀）
        part2 = optimized_prompt.strip() if optimized_prompt else ""

        # 第三段：开始生成状态
        part3 = "🚀 开始生成图片，请稍等..."

        # 组合三段式消息
        if part2:
            return f"{part1}\n\n{part2}\n\n{part3}"
        else:
            return f"{part1}\n\n{part3}"



    def _create_pre_generation_three_part_message(self, optimized_prompt: str, routing_result, workflow_info: str) -> str:
        """
        创建生成前的三段式消息格式（用户go指令后立即返回）

        Args:
            optimized_prompt: 优化后的英文提示词
            routing_result: 路由结果
            workflow_info: 工作流信息

        Returns:
            str: 三段式格式的消息
        """
        # 第一段：开始生成状态
        part1 = "🚀 开始生成图片"

        # 第二段：纯英文优化提示词（无前缀）
        part2 = optimized_prompt.strip() if optimized_prompt else ""

        # 第三段：工作流和技术信息
        part3_elements = []

        if hasattr(routing_result, 'workflow_subtype') and routing_result.workflow_subtype:
            part3_elements.append(f"🔧 {routing_result.workflow_subtype.value}")

        if hasattr(routing_result, 'confidence') and routing_result.confidence:
            part3_elements.append(f"📊 {routing_result.confidence.value}")

        if workflow_info:
            part3_elements.append(f"💡 {workflow_info}")

        part3 = "，".join(part3_elements) if part3_elements else "🔧 标准工作流"

        # 组合三段式消息
        if part2:
            return f"{part1}\n\n{part2}\n\n{part3}"
        else:
            return f"{part1}\n\n{part3}"

    def _create_fallback_three_part_message(self, optimized_prompt: str, workflow_info: str) -> str:
        """
        创建回退方案的三段式消息格式

        Args:
            optimized_prompt: 优化后的提示词
            workflow_info: 工作流信息

        Returns:
            str: 三段式格式的消息
        """
        # 第一段：开始生成状态
        part1 = "🚀 开始生成图片"

        # 第二段：纯英文优化提示词（无前缀）
        part2 = optimized_prompt.strip() if optimized_prompt else ""

        # 第三段：回退信息
        part3 = f"⚠️ {workflow_info}"

        # 组合三段式消息
        if part2:
            return f"{part1}\n\n{part2}\n\n{part3}"
        else:
            return f"{part1}\n\n{part3}"

    def _create_simple_completion_message(self, execution_time: int, metadata: dict) -> str:
        """
        创建简洁的完成消息（生成完成后）

        Args:
            execution_time: 执行时间（秒）
            metadata: 元数据信息

        Returns:
            str: 简洁的完成消息
        """
        # 基础完成消息
        msg_parts = [f"✅ 生成完成"]

        # 添加执行时间
        if execution_time > 0:
            msg_parts.append(f"⏱️ {execution_time}s")

        # 添加种子信息（如果有）
        if metadata.get('seed'):
            msg_parts.append(f"🎲 {metadata.get('seed')}")

        return "，".join(msg_parts)