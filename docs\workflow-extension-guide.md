# LangBot 智能工作流扩展指南

## 📋 任务状态跟踪

✅ **完成状态**：[smart_workflow_system] REQ-01 完成  
📌 **验证点**：
- 功能边界是否明确: ✅ 已定义多输入模式、智能意图识别、会话管理
- 输出指令格式是否定义清晰: ✅ 已定义工作流配置结构和API接口规范
- 数据记录需求是否完整: ✅ 已包含意图分析、图片类型识别、参数推荐

## 概述

LangBot 智能工作流系统支持多种输入模式，能够根据用户意图自动选择最适合的工作流，支持纯文本生成、ControlNet控制、Redux参考图片等多种模式。

## 🎯 支持的输入模式

### 1. 纯文本生成 (text_only)
- **适用场景**: 纯文本提示词生成图片
- **关键词**: 普通描述性文本
- **图片要求**: 无需图片
- **示例**: "一只可爱的小猫在花园里玩耍"

### 2. 图像控制生成 (image_control)
- **适用场景**: 需要上传图片进行控制或参考
- **图片类型**: ControlNet、Redux、普通参考
- **图片要求**: 1-3张图片
- **示例**: "controlnet 草图" + 上传线稿图片

### 3. 混合控制生成 (hybrid)
- **适用场景**: 同时使用多种控制方式
- **图片类型**: ControlNet + Redux + 参考图片
- **图片要求**: 1-3张不同类型的图片
- **示例**: "混合控制" + 上传多种类型图片

## 🔧 工作流类型详解

### 文生图工作流（纯文本输入）

#### 默认工作流 (DEFAULT)
- **适用场景**: 通用图像生成
- **关键词**: `默认`, `通用`, `普通`
- **默认参数**: 1024x1024, 20步, 引导3.5
- **特点**: 平衡的质量和速度

#### 人像工作流 (PORTRAIT)
- **适用场景**: 人物肖像、头像生成
- **关键词**: `人像`, `头像`, `肖像`, `人物`, `脸部`, `selfie`
- **默认参数**: 800x1280, 25步, 引导4.0
- **特点**: 优化面部细节，竖版比例

#### 风景工作流 (LANDSCAPE)
- **适用场景**: 自然风景、景观图片
- **关键词**: `风景`, `景观`, `自然`, `山`, `海`, `天空`, `森林`, `湖`
- **默认参数**: 1024x768, 20步, 引导3.0
- **特点**: 横版比例，自然色彩

#### 动漫工作流 (ANIME)
- **适用场景**: 动漫风格图片生成
- **关键词**: `动漫`, `二次元`, `卡通`, `anime`, `manga`, `可爱`
- **默认参数**: 800x1280, 28步, 引导4.5
- **特点**: 动漫风格优化

### 图像控制工作流（需要上传图片）

#### Kontext工作流 (KONTEXT) - 本地版本
- **适用场景**: 图像编辑和修改（使用开源模型）
- **关键词**: `kontext`, `编辑`, `修改`, `改变`
- **默认参数**: 1024x1024, 60步, 引导4.0
- **图片要求**: 1-3张参考图片
- **特点**: 本地运行，节省API费用

#### ControlNet工作流 (CONTROLNET)
- **适用场景**: 精确控制生成（草图、线稿、姿势等）
- **关键词**: `controlnet`, `控制`, `草图`, `线稿`, `pose`, `depth`, `canny`
- **默认参数**: 1024x1024, 25步, 引导4.0
- **图片要求**: 1张控制图片
- **特点**: 精确的结构控制

#### Redux参考图片工作流 (REDUX_REFERENCE)
- **适用场景**: 风格迁移和参考
- **关键词**: `redux`, `参考`, `风格`, `style`, `reference`, `风格迁移`
- **默认参数**: 1024x1024, 30步, 引导3.8
- **图片要求**: 1张参考图片
- **特点**: 风格保持和迁移

#### 混合控制工作流 (HYBRID)
- **适用场景**: 多种控制方式组合
- **关键词**: `混合`, `hybrid`, `组合`, `多控制`, `complex`
- **默认参数**: 1024x1024, 30步, 引导4.0
- **图片要求**: 1-3张不同类型的图片
- **特点**: 灵活的多重控制

## 🚀 使用流程

### 1. 纯文本生成流程
```
用户输入文本 → 意图分析 → 选择工作流 → 直接生成 → 返回图片
```

**示例**:
```
用户: "一只可爱的小猫在花园里玩耍"
系统: 🎨 开始生成图片...
     📝 提示词: 一只可爱的小猫在花园里玩耍
     🔧 工作流: 默认工作流
```

### 2. 图像控制生成流程
```
用户输入文本+图片 → 意图分析 → 创建会话 → 等待"开始"指令 → 生成图片
```

**示例**:
```
用户: "controlnet 草图" + 上传线稿图片
系统: 🎨 **CONTROLNET 工作流已准备**
     📝 **提示词**: controlnet 草图
     🖼️ **图片数量**: 1/1
     💡 **图片用途**: ControlNet控制图片
     ⚙️ **建议参数**: steps=25, guidance=4.0
     
     ✅ **准备就绪**，发送 '开始' 指令生成图片！

用户: "开始"
系统: 🎨 开始执行 ControlNet控制工作流...
     📝 提示词: controlnet 草图
     🖼️ 图片数量: 1 张
```

### 3. 混合控制生成流程
```
用户输入文本+多种图片 → 意图分析 → 创建混合会话 → 等待"开始"指令 → 生成图片
```

**示例**:
```
用户: "混合控制" + 上传ControlNet图片 + Redux参考图片
系统: 🔄 **混合控制工作流已准备**
     
     📝 提示词: 混合控制
     🖼️ 图片数量: 2 张
     💡 用途: 多种控制图片组合
     
     ✅ **准备就绪**，发送 '开始' 指令生成图片！
```

## 💡 使用技巧

### 1. 图片类型识别
系统会自动识别上传图片的用途：
- **ControlNet图片**: 草图、线稿、姿势图、深度图等
- **Redux参考图片**: 风格参考、颜色参考等
- **普通参考图片**: 构图参考、内容参考等

### 2. 参数优化
系统会根据内容类型自动优化参数：
- **人像**: 更高的guidance值，优化面部细节
- **风景**: 适中的guidance值，保持自然感
- **动漫**: 较高的guidance值，增强风格特征
- **ControlNet**: 适中的steps值，平衡控制精度和生成质量

### 3. 会话管理
- 每个用户有独立的工作流会话
- 会话包含提示词、图片、参数等信息
- 发送"开始"指令执行会话
- 执行完成后自动清理会话

## 🔧 技术架构

### 核心组件

1. **IntentAnalyzer** - 意图分析器
   - 分析用户输入文本
   - 识别内容类型和输入模式
   - 推荐合适的工作流和参数

2. **ComfyUIWorkflowManager** - 工作流管理器
   - 管理工作流配置
   - 支持多种输入模式
   - 参数映射和优化

3. **SmartWorkflowHandler** - 智能工作流处理器
   - 统一处理各种工作流请求
   - 会话管理和状态跟踪
   - 图片上传和处理

### 工作流程

```
用户输入 → 意图分析 → 工作流选择 → 会话创建 → 等待开始指令 → 执行工作流 → 返回结果
    ↓           ↓            ↓            ↓            ↓            ↓           ↓
文本+图片 → 模式识别 → 参数优化 → 状态管理 → 用户确认 → ComfyUI执行 → 图片发送
```

## 📁 工作流文件结构

```
workflows/
├── default_workflow.json          # 默认文生图工作流
├── kontext_local_workflow.json    # 本地Kontext工作流
├── controlnet_workflow.json       # ControlNet工作流
├── redux_reference_workflow.json  # Redux参考工作流
├── hybrid_workflow.json           # 混合控制工作流
├── portrait_workflow.json         # 人像工作流
├── landscape_workflow.json        # 风景工作流
└── anime_workflow.json            # 动漫工作流
```

## 🎯 未来扩展计划

### 1. Kontext模型选择
- **本地开源模型**: 节省API费用，适合日常使用
- **Max节点模型**: 更高费用，但跟随提示词效果更好
- **智能选择**: 根据用户需求和预算自动选择

### 2. 更多ControlNet类型
- **Canny边缘检测**: 线稿控制
- **OpenPose**: 姿势控制
- **Depth**: 深度控制
- **Segmentation**: 分割控制

### 3. 高级功能
- **批量生成**: 一次生成多张图片
- **参数微调**: 实时调整生成参数
- **风格预设**: 预定义风格模板
- **历史记录**: 保存和重用生成配置

## 🔍 故障排除

### 常见问题

1. **图片上传失败**
   - 检查图片格式（支持PNG、JPG）
   - 检查图片大小（建议小于10MB）
   - 检查网络连接

2. **工作流执行失败**
   - 检查ComfyUI服务状态
   - 检查模型文件是否存在
   - 查看日志获取详细错误信息

3. **意图识别不准确**
   - 使用更明确的关键词
   - 参考关键词列表
   - 手动指定工作流类型

### 调试模式

启用详细日志记录：
```python
# 在配置中设置
logging_level: DEBUG
```

## 📞 技术支持

如有问题，请：
1. 查看日志文件获取详细错误信息
2. 检查工作流配置文件是否正确
3. 确认ComfyUI服务正常运行
4. 联系技术支持团队 