"""
统一工作流路由器
整合所有工作流类型的路由选择逻辑，支持智能决策和多策略路由
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image
import io

from .models import (
    WorkflowRoute, WorkflowEnvironment, RoutingDecision, RouteContext, 
    RouteResult, ImageRole, RouteStrategy
)
from ..session.models import WorkflowType
from ..intent.models import ContentType, InputMode, QualityLevel
from ..intent.analyzer import IntentAnalyzer
from ..image.analyzer import ImageAnalyzer


class WorkflowRouter:
    """统一工作流路由器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.intent_analyzer = IntentAnalyzer(logger)
        self.image_analyzer = ImageAnalyzer(logger)
        
        # 初始化路由配置
        self.routes: Dict[str, WorkflowRoute] = {}
        self._init_default_routes()
        
        # 图片角色识别关键词
        self.image_role_keywords = {
            ImageRole.SKETCH: ["草图", "线稿", "素描", "手绘", "sketch", "line", "drawing"],
            ImageRole.CONTROL: ["控制", "引导", "control", "guide", "direct"],
            ImageRole.REFERENCE: ["参考", "风格", "参考图", "reference", "style", "inspiration"],
            ImageRole.MASK: ["蒙版", "遮罩", "mask", "inpaint", "修复"],
            ImageRole.DEPTH: ["深度", "depth", "3d", "立体"],
            ImageRole.POSE: ["姿态", "pose", "动作", "姿势"],
            ImageRole.CANNY: ["边缘", "轮廓", "canny", "edge"],
            ImageRole.OPENPOSE: ["openpose", "人体", "骨架"]
        }
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[WorkflowRouter] {message}")
            elif level == "warning":
                self.logger.warning(f"[WorkflowRouter] {message}")
            else:
                self.logger.info(f"[WorkflowRouter] {message}")
    
    def _init_default_routes(self):
        """初始化默认路由配置"""
        # AIGEN 工作流路由
        self.routes["aigen"] = WorkflowRoute(
            workflow_type=WorkflowType.AIGEN,
            environment=WorkflowEnvironment.LOCAL,
            min_images=0,
            max_images=1,
            required_prompt=True,
            supports_text_only=True,
            priority=100,
            prefixes=["aigen", "ai生成", "文生图", "画图", "绘图"],
            keywords=["生成图片", "ai画图", "文本生图"],
            content_types=[
                ContentType.PORTRAIT, ContentType.LANDSCAPE, ContentType.ANIME,
                ContentType.REALISTIC, ContentType.PRODUCT, ContentType.ARCHITECTURAL,
                ContentType.CONCEPT_ART, ContentType.ILLUSTRATION, ContentType.PHOTOGRAPHY,
                ContentType.ABSTRACT, ContentType.DEFAULT
            ],
            default_params={
                "steps": 50,
                "guidance": 7.5,
                "strength": 0.8,
                "sampler": "dpmpp_2m_sde",
                "scheduler": "karras"
            }
        )
        
        # Local Kontext 工作流路由
        self.routes["kontext_local"] = WorkflowRoute(
            workflow_type=WorkflowType.KONTEXT,
            environment=WorkflowEnvironment.LOCAL,
            min_images=1,
            max_images=3,
            required_prompt=True,
            supports_text_only=False,
            priority=90,
            prefixes=["kontext", "图生图", "图像编辑"],
            keywords=["变换", "修改", "转换", "风格化", "编辑"],
            content_types=[ContentType.KONTEXT, ContentType.IMAGE_TO_IMAGE],
            image_roles=[ImageRole.REFERENCE, ImageRole.CONTROL, ImageRole.SKETCH],
            default_params={
                "steps": 60,
                "guidance": 4.0,
                "denoise": 0.85,
                "aspect_ratio": "auto"
            }
        )
        
        # Remote Kontext API 工作流路由
        self.routes["kontext_api"] = WorkflowRoute(
            workflow_type=WorkflowType.KONTEXT_API,
            environment=WorkflowEnvironment.API,
            min_images=1,
            max_images=3,
            required_prompt=True,
            supports_text_only=False,
            priority=85,
            prefixes=["kontext api", "远程", "云端", "api"],
            keywords=["远程生成", "api调用", "云端处理"],
            content_types=[ContentType.KONTEXT, ContentType.IMAGE_TO_IMAGE],
            image_roles=[ImageRole.REFERENCE, ImageRole.CONTROL, ImageRole.SKETCH],
            default_params={
                "steps": 60,
                "guidance": 4.0,
                "denoise": 0.8
            }
        )
    
    async def route(self, user_text: str, images: Optional[List[bytes]] = None, 
                   user_id: str = "", chat_id: str = "", 
                   metadata: Optional[Dict[str, Any]] = None) -> RouteResult:
        """
        执行工作流路由决策
        
        Args:
            user_text: 用户输入文本
            images: 图片数据列表
            user_id: 用户ID
            chat_id: 聊天ID
            metadata: 额外元数据
            
        Returns:
            路由结果
        """
        start_time = time.time()
        
        try:
            # 准备路由上下文
            context = await self._prepare_context(
                user_text, images or [], user_id, chat_id, metadata or {}
            )
            
            # 执行路由决策
            decision = await self._make_routing_decision(context)
            
            processing_time = time.time() - start_time
            self._log(f"路由决策完成: {decision.workflow_type.value} ({decision.confidence:.1%}) - {processing_time:.3f}s")
            
            return RouteResult(
                success=True,
                decision=decision,
                processing_time=processing_time,
                analysis_results={
                    'intent_analysis': context.intent_analysis.get_workflow_params() if context.intent_analysis else {},
                    'image_roles': [role.value for role in context.image_roles],
                    'strategy': decision.strategy.value
                }
            )
            
        except Exception as e:
            error_msg = f"路由决策失败: {e}"
            self._log(error_msg, "error")
            
            return RouteResult(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time
            )
    
    async def _prepare_context(self, user_text: str, images: List[bytes], 
                              user_id: str, chat_id: str, 
                              metadata: Dict[str, Any]) -> RouteContext:
        """准备路由上下文"""
        # 分析用户意图
        intent_analysis = None
        try:
            intent_analysis = self.intent_analyzer.analyze_intent(user_text)
        except Exception as e:
            self._log(f"意图分析失败: {e}", "warning")
        
        # 分析图片角色
        image_roles = []
        if images:
            image_roles = await self._analyze_image_roles(images, user_text)
        
        return RouteContext(
            user_text=user_text,
            image_count=len(images),
            image_data=images,
            image_roles=image_roles,
            intent_analysis=intent_analysis,
            user_id=user_id,
            chat_id=chat_id,
            metadata=metadata
        )
    
    async def _analyze_image_roles(self, images: List[bytes], user_text: str) -> List[ImageRole]:
        """分析图片角色"""
        roles = []
        
        for image_data in images:
            try:
                # 基于文本关键词推断角色
                role = self._infer_role_from_text(user_text)
                
                # 基于图片特征分析角色
                if role == ImageRole.UNKNOWN:
                    role = await self._infer_role_from_image(image_data)
                
                roles.append(role)
                
            except Exception as e:
                self._log(f"图片角色分析失败: {e}", "warning")
                roles.append(ImageRole.UNKNOWN)
        
        return roles
    
    def _infer_role_from_text(self, text: str) -> ImageRole:
        """从文本推断图片角色"""
        text_lower = text.lower()
        
        for role, keywords in self.image_role_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return role
        
        return ImageRole.UNKNOWN
    
    async def _infer_role_from_image(self, image_data: bytes) -> ImageRole:
        """从图片特征推断角色"""
        try:
            # 使用图片分析器分析图片特征
            image_info = self.image_analyzer.analyze_image(image_data)
            
            # 简单的特征推断逻辑
            # 这里可以扩展更复杂的图片分析算法
            
            # 检查是否是草图（通常边缘较多，颜色较少）
            if image_info.format.lower() in ['png', 'gif'] and image_info.size_mb < 1.0:
                return ImageRole.SKETCH
            
            # 默认作为参考图
            return ImageRole.REFERENCE
            
        except Exception as e:
            self._log(f"图片特征分析失败: {e}", "warning")
            return ImageRole.UNKNOWN
    
    async def _make_routing_decision(self, context: RouteContext) -> RoutingDecision:
        """执行路由决策"""
        # 1. 前缀优先策略
        decision = self._try_prefix_routing(context)
        if decision:
            return decision
        
        # 2. 混合分析策略
        decision = self._try_hybrid_analysis(context)
        if decision:
            return decision
        
        # 3. 默认策略
        return self._default_routing(context)
    
    def _try_prefix_routing(self, context: RouteContext) -> Optional[RoutingDecision]:
        """尝试前缀路由"""
        for route_id, route in self.routes.items():
            if route.matches_prefix(context.user_text):
                score = route.calculate_score(context)
                if score > 80.0:  # 高置信度前缀匹配
                    confidence = min(0.95, score / 100.0)  # 限制最大置信度为95%
                    return self._create_decision(
                        route, confidence, RouteStrategy.PREFIX_BASED,
                        f"匹配前缀指令: {route.prefixes[0]}", context
                    )
        return None
    
    def _try_hybrid_analysis(self, context: RouteContext) -> Optional[RoutingDecision]:
        """尝试混合分析策略"""
        best_route = None
        best_score = 0.0
        
        for route_id, route in self.routes.items():
            score = route.calculate_score(context)
            if score > best_score:
                best_score = score
                best_route = route
        
        if best_route and best_score > 30.0:  # 中等置信度阈值
            strategy = self._determine_strategy(context)
            reasoning = self._generate_reasoning(best_route, context, best_score)
            confidence = min(0.90, best_score / 100.0)  # 限制最大置信度为90%
            
            return self._create_decision(
                best_route, confidence, strategy, reasoning, context
            )
        
        return None
    
    def _default_routing(self, context: RouteContext) -> RoutingDecision:
        """默认路由策略"""
        # 根据图片数量选择默认工作流
        if context.image_count == 0:
            # 纯文本，使用AIGEN
            route = self.routes["aigen"]
            reasoning = "纯文本输入，选择AIGEN文生图工作流"
        elif context.image_count <= 3:
            # 有图片，优先使用本地Kontext
            route = self.routes["kontext_local"]
            reasoning = f"检测到{context.image_count}张图片，选择本地Kontext工作流"
        else:
            # 图片过多，使用API
            route = self.routes["kontext_api"]
            reasoning = f"图片数量({context.image_count})较多，选择API工作流"
        
        return self._create_decision(
            route, 0.6, RouteStrategy.DEFAULT, reasoning, context
        )
    
    def _determine_strategy(self, context: RouteContext) -> RouteStrategy:
        """确定使用的策略"""
        if context.intent_analysis and context.image_count > 0:
            return RouteStrategy.HYBRID_ANALYSIS
        elif context.intent_analysis:
            return RouteStrategy.INTENT_BASED
        elif context.image_count > 0:
            return RouteStrategy.IMAGE_BASED
        else:
            return RouteStrategy.DEFAULT
    
    def _generate_reasoning(self, route: WorkflowRoute, context: RouteContext, score: float) -> str:
        """生成决策推理"""
        reasons = []
        
        if route.matches_prefix(context.user_text):
            reasons.append("匹配前缀指令")
        
        if route.matches_keywords(context.user_text):
            reasons.append("包含关键词")
        
        if context.intent_analysis and route.matches_content_type(context.intent_analysis.content_type):
            reasons.append(f"内容类型匹配({context.intent_analysis.content_type.value})")
        
        if route.matches_image_count(context.image_count):
            reasons.append(f"图片数量适配({context.image_count}张)")
        
        if context.image_roles:
            matching_roles = set(context.image_roles) & set(route.image_roles)
            if matching_roles:
                role_names = [role.value for role in matching_roles]
                reasons.append(f"图片角色匹配({','.join(role_names)})")
        
        reasoning = f"评分{score:.1f}: " + ", ".join(reasons)
        return reasoning if reasons else f"默认选择(评分{score:.1f})"
    
    def _create_decision(self, route: WorkflowRoute, confidence: float, 
                        strategy: RouteStrategy, reasoning: str, 
                        context: RouteContext) -> RoutingDecision:
        """创建路由决策结果"""
        # 分配图片角色
        image_assignments = {}
        for i, role in enumerate(context.image_roles):
            image_assignments[f"image_{i+1}"] = role
        
        # 建议参数
        suggested_params = route.default_params.copy()
        
        # 建议提示词
        suggested_prompt = context.user_text
        if context.intent_analysis and hasattr(context.intent_analysis, 'suggested_params'):
            suggested_params.update(context.intent_analysis.suggested_params)
        
        return RoutingDecision(
            workflow_type=route.workflow_type,
            environment=route.environment,
            route=route,
            confidence=confidence,
            reasoning=reasoning,
            strategy=strategy,
            image_assignments=image_assignments,
            suggested_params=suggested_params,
            suggested_prompt=suggested_prompt
        )
    
    def add_route(self, route_id: str, route: WorkflowRoute):
        """添加自定义路由"""
        self.routes[route_id] = route
        self._log(f"添加路由配置: {route_id}")
    
    def remove_route(self, route_id: str):
        """删除路由"""
        if route_id in self.routes:
            del self.routes[route_id]
            self._log(f"删除路由配置: {route_id}")
    
    def get_routes(self) -> Dict[str, WorkflowRoute]:
        """获取所有路由配置"""
        return self.routes.copy()
    
    def set_route_enabled(self, route_id: str, enabled: bool):
        """设置路由启用状态"""
        if route_id in self.routes:
            self.routes[route_id].enabled = enabled
            self._log(f"设置路由{route_id}状态: {'启用' if enabled else '禁用'}")


# 全局实例
workflow_router = WorkflowRouter() 