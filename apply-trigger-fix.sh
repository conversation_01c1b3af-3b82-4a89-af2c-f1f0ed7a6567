#!/bin/bash

# 双触发条件修复应用脚本
# 用于在更新langbot后重新应用修复

set -e

echo "🔧 开始应用双触发条件修复..."

# 检查补丁文件是否存在
if [ ! -f "fix-trigger-conditions.patch" ]; then
    echo "❌ 补丁文件 fix-trigger-conditions.patch 不存在"
    exit 1
fi

# 备份当前文件
echo "📋 备份当前文件..."
backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

# 备份要修改的文件
if [ -f "pkg/pipeline/resprule/rules/atbot_with_prefix.py" ]; then
    cp "pkg/pipeline/resprule/rules/atbot_with_prefix.py" "$backup_dir/"
fi
if [ -f "pkg/provider/runners/comfyui_agent.py" ]; then
    cp "pkg/provider/runners/comfyui_agent.py" "$backup_dir/"
fi
if [ -f "pkg/core/session/models.py" ]; then
    cp "pkg/core/session/models.py" "$backup_dir/"
fi

echo "✅ 文件已备份到: $backup_dir"

# 应用补丁
echo "🔧 应用修复补丁..."
if git apply --check fix-trigger-conditions.patch 2>/dev/null; then
    git apply fix-trigger-conditions.patch
    echo "✅ 补丁应用成功！"
    echo ""
    echo "🎯 修复内容："
    echo "  - 修复了 @机器人 + 前缀 的双触发条件"
    echo "  - 简化了 ComfyUI Agent 的处理逻辑"
    echo "  - 增强了会话管理功能"
    echo ""
    echo "🧪 请测试以下场景："
    echo "  ❌ 普通消息不应触发: '今天天气不错'"
    echo "  ❌ 只@机器人不应触发: '@机器人 你好'"
    echo "  ❌ 只前缀不应触发: 'aigen 生成图片'"
    echo "  ✅ 双条件应该触发: '@机器人 aigen 生成图片'"
else
    echo "❌ 补丁无法应用，可能存在冲突"
    echo "💡 请手动检查修改或联系技术支持"
    exit 1
fi 