{"61": {"inputs": {"base64_data": "", "image_output": "Preview", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "Load Image (Base64)"}}, "62": {"inputs": {"caption_type": "Descriptive", "caption_length": "any", "max_new_tokens": 512, "top_p": 0.9, "top_k": 0, "temperature": 0.6, "user_prompt": "", "image": ["61", 0], "joycaption_beta1_model": ["63", 0]}, "class_type": "LayerUtility: JoyCaptionBeta1", "_meta": {"title": "LayerUtility: JoyCaption Beta One (Advance)"}}, "63": {"inputs": {"model": "fancyfeast/llama-joycaption-beta-one-hf-llava", "quantization_mode": "nf4", "device": "cuda"}, "class_type": "LayerUtility: LoadJoyCaptionBeta1Model", "_meta": {"title": "LayerUtility: Load JoyCaption Beta One Model (Advance)"}}, "64": {"inputs": {"text_undefined": "This is a photograph of a modern architectural facade, showcasing a repetitive geometric pattern. The building's exterior features a series of vertical and horizontal lines creating a grid-like structure. Each segment of the facade has a protruding, rectangular, cuboid window that extends outward, forming a three-dimensional effect. These windows are framed by beige, textured concrete, which contrasts with the transparent glass panes. The windows are symmetrically arranged in rows and columns, giving the image a rhythmic, almost musical quality. The glass panes reflect light and surrounding structures, adding a dynamic element to the otherwise rigid pattern. The overall color palette is muted, with beige, brown, and green tones dominating the image. The photograph is taken during daylight, with natural light highlighting the textures and geometric shapes. The image focuses solely on the architectural elements, with no visible people or additional objects. The style of the photograph is modernist, emphasizing clean lines, geometric forms, and minimalism. The textures of the concrete and glass add depth and a sense of realism to the image.", "text": ["62", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "65": {"inputs": {"anything": ["62", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}}