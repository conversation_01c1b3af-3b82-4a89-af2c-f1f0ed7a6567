"""
统一消息处理器
整合所有消息处理、路由决策、会话管理功能
"""

import time
from typing import List, Optional, Tuple, Dict, Any
from .models import (
    MessageContent, ProcessContext, ProcessResult, ProcessAction, MessageOptions
)
from .sender import MessageSender
from ..session.manager import SessionManager
from ..session.models import SessionState
from ..image.processor import ImageProcessor
from ..workflow.unified_routing_system import get_unified_router
from ..intent.analyzer import IntentAnalyzer


class MessageProcessor:
    """统一消息处理器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.message_sender = MessageSender(logger)
        self.session_manager = SessionManager()
        self.image_processor = ImageProcessor(logger)
        self.unified_router = get_unified_router()
        self.intent_analyzer = IntentAnalyzer(logger)
        
        # 设置日志器
        if logger:
            self.session_manager.set_logger(logger)
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[MessageProcessor] {message}")
            elif level == "warning":
                self.logger.warning(f"[MessageProcessor] {message}")
            else:
                self.logger.info(f"[MessageProcessor] {message}")
    
    async def process_message(self, raw_message: Any, user_id: str, 
                            chat_id: str = "") -> ProcessResult:
        """
        处理用户消息的统一入口
        
        Args:
            raw_message: 原始查询对象
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        try:
            # 1. 准备处理上下文
            context = await self._prepare_context(raw_message, user_id, chat_id)
            
            # 2. 检查是否有活跃会话
            session = self.session_manager.get_session(user_id, chat_id)
            
            if session:
                # 有活跃会话，处理会话内交互
                result = await self._handle_session_interaction(context, session)
            else:
                # 无活跃会话，检查是否启动新工作流
                result = await self._handle_new_request(context)
            
            # 更新处理时间
            processing_time = time.time() - start_time
            result.metadata['processing_time'] = processing_time
            
            self._log(f"消息处理完成: {result.action.value} ({processing_time:.3f}s)")
            return result
            
        except Exception as e:
            error_msg = f"消息处理失败: {e}"
            self._log(error_msg, "error")
            
            return ProcessResult(
                success=False,
                action=ProcessAction.ERROR,
                message=error_msg,
                error_code="PROCESSING_ERROR",
                error_details=str(e),
                metadata={'processing_time': time.time() - start_time}
            )
    
    async def _prepare_context(self, raw_message: Any, user_id: str, 
                             chat_id: str) -> ProcessContext:
        """准备处理上下文"""
        # 提取用户输入
        user_text = self._extract_user_text(raw_message)
        user_images = await self.image_processor.extract_user_images(raw_message)
        
        # 创建消息内容
        user_input = MessageContent(
            text=user_text,
            images=user_images
        )
        
        # 检查会话状态
        session = self.session_manager.get_session(user_id, chat_id)
        has_active_session = session is not None
        session_data = {}
        
        if session:
            session_data = {
                'workflow_type': session.workflow_type.value,
                'state': session.state.value,
                'prompt': session.prompt,
                'image_count': session.get_image_count(),
                'created_at': session.created_at,
                'updated_at': session.updated_at
            }
        
        return ProcessContext(
            user_id=user_id,
            chat_id=chat_id,
            raw_message=raw_message,
            user_input=user_input,
            has_active_session=has_active_session,
            session_data=session_data
        )
    
    def _extract_user_text(self, raw_message: Any) -> str:
        """提取用户文本"""
        try:
            if hasattr(raw_message, 'user_message') and raw_message.user_message:
                if hasattr(raw_message.user_message, 'content'):
                    content = raw_message.user_message.content
                    
                    if isinstance(content, str):
                        return content.strip()
                    elif isinstance(content, list):
                        # 提取文本内容
                        text_parts = []
                        for item in content:
                            if hasattr(item, 'type') and item.type == 'text':
                                if hasattr(item, 'text'):
                                    text_parts.append(item.text)
                        return " ".join(text_parts).strip()
            
            # 尝试其他方式提取文本
            if hasattr(raw_message, 'message_chain'):
                return str(raw_message.message_chain).strip()
            
            return ""
            
        except Exception as e:
            self._log(f"提取用户文本失败: {e}", "error")
            return ""
    
    async def _handle_session_interaction(self, context: ProcessContext, 
                                        session: Any) -> ProcessResult:
        """处理会话内交互"""
        user_text = context.user_input.text
        user_images = context.user_input.images
        
        # 检查会话超时
        if not session.is_active():
            self.session_manager.delete_session(context.user_id, context.chat_id)
            return ProcessResult(
                success=False,
                action=ProcessAction.TIMEOUT,
                message="会话已超时，请重新开始",
                session_updated=True
            )
        
        # 检查取消指令
        if self._is_cancel_command(user_text):
            self.session_manager.delete_session(context.user_id, context.chat_id)
            return ProcessResult(
                success=True,
                action=ProcessAction.CANCEL,
                message="会话已取消",
                session_updated=True
            )
        
        # 检查执行指令（go/开始）
        if self._is_execution_command(user_text):
            if session.can_execute():
                # 执行一次性路由决策
                return await self._execute_workflow_decision(context, session)
            else:
                return ProcessResult(
                    success=False,
                    action=ProcessAction.ERROR,
                    message="会话尚未准备好执行，请继续添加内容",
                    session_data=context.session_data
                )
        
        # 收集模式：累积内容，不进行工作流选择
        return await self._collect_content(context, session)
    
    async def _collect_content(self, context: ProcessContext, session: Any) -> ProcessResult:
        """收集模式：累积文本和图片，不进行工作流选择"""
        user_text = context.user_input.text
        user_images = context.user_input.images
        
        # 处理图片添加
        if user_images:
            added_count = 0
            errors = []
            
            for image_data in user_images:
                success, message, updated_session = self.session_manager.add_image_to_session(
                    context.user_id, image_data, context.chat_id
                )
                if success:
                    added_count += 1
                else:
                    errors.append(message)
            
            if added_count > 0:
                result_message = f"成功添加 {added_count} 张图片"
                if errors:
                    result_message += f"，{len(errors)} 张失败"
                
                return ProcessResult(
                    success=True,
                    action=ProcessAction.IMAGE_ADDED,
                    message=result_message,
                    session_updated=True,
                    session_data=context.session_data,
                    metadata={'added_count': added_count, 'errors': errors}
                )
            else:
                return ProcessResult(
                    success=False,
                    action=ProcessAction.ERROR,
                    message="图片添加失败",
                    error_details="; ".join(errors)
                )
        
        # 处理提示词更新
        if user_text.strip():
            session.set_prompt(user_text)
            session.update_timestamp()
            
            return ProcessResult(
                success=True,
                action=ProcessAction.PROMPT_UPDATED,
                message="提示词已更新",
                session_updated=True,
                session_data=context.session_data,
                next_step="请继续添加图片或发送'go'执行"
            )
        
        # 继续等待用户输入
        return ProcessResult(
            success=True,
            action=ProcessAction.CONTINUE,
            message="请继续添加内容或发送'go'执行工作流",
            session_data=context.session_data
        )
    
    async def _execute_workflow_decision(self, context: ProcessContext, session: Any) -> ProcessResult:
        """执行一次性工作流决策"""
        try:
            # 获取累积的所有内容
            all_text = session.prompt
            all_images = session.images  # List[SessionImage]
            
            # 执行统一路由决策
            route_result = await self.unified_router.route_unified(
                user_text=all_text,
                has_images=bool(all_images),
                image_count=len(all_images) if all_images else 0,
                query=context.raw_message
            )
            
            if not route_result:
                return ProcessResult(
                    success=False,
                    action=ProcessAction.ERROR,
                    message="工作流路由失败：无法确定工作流类型"
                )
            
            # 更新会话状态
            session.state = SessionState.PROCESSING
            session.update_timestamp()
            
            return ProcessResult(
                success=True,
                action=ProcessAction.EXECUTE,
                message=f"开始执行 {route_result.workflow_type.value} 工作流",
                session_updated=True,
                session_data=context.session_data,
                workflow_selected=True,
                workflow_type=route_result.workflow_type.value,
                environment="local",  # 默认本地环境
                metadata={
                    'confidence': route_result.confidence.value,
                    'reasoning': route_result.reasoning,
                    'routing_level': route_result.routing_level.value,
                    'needs_clarification': route_result.needs_clarification,
                    'clarification_question': route_result.clarification_question,
                    'workflow_subtype': route_result.workflow_subtype.value,
                    'workflow_file': route_result.workflow_file
                },
                next_step="工作流执行中..."
            )
            
        except Exception as e:
            error_msg = f"工作流决策失败: {e}"
            self._log(error_msg, "error")
            return ProcessResult(
                success=False,
                action=ProcessAction.ERROR,
                message=error_msg,
                error_code="WORKFLOW_DECISION_ERROR"
            )
    
    async def _handle_new_request(self, context: ProcessContext) -> ProcessResult:
        """处理新工作流请求"""
        user_text = context.user_input.text
        user_images = context.user_input.images
        
        # 检查是否是触发词
        is_trigger_word = self._is_trigger_word(user_text)
        
        if not is_trigger_word and not user_images:
            return ProcessResult(
                success=True,
                action=ProcessAction.CONTINUE,
                message="未检测到工作流请求，请发送工作流指令或上传图片",
                suggested_actions=[
                    "发送 'aigen' 开始文生图工作流",
                    "发送 'kontext' 开始图生图工作流",
                    "发送 'kontext_api' 开始API工作流",
                    "上传图片开始处理"
                ]
            )
        
        # 检测到触发词，创建新会话
        if is_trigger_word:
            # 执行第一级路由确定管道类型
            pipeline_type = self.unified_router._route_level_1(user_text)
            
            if not pipeline_type:
                return ProcessResult(
                    success=False,
                    action=ProcessAction.ERROR,
                    message="无法识别工作流类型"
                )
            
            # 创建新会话
            success, message, session = self.session_manager.create_session(
                context.user_id,
                pipeline_type,
                context.chat_id
            )
            
            if not success:
                return ProcessResult(
                    success=False,
                    action=ProcessAction.ERROR,
                    message=f"创建会话失败: {message}"
                )
            
            # 添加初始内容到会话
            if user_text.strip():
                session.set_prompt(user_text)
            
            if user_images:
                for image_data in user_images:
                    session.add_image(image_data, purpose="reference", source="upload")
            
            # 更新会话状态
            session.update_timestamp()
            
            return ProcessResult(
                success=True,
                action=ProcessAction.SESSION_CREATED,
                message=f"已创建 {pipeline_type.value} 工作流会话",
                session_updated=True,
                session_data={
                    'workflow_type': pipeline_type.value,
                    'state': session.state.value,
                    'prompt': session.prompt,
                    'image_count': session.get_image_count()
                },
                workflow_selected=True,
                workflow_type=pipeline_type.value,
                environment="local",  # 默认本地环境
                next_step="继续添加内容或发送'go'执行"
            )
        
        # 只有图片没有触发词，需要询问
        return ProcessResult(
            success=False,
            action=ProcessAction.ERROR,
            message="检测到图片但未指定工作流类型，请发送工作流指令",
            suggested_actions=[
                "发送 'aigen' 开始文生图工作流",
                "发送 'kontext' 开始图生图工作流",
                "发送 'kontext_api' 开始API工作流"
            ]
        )
    
    def _is_trigger_word(self, text: str) -> bool:
        """检查是否是触发词"""
        if not text:
            return False
        
        text_lower = text.lower().strip()
        trigger_words = ["aigen", "kontext", "kontext_api"]
        
        for word in trigger_words:
            if text_lower.startswith(word + " "):
                return True
        
        return False
    
    def _is_execution_command(self, text: str) -> bool:
        """检查是否是执行指令"""
        text_lower = text.lower().strip()
        execution_commands = ['go', '开始', '生成', 'create', 'start', '执行', 'run']
        return text_lower in execution_commands
    
    def _is_cancel_command(self, text: str) -> bool:
        """检查是否是取消指令"""
        text_lower = text.lower().strip()
        cancel_commands = ['取消', 'cancel', '停止', 'stop', '退出', 'quit', '结束']
        return text_lower in cancel_commands
    
    def create_response_message(self, result: ProcessResult, 
                              options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """根据处理结果创建响应消息"""
        return self.message_sender.create_result_message(result, options)
    
    def get_session_info(self, user_id: str, chat_id: str = "") -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        session = self.session_manager.get_session(user_id, chat_id)
        if not session:
            return None
        
        return {
            'session_id': session.session_id,
            'workflow_type': session.workflow_type.value,
            'state': session.state.value,
            'prompt': session.prompt,
            'image_count': session.get_image_count(),
            'created_at': session.created_at,
            'updated_at': session.updated_at,
            'is_active': session.is_active(),
            'can_execute': session.can_execute()
        }
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        self.session_manager.cleanup_expired_sessions()


# 全局实例
message_processor = MessageProcessor() 