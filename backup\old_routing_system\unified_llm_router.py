"""
统一LLM工作流路由器
替代传统的硬编码关键词匹配，使用LLM智能理解用户意图并路由到合适的工作流
"""

import json
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

from ..session.models import WorkflowType
from ...provider import entities as llm_entities
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...core import entities as core_entities


class RouterConfidence(Enum):
    """路由置信度级别"""
    HIGH = "high"        # 高置信度，直接路由
    MEDIUM = "medium"    # 中等置信度，建议路由
    LOW = "low"          # 低置信度，使用默认
    UNKNOWN = "unknown"  # 无法判断，需要用户确认


@dataclass
class RoutingResult:
    """路由结果"""
    workflow_type: WorkflowType
    confidence: RouterConfidence
    reasoning: str
    suggested_prompt: str = ""
    alternative_workflows: Optional[List[WorkflowType]] = None
    needs_clarification: bool = False
    clarification_question: str = ""
    
    def __post_init__(self):
        if self.alternative_workflows is None:
            self.alternative_workflows = []


class UnifiedLLMRouter:
    """统一LLM工作流路由器"""
    
    def __init__(self, ap=None):
        self.ap = ap
        self.logger = logging.getLogger(__name__)
        
        # LLM路由系统提示词
        self.system_prompt = """你是一个智能工作流路由器，负责理解用户意图并选择最合适的AI工作流。

可用的工作流类型：
1. aigen - 文生图工作流（本地Flux）
   - 适用于：从文本描述生成图片
   - 可选输入：文本提示词 + 可选参考图片（控制图/参考图）
   - 特点：支持ControlNet、LoRA、高质量生成

2. kontext - 图生图工作流（本地Kontext）  
   - 适用于：基于现有图片进行编辑、修改、风格转换
   - 必需输入：至少1张图片 + 编辑指令
   - 特点：保持原图结构，精准编辑

3. kontext_api - 远程API工作流
   - 适用于：复杂的图像处理，当本地资源不足时
   - 必需输入：图片 + 处理指令
   - 特点：云端处理，功能丰富

路由规则：
- 如果用户明确提到"aigen"、"文生图"、"生成图片"等 → aigen
- 如果用户明确提到"kontext"、"编辑"、"修改图片"等 → kontext  
- 如果用户请求复杂处理或提到"api"、"云端"等 → kontext_api
- 根据用户描述的任务类型智能选择最合适的工作流
- 考虑输入条件（是否有图片、图片数量）

请以JSON格式返回结果：
{
    "workflow_type": "aigen|kontext|kontext_api",
    "confidence": "high|medium|low|unknown", 
    "reasoning": "选择这个工作流的理由",
    "suggested_prompt": "优化后的提示词（可选）",
    "alternative_workflows": ["备选工作流列表"],
    "needs_clarification": true/false,
    "clarification_question": "需要用户确认的问题（如果有）"
}"""

    async def route_workflow(
        self, 
        user_text: str, 
        has_images: bool = False,
        image_count: int = 0,
        query: Optional[Any] = None
    ) -> RoutingResult:
        """
        智能路由工作流
        
        Args:
            user_text: 用户输入文本
            has_images: 是否有图片
            image_count: 图片数量
            query: 查询对象（用于LLM调用）
            
        Returns:
            RoutingResult: 路由结果
        """
        try:
            # 首先尝试LLM智能路由
            if query:
                llm_result = await self._route_with_llm(user_text, has_images, image_count, query)
                if llm_result:
                    self.logger.info(f"LLM路由成功: {llm_result.workflow_type.value}, 置信度: {llm_result.confidence.value}")
                    return llm_result
            
            # LLM路由失败，使用智能启发式路由
            heuristic_result = self._route_with_heuristics(user_text, has_images, image_count)
            self.logger.info(f"启发式路由: {heuristic_result.workflow_type.value}")
            return heuristic_result
            
        except Exception as e:
            self.logger.error(f"工作流路由失败: {e}")
            return self._get_fallback_route(user_text, has_images)
    
    async def _route_with_llm(
        self, 
        user_text: str, 
        has_images: bool, 
        image_count: int, 
        query: Any
    ) -> Optional[RoutingResult]:
        """使用LLM进行智能路由"""
        
        if not self.ap or not query.pipeline_config:
            return None
        
        # 获取LLM模型
        model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
        if not model_uuid:
            return None
        
        # 找到对应的RuntimeLLMModel
        runtime_llm_model = None
        for model in self.ap.model_mgr.llm_models:
            if model.model_entity.uuid == model_uuid:
                runtime_llm_model = model
                break
        
        if not runtime_llm_model:
            return None
        
        # 构建用户查询
        context_info = {
            "user_text": user_text,
            "has_images": has_images,
            "image_count": image_count,
            "timestamp": time.time()
        }
        
        user_prompt = f"""用户请求: "{user_text}"
输入条件:
- 是否有图片: {has_images}
- 图片数量: {image_count}

请分析用户意图并选择最合适的工作流。"""
        
        # 创建消息
        messages = [
            llm_entities.Message(role='system', content=self.system_prompt),
            llm_entities.Message(role='user', content=user_prompt)
        ]
        
        try:
            # 调用LLM
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )
            
            # 解析响应
            response_text = self._extract_response_text(result)
            if not response_text:
                return None
            
            # 解析JSON
            parsed_result = self._parse_llm_response(response_text)
            if parsed_result:
                self.logger.info(f"LLM工作流路由成功: {parsed_result.reasoning}")
                return parsed_result
            
        except Exception as e:
            self.logger.error(f"LLM路由调用失败: {e}")
            return None
        
        return None
    
    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""
        
        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)
        
        return response_text.strip()
    
    def _parse_llm_response(self, response_text: str) -> Optional[RoutingResult]:
        """解析LLM路由响应"""
        try:
            # 尝试提取JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                # 验证和转换结果
                workflow_type_str = data.get('workflow_type', '').lower()
                confidence_str = data.get('confidence', 'unknown').lower()
                
                # 映射工作流类型
                workflow_mapping = {
                    'aigen': WorkflowType.AIGEN,
                    'kontext': WorkflowType.KONTEXT, 
                    'kontext_api': WorkflowType.KONTEXT_API
                }
                
                if workflow_type_str not in workflow_mapping:
                    return None
                
                # 映射置信度
                confidence_mapping = {
                    'high': RouterConfidence.HIGH,
                    'medium': RouterConfidence.MEDIUM,
                    'low': RouterConfidence.LOW,
                    'unknown': RouterConfidence.UNKNOWN
                }
                
                confidence = confidence_mapping.get(confidence_str, RouterConfidence.UNKNOWN)
                
                # 解析备选工作流
                alternatives = []
                for alt in data.get('alternative_workflows', []):
                    if alt.lower() in workflow_mapping:
                        alternatives.append(workflow_mapping[alt.lower()])
                
                return RoutingResult(
                    workflow_type=workflow_mapping[workflow_type_str],
                    confidence=confidence,
                    reasoning=str(data.get('reasoning', '')),
                    suggested_prompt=str(data.get('suggested_prompt', '')),
                    alternative_workflows=alternatives,
                    needs_clarification=bool(data.get('needs_clarification', False)),
                    clarification_question=str(data.get('clarification_question', ''))
                )
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            self.logger.warning(f"解析LLM路由响应失败: {e}, 响应内容: {response_text}")
            
        return None
    
    def _route_with_heuristics(
        self, 
        user_text: str, 
        has_images: bool, 
        image_count: int
    ) -> RoutingResult:
        """启发式路由（智能关键词匹配）"""
        
        user_text_lower = user_text.lower()
        
        # 明确的工作流关键词
        aigen_keywords = ['aigen', 'ai生成', '文生图', 'text to image', '生成图片', '画', '创建', '制作']
        kontext_keywords = ['kontext', '编辑', '修改', '图生图', 'image to image', '转换', '风格化']
        api_keywords = ['kontext_api', 'api', '远程', '云端', '在线']
        
        # 检查明确关键词
        if any(keyword in user_text_lower for keyword in aigen_keywords):
            return RoutingResult(
                workflow_type=WorkflowType.AIGEN,
                confidence=RouterConfidence.HIGH,
                reasoning="检测到文生图相关关键词"
            )
        
        if any(keyword in user_text_lower for keyword in kontext_keywords):
            if has_images:
                return RoutingResult(
                    workflow_type=WorkflowType.KONTEXT,
                    confidence=RouterConfidence.HIGH,
                    reasoning="检测到图像编辑关键词且有输入图片"
                )
            else:
                return RoutingResult(
                    workflow_type=WorkflowType.KONTEXT,
                    confidence=RouterConfidence.MEDIUM,
                    reasoning="检测到图像编辑关键词但无输入图片",
                    needs_clarification=True,
                    clarification_question="图像编辑需要提供图片，请上传图片后再试"
                )
        
        if any(keyword in user_text_lower for keyword in api_keywords):
            return RoutingResult(
                workflow_type=WorkflowType.KONTEXT_API,
                confidence=RouterConfidence.HIGH,
                reasoning="检测到API工作流关键词"
            )
        
        # 智能推理：根据输入条件和任务描述
        if has_images and image_count > 0:
            # 有图片输入，更可能是图生图
            edit_patterns = ['修改', '编辑', '改变', '转换', '处理', '调整']
            if any(pattern in user_text_lower for pattern in edit_patterns):
                return RoutingResult(
                    workflow_type=WorkflowType.KONTEXT,
                    confidence=RouterConfidence.MEDIUM,
                    reasoning="有图片输入且含有编辑指令"
                )
            else:
                # 有图片但没有明确编辑指令，可能是文生图的参考图
                return RoutingResult(
                    workflow_type=WorkflowType.AIGEN,
                    confidence=RouterConfidence.MEDIUM,
                    reasoning="有图片输入，可能作为文生图的参考"
                )
        else:
            # 无图片输入，更可能是文生图
            generation_patterns = ['生成', '创建', '画', '制作', '绘制', '设计']
            if any(pattern in user_text_lower for pattern in generation_patterns):
                return RoutingResult(
                    workflow_type=WorkflowType.AIGEN,
                    confidence=RouterConfidence.MEDIUM,
                    reasoning="无图片输入且含有生成指令"
                )
        
        # 默认情况
        return RoutingResult(
            workflow_type=WorkflowType.AIGEN,
            confidence=RouterConfidence.LOW,
            reasoning="默认选择文生图工作流",
            alternative_workflows=[WorkflowType.KONTEXT, WorkflowType.KONTEXT_API]
        )
    
    def _get_fallback_route(self, user_text: str, has_images: bool) -> RoutingResult:
        """获取后备路由"""
        return RoutingResult(
            workflow_type=WorkflowType.AIGEN,
            confidence=RouterConfidence.UNKNOWN,
            reasoning="路由失败，使用默认工作流",
            needs_clarification=True,
            clarification_question="无法确定工作流类型，已选择默认的文生图工作流。如需其他功能请明确说明。"
        )
    
    def get_workflow_description(self, workflow_type: WorkflowType) -> str:
        """获取工作流描述"""
        descriptions = {
            WorkflowType.AIGEN: "🎨 **文生图工作流** - 根据文本描述生成图片，支持参考图和控制图",
            WorkflowType.KONTEXT: "✏️ **图生图工作流** - 编辑和修改现有图片，保持原图结构",
            WorkflowType.KONTEXT_API: "☁️ **远程API工作流** - 云端图像处理，功能丰富"
        }
        return descriptions.get(workflow_type, "❓ 未知工作流")
    
    def explain_routing_decision(self, result: RoutingResult) -> str:
        """解释路由决策"""
        explanation = f"{self.get_workflow_description(result.workflow_type)}\n\n"
        explanation += f"**选择原因**: {result.reasoning}\n"
        explanation += f"**置信度**: {result.confidence.value}\n"
        
        if result.alternative_workflows:
            alt_names = [wf.value for wf in result.alternative_workflows]
            explanation += f"**备选方案**: {', '.join(alt_names)}\n"
        
        if result.needs_clarification:
            explanation += f"**需要确认**: {result.clarification_question}\n"
        
        return explanation


# 全局实例
unified_router: Optional[UnifiedLLMRouter] = None

def get_unified_router(ap=None) -> UnifiedLLMRouter:
    """获取统一路由器实例"""
    global unified_router
    if unified_router is None:
        unified_router = UnifiedLLMRouter(ap)
    return unified_router 