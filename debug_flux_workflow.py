#!/usr/bin/env python3
"""
调试实际Flux工作流
使用真实的图片数据测试
"""

import base64
import json
import os
import sys
import aiohttp
import asyncio
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

def load_flux_workflow_template(workflow_file: str) -> Optional[Dict[str, Any]]:
    """
    加载Flux工作流模板
    
    Args:
        workflow_file: 工作流文件路径
        
    Returns:
        Dict[str, Any]: 工作流数据
    """
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        return workflow_data
    except Exception as e:
        print(f"❌ 加载工作流文件失败: {e}")
        return None

def apply_image_to_workflow(workflow_data: Dict[str, Any], image_data: bytes, node_title: str = "controlnet_image_input") -> Dict[str, Any]:
    """
    将图片应用到工作流
    
    Args:
        workflow_data: 工作流数据
        image_data: 图片数据
        node_title: 目标节点标题
        
    Returns:
        Dict[str, Any]: 修改后的工作流数据
    """
    # 转换为base64
    image_base64 = base64.b64encode(image_data).decode('utf-8')
    
    # 查找目标节点
    target_node_id = None
    for node_id, node_data in workflow_data.items():
        if isinstance(node_data, dict) and 'class_type' in node_data and '_meta' in node_data:
            title = node_data['_meta'].get('title', '')
            if title == node_title:
                target_node_id = node_id
                print(f"✅ 找到目标节点: {node_id} ({title})")
                break
    
    if target_node_id:
        workflow_data[target_node_id]["inputs"]["base64_data"] = image_base64
        print(f"✅ 成功应用图片到节点 {target_node_id}")
    else:
        print(f"❌ 未找到目标节点: {node_title}")
    
    return workflow_data

async def test_flux_workflow_submission(workflow_data: Dict[str, Any], api_url: str = "http://localhost:8188"):
    """
    测试Flux工作流提交
    
    Args:
        workflow_data: 工作流数据
        api_url: ComfyUI API地址
    """
    print(f"🚀 测试Flux工作流提交到ComfyUI: {api_url}")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # 准备提交数据
            prompt_data = {"prompt": workflow_data}
            
            print(f"📋 提交数据大小: {len(json.dumps(prompt_data))} 字符")
            
            # 检查关键节点
            print("\n🔍 检查关键节点:")
            image_nodes = []
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict):
                    class_type = node_data.get("class_type", "")
                    if class_type == "easy loadImageBase64":
                        title = node_data.get("_meta", {}).get("title", "")
                        inputs = node_data.get("inputs", {})
                        base64_data = inputs.get("base64_data", "")
                        
                        image_nodes.append((node_id, title, base64_data))
                        print(f"  🔸 节点 {node_id}: {title}")
                        print(f"    📝 base64_data长度: {len(base64_data)} 字符")
                        
                        if base64_data:
                            # 验证base64数据
                            try:
                                decoded_data = base64.b64decode(base64_data)
                                print(f"    ✅ 解码成功，大小: {len(decoded_data)} bytes")
                                
                                # 检查图片格式
                                if decoded_data.startswith(b'\xff\xd8\xff'):
                                    print(f"    📷 格式: JPEG")
                                elif decoded_data.startswith(b'\x89PNG\r\n\x1a\n'):
                                    print(f"    📷 格式: PNG")
                                else:
                                    print(f"    ⚠️  未知格式，头部: {decoded_data[:16].hex()}")
                                    
                            except Exception as e:
                                print(f"    ❌ 解码失败: {e}")
                        else:
                            print(f"    ⚠️  base64_data为空")
            
            print(f"\n📊 图片节点统计: {len(image_nodes)} 个")
            
            # 提交工作流
            print(f"\n📤 提交工作流...")
            async with session.post(f"{api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    print(f"✅ 提交成功，prompt_id: {prompt_id}")
                    
                    # 检查队列状态
                    print(f"\n📊 检查队列状态...")
                    async with session.get(f"{api_url}/queue") as queue_response:
                        if queue_response.status == 200:
                            queue_data = await queue_response.json()
                            queue_running = queue_data.get("queue_running", [])
                            queue_pending = queue_data.get("queue_pending", [])
                            
                            print(f"✅ 队列状态:")
                            print(f"  🔄 运行中: {len(queue_running)} 个")
                            print(f"  ⏳ 等待中: {len(queue_pending)} 个")
                            
                            if queue_running:
                                for item in queue_running:
                                    item_prompt_id = item[1]
                                    if item_prompt_id == prompt_id:
                                        print(f"  🎯 当前工作流正在运行")
                                        break
                    
                    return prompt_id
                else:
                    error_text = await response.text()
                    print(f"❌ 提交失败: {response.status}, {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        return None

async def main():
    """主函数"""
    print("🔍 Flux工作流调试工具")
    print("=" * 60)
    
    # 测试ComfyUI连接
    api_url = "http://localhost:8188"
    
    # 创建测试图片数据（模拟真实的图片）
    test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
    
    print(f"📷 测试图片大小: {len(test_image_data)} bytes")
    
    # 测试flux_controlnet.json工作流
    workflow_file = "workflows/flux_controlnet.json"
    print(f"\n📁 测试工作流文件: {workflow_file}")
    
    workflow_data = load_flux_workflow_template(workflow_file)
    if not workflow_data:
        print("❌ 无法加载工作流文件")
        return
    
    print(f"✅ 成功加载工作流，包含 {len(workflow_data)} 个节点")
    
    # 应用图片到工作流
    workflow_data = apply_image_to_workflow(workflow_data, test_image_data, "controlnet_image_input")
    
    # 测试工作流提交
    prompt_id = await test_flux_workflow_submission(workflow_data, api_url)
    
    if prompt_id:
        print(f"\n✅ 测试完成，prompt_id: {prompt_id}")
        
        # 等待一段时间看是否有错误
        print(f"\n⏳ 等待5秒观察执行情况...")
        await asyncio.sleep(5)
        
        # 检查历史记录
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{api_url}/history/{prompt_id}") as response:
                    if response.status == 200:
                        history_data = await response.json()
                        if prompt_id in history_data:
                            prompt_data = history_data[prompt_id]
                            if 'error' in prompt_data:
                                error_info = prompt_data['error']
                                print(f"❌ 工作流执行出错:")
                                print(f"  📝 错误信息: {error_info.get('message', '未知错误')}")
                                print(f"  📋 错误详情: {json.dumps(error_info, indent=2)}")
                            else:
                                print(f"✅ 工作流执行成功")
                        else:
                            print(f"⏳ 工作流仍在执行中")
                    else:
                        print(f"❌ 获取历史记录失败: {response.status}")
        except Exception as e:
            print(f"❌ 检查历史记录失败: {e}")
    else:
        print(f"\n❌ 测试失败")
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    asyncio.run(main()) 