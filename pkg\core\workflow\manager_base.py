from typing import Protocol, Any, Dict, Optional
from dataclasses import dataclass

@dataclass
class WorkflowResult:
    success: bool
    image_data: Optional[bytes] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class BaseWorkflowManager(Protocol):
    async def execute_workflow(self, prompt: str, query: Any) -> WorkflowResult:
        ...
    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        ...
    async def close(self) -> None:
        ... 