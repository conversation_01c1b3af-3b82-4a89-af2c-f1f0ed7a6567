apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: slack
  label:
    en_US: Slack
    zh_Hans: Slack
  description:
    en_US: Slack Adapter
    zh_Hans: Slack 适配器，请查看文档了解使用方式
  icon: slack.png
spec:
  config:
    - name: bot_token
      label:
        en_US: <PERSON><PERSON>
        zh_Hans: 机器人令牌
      type: string
      required: true
      default: ""
    - name: signing_secret
      label:
        en_US: signing_secret
        zh_Hans: 密钥
      type: string
      required: true
      default: ""
    - name: port
      label:
        en_US: Port
        zh_Hans: 监听端口
      type: int
      required: true
      default: 2288
execution:
  python:
    path: ./slack.py
    attr: SlackAdapter