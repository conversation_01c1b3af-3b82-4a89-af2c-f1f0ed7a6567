"""
Lora模型管理命令
支持查看、配置、更新Lora模型
"""
import asyncio
from typing import List, Optional, AsyncGenerator, TYPE_CHECKING
from ...command import operator, entities as cmd_entities
from pkg.workers.shared.shared_lora_manager import SharedLoraManager, LoraCategory, LoraModel

if TYPE_CHECKING:
    from ...core import app


@operator.operator_class(
    name="lora",
    help="Lora模型管理命令",
    usage="/lora <命令> [参数]",
    alias=["/lora"],
    privilege=1
)
class LoraCommand(operator.CommandOperator):
    """Lora模型管理命令"""
    
    def __init__(self, ap: "app.Application"):
        super().__init__(ap)
        self.lora_manager = SharedLoraManager()
    
    async def execute(self, context: cmd_entities.ExecuteContext) -> AsyncGenerator[cmd_entities.CommandReturn, None]:
        """执行Lora管理命令"""
        args = context.crt_params
        if not args:
            yield cmd_entities.CommandReturn(text=await self._show_help())
            return
        
        command = args[0].lower()
        
        if command == "list":
            result = await self._list_models(args[1:])
        elif command == "info":
            result = await self._show_model_info(args[1:])
        elif command == "search":
            result = await self._search_models(args[1:])
        elif command == "update":
            result = await self._update_from_civitai(args[1:])
        elif command == "config":
            result = await self._configure_model(args[1:])
        elif command == "stats":
            result = await self._show_statistics()
        elif command == "help":
            result = await self._show_help()
        else:
            result = f"未知命令: {command}\n{await self._show_help()}"
        
        yield cmd_entities.CommandReturn(text=result)
    
    async def _show_help(self) -> str:
        """显示帮助信息"""
        help_text = """
🎨 Lora模型管理命令

用法: /lora <命令> [参数]

可用命令:
  list [分类]           - 列出所有Lora模型（可选按分类筛选）
  info <模型名>         - 显示指定模型的详细信息
  search <关键词>       - 搜索匹配的Lora模型
  update [关键词]       - 从Civitai更新Lora模型信息
  config <模型名> <属性> <值> - 配置模型属性
  stats                 - 显示统计信息
  help                  - 显示此帮助信息

分类选项:
  architecture  - 建筑类
  portrait      - 人像类
  landscape     - 风景类
  anime         - 动漫类
  detail        - 细节增强类
  style         - 风格类
  object        - 物体类
  other         - 其他类

示例:
  /lora list                    # 列出所有模型
  /lora list architecture       # 列出建筑类模型
  /lora info ASTRA_Flux_OC_Vbeta-2
  /lora search 建筑
  /lora update flux
  /lora config detail_aidmafluxproultra-FLUX-v0.1 weight 0.9
  /lora stats
"""
        return help_text.strip()
    
    async def _list_models(self, args: List[str]) -> str:
        """列出Lora模型"""
        if args and args[0]:
            try:
                category = LoraCategory(args[0])
                models = self.lora_manager.get_models_by_category(category)
                title = f"📋 {category.value.title()} 类Lora模型"
            except ValueError:
                return f"❌ 无效的分类: {args[0]}"
        else:
            models = list(self.lora_manager.lora_models.values())
            title = "📋 所有Lora模型"
        
        if not models:
            return f"{title}\n暂无模型"
        
        # 按评分和下载量排序
        models.sort(key=lambda x: (x.rating or 0, x.downloads or 0), reverse=True)
        
        result = [title, ""]
        
        for i, model in enumerate(models[:20], 1):  # 最多显示20个
            status = "🟢" if model.is_active else "🔴"
            local_remote = "📁" if model.is_local else "☁️"
            priority = "⭐" if model.is_priority else ""
            rating_str = f"⭐{model.rating:.1f}" if model.rating else ""
            downloads_str = f"📥{model.downloads:,}" if model.downloads else ""
            
            result.append(f"{i:2d}. {status}{local_remote}{priority} {model.name}")
            result.append(f"     📂 {model.filename}")
            result.append(f"     🏷️  {model.category.value}")
            if model.trigger_words:
                result.append(f"     🎯 {' '.join(model.trigger_words[:3])}")
            if rating_str or downloads_str:
                result.append(f"     {rating_str} {downloads_str}")
            result.append("")
        
        if len(models) > 20:
            result.append(f"... 还有 {len(models) - 20} 个模型")
        
        return "\n".join(result)
    
    async def _show_model_info(self, args: List[str]) -> str:
        """显示模型详细信息"""
        if not args:
            return "❌ 请指定模型名称\n用法: /lora info <模型名>"
        
        model_name = args[0]
        if model_name not in self.lora_manager.lora_models:
            return f"❌ 模型不存在: {model_name}"
        
        model = self.lora_manager.lora_models[model_name]
        
        result = [
            f"📋 模型信息: {model.name}",
            f"📂 文件名: {model.filename}",
            f"📁 路径: {model.file_path}",
            f"🏷️ 分类: {model.category.value}",
            f"⚖️ 权重: {model.weight}",
            f"📝 描述: {model.description}",
            f"🎯 触发词: {', '.join(model.trigger_words) if model.trigger_words else '无'}",
            f"🔄 状态: {'启用' if model.is_active else '禁用'}",
            f"📍 类型: {'本地' if model.is_local else '远程'}"
        ]
        
        if model.civitai_id:
            result.extend([
                f"🌐 Civitai ID: {model.civitai_id}",
                f"🔗 Civitai URL: {model.civitai_url}"
            ])
        
        if model.rating:
            result.append(f"⭐ 评分: {model.rating:.1f}")
        
        if model.downloads:
            result.append(f"📥 下载次数: {model.downloads:,}")
        
        return "\n".join(result)
    
    async def _search_models(self, args: List[str]) -> str:
        """搜索模型"""
        if not args:
            return "❌ 请指定搜索关键词\n用法: /lora search <关键词>"
        
        keyword = " ".join(args)
        models = self.lora_manager.get_models_by_trigger(keyword)
        
        if not models:
            return f"🔍 未找到匹配 '{keyword}' 的Lora模型"
        
        result = [f"🔍 搜索 '{keyword}' 的结果:"]
        
        for i, model in enumerate(models[:10], 1):  # 最多显示10个
            status = "🟢" if model.is_active else "🔴"
            local_remote = "📁" if model.is_local else "☁️"
            rating_str = f"⭐{model.rating:.1f}" if model.rating else ""
            
            result.append(f"{i}. {status}{local_remote} {model.name}")
            result.append(f"   🏷️ {model.category.value} | ⚖️ {model.weight}")
            if rating_str:
                result.append(f"   {rating_str}")
            result.append("")
        
        if len(models) > 10:
            result.append(f"... 还有 {len(models) - 10} 个匹配结果")
        
        return "\n".join(result)
    
    async def _update_from_civitai(self, args: List[str]) -> str:
        """从Civitai更新模型信息"""
        query = "flux" if not args else " ".join(args)
        
        try:
            self.lora_manager.update_from_civitai(query=query, limit=20)
            return f"✅ 已从Civitai更新Lora模型信息 (搜索关键词: {query})"
        except Exception as e:
            return f"❌ 更新失败: {str(e)}"
    
    async def _configure_model(self, args: List[str]) -> str:
        """配置模型属性"""
        if len(args) < 3:
            return "❌ 参数不足\n用法: /lora config <模型名> <属性> <值>"
        
        model_name = args[0]
        property_name = args[1]
        value = " ".join(args[2:])
        
        if model_name not in self.lora_manager.lora_models:
            return f"❌ 模型不存在: {model_name}"
        
        # 验证属性名
        valid_properties = ["weight", "description", "is_active", "category"]
        if property_name not in valid_properties:
            return f"❌ 无效属性: {property_name}\n有效属性: {', '.join(valid_properties)}"
        
        # 类型转换
        try:
            if property_name == "weight":
                value = float(value)
            elif property_name == "is_active":
                value = value.lower() in ["true", "1", "yes", "启用"]
            elif property_name == "category":
                value = LoraCategory(value)
        except (ValueError, KeyError):
            return f"❌ 无效的值: {value}"
        
        # 更新模型
        self.lora_manager.update_model(model_name, **{property_name: value})
        
        return f"✅ 已更新模型 {model_name} 的 {property_name} 为 {value}"
    
    async def _show_statistics(self) -> str:
        """显示统计信息"""
        stats = self.lora_manager.get_statistics()
        
        result = [
            "📊 Lora模型统计信息",
            f"📈 总数: {stats['total']}",
            f"🟢 启用: {stats['active']}",
            f"📁 本地: {stats['local']}",
            f"☁️ 远程: {stats['remote']}",
            f"⭐ 优先模型: {stats['priority']}",
            "",
            "📂 按分类统计:"
        ]
        
        for category, count in stats['by_category'].items():
            if count > 0:
                result.append(f"  {category}: {count}")
        
        return "\n".join(result)


# 命令已通过装饰器自动注册 