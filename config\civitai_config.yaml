# Civitai API配置
# 代理设置 - 支持HTTP和SOCKS代理
proxy: "http://127.0.0.1:7890"  # 根据你的代理设置修改

# API密钥（可选）
api_key: "a5eb0f9150c40b0d36e15d662a108c9e"  # 已填写API Key

# 超时设置
timeout: 30  # 请求超时时间（秒）

# 下载设置
download_timeout: 60  # 下载超时时间（秒）
chunk_size: 8192  # 下载块大小

# 搜索设置
default_limit: 20  # 默认搜索结果数量
default_sort: "Highest Rated"  # 默认排序方式

# 日志设置
log_level: "INFO"  # 日志级别

# API基础URL
base_url: "https://civitai.com/api/v1"

# 默认搜索参数
default_search:
  limit: 20
  sort: "Highest Rated"
  types: "LoRA"

# 用户代理
user_agent: "LangBot/1.0"

# 是否启用自动下载
auto_download: false

# 下载目录
download_dir: "/home/<USER>/Workspace/ComfyUI/models/loras"

# 是否启用自动下载
auto_download: false

# 下载目录
download_dir: "/home/<USER>/Workspace/ComfyUI/models/loras" 
 