# 🏆 ComfyUI官方API Key使用指南

## 📋 背景

根据[ComfyUI官方API Key集成文档](https://docs.comfy.org/zh-CN/development/comfyui-server/api-key-integration)，我们**不需要复杂的Firebase token自动化方案**。

ComfyUI现在支持通过官方Platform API Key直接调用API节点，**一次设置，长期使用**。

## 🚀 **超简单解决方案** (5分钟完成)

### 1️⃣ **获取官方API Key**

1. 访问 [ComfyUI Platform](https://platform.comfy.org/login)
2. 使用Google账户登录（推荐）
3. 在账户设置中生成API Key
   - 格式类似：`comfyui-87d01e28d*******************************************************`
4. 为账户购买积分（用于Flux Pro等付费模型）

### 2️⃣ **简化docker-compose.yaml配置**

```yaml
environment:
  - TZ=Asia/Shanghai
  # ComfyUI官方API Key（长期有效，无需定期更新）
  - "API_KEY_COMFY_ORG=comfyui-你的API_KEY"
  # 删除AUTH_TOKEN_COMFY_ORG（不再需要）
```

### 3️⃣ **重启容器**

```bash
# 使用管理脚本（推荐）
./langbot-manager.sh restart

# 或手动重启
docker stop langbot && docker rm langbot
docker-compose up -d
```

### 4️⃣ **测试Kontext工作流**

发送微信消息：`kontext 一只可爱的猫咪`

## 📊 **新方案 vs 旧方案对比**

| 方面 | 🏆 **新方案：官方API Key** | ❌ **旧方案：Firebase自动化** |
|------|-------------------------|---------------------------|
| **设置复杂度** | ⭐ 5分钟一次性设置 | ⭐⭐⭐⭐⭐ 需要多个组件配置 |
| **维护成本** | ⭐⭐⭐⭐⭐ 零维护 | ⭐ 需要定期维护自动化脚本 |
| **稳定性** | ⭐⭐⭐⭐⭐ 官方支持 | ⭐⭐ 依赖网页结构和浏览器 |
| **Token有效期** | ⭐⭐⭐⭐⭐ 长期有效 | ⭐ 每小时过期 |
| **故障点** | ⭐⭐⭐⭐⭐ 几乎无故障点 | ⭐ 多个故障点 |
| **技术栈** | ⭐⭐⭐⭐⭐ 纯API调用 | ⭐ Playwright+MCP+浏览器自动化 |

## 🔧 **LangBot代码适配**

LangBot已自动适配官方API Key方式：

```python
# 官方方案（优先）
if api_key:
    prompt_data["extra_data"]["api_key_comfy_org"] = api_key

# 兼容Firebase token（备用）  
elif auth_token:
    prompt_data["extra_data"]["api_key_comfy_org"] = auth_token
```

## ✅ **验证方法**

### **日志检查**
```bash
./langbot-manager.sh logs | grep -i "comfyui\|api_key"
```

应该看到：
```
已添加ComfyUI官方API Key到extra_data中
```

### **测试Kontext**
微信发送：`kontext 测试图片生成`

## 🗑️ **清理旧文件**

现在可以删除复杂的自动化文件：

```bash
# 删除不再需要的文件
rm -f plugins/firebase_token_auto_updater/plugin.py
rm -f scripts/install_firebase_auto_updater.sh  
rm -f scripts/browser_mcp_firebase_updater.py
rm -f docs/FIREBASE_TOKEN_AUTO_UPDATE_SOLUTIONS.md
```

## 💡 **最佳实践**

1. **API Key管理**：建议定期轮换API Key（可选）
2. **积分监控**：定期检查账户积分余额
3. **错误处理**：如遇401/402错误，检查API Key和积分
4. **备份配置**：保存API Key到安全位置

## 🎉 **总结**

通过使用ComfyUI官方API Key：

- ✅ **从复杂变简单**：从多组件自动化变为一个环境变量
- ✅ **从不稳定变稳定**：从依赖浏览器变为直接API调用  
- ✅ **从高维护变零维护**：设置一次，长期使用
- ✅ **从每小时更新变永久有效**：告别token过期问题

**这就是为什么要使用官方提供的解决方案！** 🎯 