"""
ComfyUI集成模块
此模块会在pipeline包导入时自动加载，并注册ComfyUI相关的处理阶段

二次开发说明：
- 此文件通过装饰器自动注册到langbot的pipeline系统中
- 不需要修改核心文件，保持与主项目的兼容性
- 升级langbot时此文件不会受影响
"""

from __future__ import annotations

import asyncio
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING, List

# 使用延迟导入避免循环依赖
if TYPE_CHECKING:
    from ..core import entities as core_entities

from . import entities, stage
from pkg.workers.kontext.local_kontext_workflow_manager import LocalKontextWorkflowManager
from pkg.workers.kontext.kontext_workflow_executor import KontextWorkflowExecutor


@stage.stage_class('ComfyUIIntegration')
class ComfyUIIntegrationStage(stage.PipelineStage):
    """ComfyUI集成阶段，处理图像生成请求、Kontext工作流和管理员同步
    
    功能包括：
    1. 检测图像生成请求
    2. 处理Kontext工作流（@kontext前缀）
    3. 重复生成检测和用户引导
    4. "再次生成"功能支持
    5. 管理员消息同步
    6. ComfyUI工作流管理
    """

    def __init__(self, ap):
        super().__init__(ap)
        self.logger = logging.getLogger(__name__)
        self.llm_preprocessor: Optional[Any] = None
        self.admin_sync_service: Optional[Any] = None
        
        # Kontext相关组件
        self.kontext_manager: Optional[Any] = None
        self.generation_tracker: Optional[Any] = None
        self.repeat_handler: Optional[Any] = None
        self.guidance_manager: Optional[Any] = None

    async def initialize(self, config: Dict[str, Any]):
        """初始化阶段组件"""
        
        self.logger.info("初始化ComfyUI集成阶段（含Kontext支持）...")
        
        try:
            # 初始化LLM预处理器
            try:
                from ..processors.llm_preprocessor import LLMPreprocessor
                llm_router = getattr(self.ap, 'llm_router', None)
                if llm_router:
                    self.llm_preprocessor = LLMPreprocessor(llm_router)
                else:
                    self.logger.warning("LLM路由器不可用，LLM预处理功能将被禁用")
            except Exception as e:
                self.logger.warning(f"LLM预处理器初始化失败: {e}")
            
            # 检查是否启用Kontext功能
            kontext_config = config.get("kontext", {})
            if kontext_config and kontext_config.get("enabled", False):
                # 直接初始化新版Kontext管理器
                self.kontext_manager = LocalKontextWorkflowManager()
                self.generation_tracker = None  # 如有新版tracker可在此初始化
                self.repeat_handler = None     # 如有新版repeat handler可在此初始化
                self.guidance_manager = None   # 如有新版guidance manager可在此初始化
            
        except Exception as e:
            self.logger.warning(f"组件初始化失败: {e}")
        
        self.logger.info("ComfyUI集成阶段（含Kontext支持）初始化完成")

    async def process(
        self,
        query: "core_entities.Query",
        stage_inst_name: str,
    ) -> entities.StageProcessResult:
        """处理消息，支持普通工作流和Kontext工作流"""
        
        try:
            # 检查是否启用了ComfyUI功能
            ai_config = query.pipeline_config.get("ai") if query.pipeline_config else None
            if ai_config is None:
                ai_config = {}
            comfyui_config = ai_config.get("comfyui-agent") if ai_config else None
            if comfyui_config is None:
                comfyui_config = {}
            if not comfyui_config.get("enabled", False):
                # 功能未启用，直接继续下一阶段
                return entities.StageProcessResult(
                    result_type=entities.ResultType.CONTINUE,
                    new_query=query
                )

            # 提取用户输入和图片
            user_input = self._extract_text_content(query.message_chain)
            # 安全获取用户ID
            user_id = "unknown_user"
            try:
                if hasattr(query, 'message_event') and query.message_event:
                    message_event = query.message_event
                    sender = getattr(message_event, 'sender', None)
                    if sender:
                        sender_id = getattr(sender, 'id', None)
                        if sender_id:
                            user_id = str(sender_id)
            except Exception as e:
                self.logger.warning(f"获取用户ID失败: {e}")
            
            attached_images = self._extract_images(query.message_chain)
            
            # ======= 图片反推自定义流程 =======
            # 1. 检查是否为"图片反推"触发词
            if user_input.strip() == "图片反推":
                query.set_variable('pending_image_to_text', True)
                await self._send_message(query, "请上传要识别的图片。")
                return entities.StageProcessResult(
                    result_type=entities.ResultType.INTERRUPT,
                    new_query=query
                )
            # 2. 检查是否处于等待图片反推状态
            if query.get_variable('pending_image_to_text'):
                if not attached_images:
                    await self._send_message(query, "请上传图片。")
                    return entities.StageProcessResult(
                        result_type=entities.ResultType.INTERRUPT,
                        new_query=query
                    )
                # 清除pending状态
                query.set_variable('pending_image_to_text', False)
                # 读取工作流模板
                import json, base64, os
                workflow_path = os.path.join('workflows', 'image_to_text_workflow.json')
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                # 填入base64图片
                img_bytes = None
                img_obj = attached_images[0]
                if hasattr(img_obj, 'data') and isinstance(img_obj.data, bytes):
                    img_bytes = img_obj.data
                elif hasattr(img_obj, 'raw') and isinstance(img_obj.raw, bytes):
                    img_bytes = img_obj.raw
                elif hasattr(img_obj, 'base64') and isinstance(img_obj.base64, str):
                    import base64 as b64
                    try:
                        img_bytes = b64.b64decode(img_obj.base64)
                    except Exception:
                        img_bytes = None
                elif isinstance(img_obj, bytes):
                    img_bytes = img_obj
                if img_bytes is None:
                    await self._send_message(query, "图片数据读取失败。")
                    return entities.StageProcessResult(
                        result_type=entities.ResultType.INTERRUPT,
                        new_query=query
                    )
                workflow_data["61"]["inputs"]["base64_data"] = base64.b64encode(img_bytes).decode('utf-8')
                # 提交到ComfyUI
                result = await self._execute_comfyui_workflow(workflow_data)
                # 解析ShowText节点输出
                text = None
                if result and isinstance(result, dict):
                    # 查找ShowText节点输出
                    for node_id, node_data in result.items():
                        if node_data.get("class_type", "").startswith("ShowText"):
                            # 兼容ComfyUI输出格式
                            if "text" in node_data:
                                text = node_data["text"]
                            elif "inputs" in node_data and "text" in node_data["inputs"]:
                                text = node_data["inputs"]["text"]
                            break
                if not text or not isinstance(text, str):
                    text = "未能识别图片内容。"
                await self._send_message(query, text)
                return entities.StageProcessResult(
                    result_type=entities.ResultType.CONTINUE,
                    new_query=query
                )
            
            # 首先检查是否为kontext请求 (支持 "kontext" 和 "@kontext" 两种格式)
            is_kontext_request = (user_input.startswith("@kontext") or user_input.startswith("kontext "))
            
            # 如果不是kontext请求，再检查重复生成和修改提示词指令
            if not is_kontext_request:
                # 检查是否为"再次生成"命令
                if self.repeat_handler and self.repeat_handler.is_repeat_command(user_input):
                    return await self._handle_repeat_generation(query, user_id)
                
                # 检查是否为"修改提示词为..."命令
                if self.repeat_handler:
                    is_modify, new_prompt = self.repeat_handler.is_modify_prompt_command(user_input)
                    if is_modify:
                        return await self._handle_modify_prompt_generation(query, user_id, new_prompt)
            
            # 处理用户对kontext引导的回复
            if (self.guidance_manager and 
                user_id in self.guidance_manager.pending_guidance_users):
                handled, response = self.guidance_manager.handle_user_response(user_id, user_input)
                if handled and response:
                    await self._send_message(query, response)
                    return entities.StageProcessResult(
                        result_type=entities.ResultType.INTERRUPT,
                        new_query=query
                    )
            
            # 检查是否询问kontext帮助
            if (self.guidance_manager and self.kontext_manager and
                self.guidance_manager.detect_kontext_help_request(user_input)):
                instructions = self.kontext_manager.get_kontext_instructions()
                await self._send_message(query, instructions)
                return entities.StageProcessResult(
                    result_type=entities.ResultType.INTERRUPT,
                    new_query=query
                )
            
            # 处理kontext请求
            if is_kontext_request:
                return await self._process_kontext_request(query, user_input, attached_images, user_id)
            
            # 处理普通工作流请求
            else:
                return await self._process_normal_workflow(query, user_input, attached_images, user_id)

        except Exception as e:
            self.logger.error(f"ComfyUI集成阶段处理失败: {str(e)}", exc_info=True)
            # 即使出错也继续处理，避免中断整个pipeline
            return entities.StageProcessResult(
                result_type=entities.ResultType.CONTINUE,
                new_query=query
            )

    async def _process_kontext_request(self, query, user_input: str, attached_images: List, user_id: str):
        """处理Kontext请求"""
        
        # 检查kontext_manager是否可用
        if not self.kontext_manager:
            await self._send_message(query, "Kontext功能暂不可用，请稍后再试。")
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        # 移除kontext前缀
        if user_input.startswith("@kontext"):
            kontext_prompt = user_input[8:].strip()  # 移除@kontext前缀
        elif user_input.startswith("kontext "):
            kontext_prompt = user_input[8:].strip()  # 移除kontext 前缀
        else:
            kontext_prompt = user_input.strip()  # 应该不会到这里
        
        # 验证图片要求
        if not attached_images or len(attached_images) == 0:
            await self._send_message(query, "Kontext工作流需要至少一张图片。请上传图片后再试。")
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        try:
            # 记录kontext生成请求
            if self.generation_tracker:
                self.generation_tracker.record_generation_request(
                    user_id, user_input, attached_images, "kontext"
                )
            
            # 选择工作流
            workflow_config = self.kontext_manager.select_workflow(len(attached_images))
            
            # 确定最佳比例
            aspect_ratio = self.kontext_manager.determine_optimal_ratio(attached_images, kontext_prompt)
            
            # 优化生成参数
            generation_params = self.kontext_manager.optimize_generation_params(kontext_prompt)
            
            # 发送到ComfyUI
            self.logger.info(f"执行Kontext工作流: {workflow_config.description}")
            await self._send_message(query, f"🎨 正在使用Kontext编辑图片...\n📝 指令: {kontext_prompt[:50]}{'...' if len(kontext_prompt) > 50 else ''}")
            
            # 调用实际的ComfyUI执行逻辑
            try:
                # 获取ComfyUI配置
                comfyui_config = query.pipeline_config.get('ai', {}).get('comfyui-agent', {})
                api_url = comfyui_config.get('api-url', 'http://localhost:8188')
                timeout = comfyui_config.get('timeout', 180)
                
                # 创建执行器（默认使用本地模式）
                workflow_exec = KontextWorkflowExecutor(mode='local', api_url=api_url, timeout=timeout)
                
                try:
                    # 准备工作流数据
                    workflow_data = await workflow_exec.prepare_workflow_data(
                        {}, attached_images, kontext_prompt, aspect_ratio, generation_params
                    )
                    
                    # 执行工作流
                    result = await workflow_exec.execute_workflow(kontext_prompt, query)
                    
                    if result.success:
                        self.logger.info(f"Kontext工作流执行成功: {workflow_config.workflow_file}")
                        if result.image_data:
                            await self._send_message(query, "✅ Kontext编辑完成！")
                    else:
                        error_msg = result.error_message or '未知错误'
                        self.logger.error(f"Kontext工作流执行失败: {error_msg}")
                        await self._send_message(query, f"❌ Kontext编辑失败: {error_msg}")
                        
                finally:
                    await workflow_exec.close()
                    
            except Exception as e:
                self.logger.error(f"执行Kontext工作流时出错: {e}")
                await self._send_message(query, f"❌ 执行Kontext工作流时出错: {str(e)}")
            
            # 初始化管理员同步服务
            await self._sync_to_admin_if_enabled(query, {
                "theme": f"Kontext编辑: {kontext_prompt[:30]}",
                "is_kontext": True
            })
            
            return entities.StageProcessResult(
                result_type=entities.ResultType.CONTINUE,
                new_query=query
            )
            
        except Exception as e:
            self.logger.error(f"Kontext请求处理失败: {str(e)}")
            await self._send_message(query, f"处理Kontext请求时出现错误: {str(e)}")
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )

    async def _process_normal_workflow(self, query, user_input: str, attached_images: List, user_id: str):
        """处理普通工作流请求"""
        
        try:
            # 记录普通生成请求
            if self.generation_tracker:
                self.generation_tracker.record_generation_request(
                    user_id, user_input, attached_images, "normal"
                )
                
                # 检查是否为完全重复的请求
                if self.generation_tracker.is_exact_repeat(user_id, user_input, attached_images):
                    if self.guidance_manager:
                        suggestion_msg = self.guidance_manager.offer_kontext_guidance(user_id)
                        await self._send_message(query, suggestion_msg)
            
            # 检测是否为图像生成请求
            if self.llm_preprocessor:
                analysis = await self.llm_preprocessor.process_message(query.message_chain)
                
                if analysis and analysis.get("is_image_request", False):
                    self.logger.info(f"检测到图像生成请求: {analysis.get('theme', '未知主题')}")
                    
                    # 同步到管理员
                    await self._sync_to_admin_if_enabled(query, analysis)
                    
                    # 在此处可以添加实际的ComfyUI处理逻辑
                    self.logger.info("普通ComfyUI图像生成逻辑将在此处实现")

            # 继续处理下一阶段
            return entities.StageProcessResult(
                result_type=entities.ResultType.CONTINUE,
                new_query=query
            )
            
        except Exception as e:
            self.logger.error(f"普通工作流处理失败: {str(e)}")
            return entities.StageProcessResult(
                result_type=entities.ResultType.CONTINUE,
                new_query=query
            )

    async def _handle_repeat_generation(self, query, user_id: str):
        """处理再次生成请求"""
        
        if not self.generation_tracker:
            await self._send_message(query, "再次生成功能暂不可用。")
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        last_params, error_msg = self.generation_tracker.get_last_generation_params(user_id)
        
        if error_msg:
            await self._send_message(query, error_msg)
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        # 提示用户正在使用上次参数
        info_msg = f"🔄 使用上次生成参数重新生成...\n"
        if last_params and last_params.get("is_kontext"):
            content = last_params.get("content", "")
            if len(content) > 8:
                info_msg += f"📝 Kontext指令: {content[8:].strip()[:50]}...\n"
        else:
            content = last_params.get("content", "") if last_params else ""
            info_msg += f"📝 提示词: {content[:50]}...\n"
        
        attached_images = last_params.get("attached_images", []) if last_params else []
        if attached_images:
            info_msg += f"🖼️ 图片数量: {len(attached_images)}张"
        
        await self._send_message(query, info_msg)
        
        # 根据类型重新执行生成
        if last_params and last_params.get("is_kontext"):
            # 重新执行kontext生成
            return await self._process_kontext_request(
                query, 
                last_params.get("content", ""),
                attached_images,
                user_id
            )
        else:
            # 重新执行普通工作流
            return await self._process_normal_workflow(
                query,
                last_params.get("content", "") if last_params else "",
                attached_images,
                user_id
            )
    
    async def _handle_modify_prompt_generation(self, query, user_id: str, new_prompt: str):
        """处理修改提示词的生成请求"""
        
        if not self.repeat_handler:
            await self._send_message(query, "修改提示词功能暂不可用。")
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        # 获取上次生成参数并更新提示词
        success, updated_params, message = self.repeat_handler.get_last_generation_with_new_prompt(user_id, new_prompt)
        
        if not success:
            await self._send_message(query, message)
            return entities.StageProcessResult(
                result_type=entities.ResultType.INTERRUPT,
                new_query=query
            )
        
        # 提示用户正在使用更新后的参数
        info_msg = f"🔄 {message}\n"
        
        attached_images = updated_params.get("attached_images", [])
        if attached_images:
            info_msg += f"🖼️ 使用上次的图片({len(attached_images)}张)\n"
        
        if updated_params.get("is_kontext"):
            info_msg += f"📝 新Kontext指令: {new_prompt[:50]}..."
        else:
            info_msg += f"📝 新提示词: {new_prompt[:50]}..."
        
        await self._send_message(query, info_msg)
        
        # 根据类型执行生成
        if updated_params.get("is_kontext"):
            # 执行kontext生成
            return await self._process_kontext_request(
                query, 
                updated_params.get("content", ""),
                attached_images,
                user_id
            )
        else:
            # 执行普通工作流
            return await self._process_normal_workflow(
                query,
                updated_params.get("content", ""),
                attached_images,
                user_id
            )

    async def _sync_to_admin_if_enabled(self, query, analysis: Dict[str, Any]):
        """如果启用了管理员同步，则同步消息"""
        
        try:
            # 延迟导入以避免循环依赖
            from ..services.admin_sync_service import AdminSyncService
            
            # 初始化管理员同步服务
            self.admin_sync_service = AdminSyncService(
                pipeline_config=query.pipeline_config,
                query=query,
                logger=self.logger
            )

            if self.admin_sync_service.is_sync_enabled():
                await self.admin_sync_service.sync_user_request(
                    event=query.message_event,
                    analysis=analysis,
                    adapter_instance=query.adapter
                )
                
                # 同步生成开始状态
                await self.admin_sync_service.sync_generation_status(
                    event=query.message_event,
                    status="starting",
                    details=f"主题: {analysis.get('theme', '未知')}",
                    adapter_instance=query.adapter
                )
                
        except Exception as e:
            self.logger.warning(f"管理员同步失败: {e}")

    def _extract_text_content(self, message_chain) -> str:
        """提取消息链中的文本内容"""
        
        text_content = ""
        if hasattr(message_chain, '__iter__'):
            for element in message_chain:
                if hasattr(element, 'text'):
                    text_content += element.text
        return text_content.strip()

    def _extract_images(self, message_chain) -> List[Any]:
        """提取消息链中的图片"""
        
        images = []
        if hasattr(message_chain, '__iter__'):
            for element in message_chain:
                if hasattr(element, 'type') and element.type == 'image':
                    images.append(element)
        return images

    async def _send_message(self, query, message: str):
        """发送消息给用户"""
        
        try:
            # 这里需要根据实际的消息发送API来实现
            # 暂时只记录日志
            self.logger.info(f"发送消息: {message}")
            
            # 实际实现可能类似：
            # await query.adapter.send_message(
            #     target_type="group" if hasattr(query.message_event.sender, 'group') else "friend",
            #     target_id=query.message_event.sender.group.id if hasattr(query.message_event.sender, 'group') else query.message_event.sender.id,
            #     message=message
            # )
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")

    async def _execute_comfyui_workflow(self, workflow_data: Dict[str, Any]):
        """执行ComfyUI工作流"""
        
        try:
            # 这里需要实现实际的ComfyUI API调用
            # 注意处理SaveImageWebsocket节点的特殊性
            self.logger.info("执行ComfyUI工作流...")
            
            # 实际实现可能涉及：
            # 1. 上传图片到ComfyUI
            # 2. 提交工作流到队列
            # 3. 监听WebSocket获取结果
            # 4. 处理SaveImageWebsocket的输出
            
        except Exception as e:
            self.logger.error(f"执行ComfyUI工作流失败: {e}")
            raise 