#!/usr/bin/env python3
"""
调试图片数据传递过程
检查base64编码和图片数据的完整性
"""

import base64
import json
import os
from typing import Optional

def validate_base64_image(base64_str: str) -> tuple[bool, str]:
    """
    验证base64图片数据的有效性
    
    Args:
        base64_str: base64编码的图片字符串
        
    Returns:
        (是否有效, 错误信息)
    """
    try:
        # 检查是否为空
        if not base64_str or len(base64_str.strip()) == 0:
            return False, "base64字符串为空"
        
        # 移除可能的data URL前缀
        if ',' in base64_str:
            base64_str = base64_str.split(',', 1)[1]
        
        # 检查长度
        if len(base64_str) < 100:  # 最小长度检查
            return False, f"base64字符串过短: {len(base64_str)} 字符"
        
        # 尝试解码
        try:
            image_data = base64.b64decode(base64_str)
        except Exception as e:
            return False, f"base64解码失败: {e}"
        
        # 检查解码后的数据
        if len(image_data) == 0:
            return False, "解码后图片数据为空"
        
        if len(image_data) < 100:  # 最小文件大小检查
            return False, f"图片文件过小: {len(image_data)} bytes"
        
        # 检查图片格式头部
        if image_data.startswith(b'\xff\xd8\xff'):  # JPEG
            format_name = "JPEG"
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):  # PNG
            format_name = "PNG"
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):  # GIF
            format_name = "GIF"
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:  # WEBP
            format_name = "WEBP"
        else:
            return False, f"不支持的图片格式，文件头: {image_data[:16].hex()}"
        
        return True, f"有效 {format_name} 图片，大小: {len(image_data)} bytes"
        
    except Exception as e:
        return False, f"验证过程异常: {e}"

def check_workflow_image_data(workflow_file: str) -> None:
    """
    检查工作流文件中的图片数据
    
    Args:
        workflow_file: 工作流文件路径
    """
    print(f"🔍 检查工作流文件: {workflow_file}")
    
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return
    
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        print(f"✅ 成功加载工作流文件，包含 {len(workflow_data)} 个节点")
        
        # 查找图片输入节点
        image_nodes = []
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                if class_type in ["easy loadImageBase64", "LoadImage"]:
                    title = node_data.get("_meta", {}).get("title", "")
                    inputs = node_data.get("inputs", {})
                    image_nodes.append((node_id, title, class_type, inputs))
        
        print(f"📷 找到 {len(image_nodes)} 个图片输入节点:")
        
        for node_id, title, class_type, inputs in image_nodes:
            print(f"\n  🔸 节点 {node_id}: {title} ({class_type})")
            
            if class_type == "easy loadImageBase64":
                base64_data = inputs.get("base64_data", "")
                if base64_data:
                    is_valid, message = validate_base64_image(base64_data)
                    if is_valid:
                        print(f"    ✅ {message}")
                    else:
                        print(f"    ❌ {message}")
                        print(f"    📝 base64数据预览: {base64_data[:50]}...")
                else:
                    print(f"    ⚠️  base64_data 为空")
            
            # 显示所有输入参数
            print(f"    📋 输入参数:")
            for key, value in inputs.items():
                if key == "base64_data" and value:
                    preview = value[:30] + "..." if len(value) > 30 else value
                    print(f"      {key}: {preview}")
                else:
                    print(f"      {key}: {value}")
    
    except Exception as e:
        print(f"❌ 检查工作流文件失败: {e}")

def test_image_processing() -> None:
    """
    测试图片处理流程
    """
    print("\n🧪 测试图片处理流程")
    
    # 创建一个简单的测试图片数据
    test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
    
    # 转换为base64
    test_base64 = base64.b64encode(test_image_data).decode('utf-8')
    
    print(f"📝 测试图片数据大小: {len(test_image_data)} bytes")
    print(f"📝 测试base64长度: {len(test_base64)} 字符")
    
    # 验证base64数据
    is_valid, message = validate_base64_image(test_base64)
    print(f"✅ 测试base64验证: {message}")
    
    # 测试解码
    try:
        decoded_data = base64.b64decode(test_base64)
        print(f"✅ 测试解码成功，大小: {len(decoded_data)} bytes")
        print(f"📝 解码数据头部: {decoded_data[:16].hex()}")
    except Exception as e:
        print(f"❌ 测试解码失败: {e}")

def main():
    """主函数"""
    print("🔍 图片数据调试工具")
    print("=" * 50)
    
    # 检查工作流文件
    workflow_files = [
        "workflows/flux_controlnet.json",
        "workflows/flux_controlnet_redux.json"
    ]
    
    for workflow_file in workflow_files:
        check_workflow_image_data(workflow_file)
    
    # 测试图片处理
    test_image_processing()
    
    print("\n" + "=" * 50)
    print("🎯 调试完成")

if __name__ == "__main__":
    main() 