#!/usr/bin/env python3
"""
调试LoadImage工作流
测试使用LoadImage节点替代base64节点的方案
"""

import base64
import json
import os
import sys
import asyncio
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Workspace/langbot')

def create_test_session_image() -> Any:
    """创建测试SessionImage对象"""
    try:
        from pkg.core.session.models import SessionImage, FluxImageType
        
        # 创建测试图片数据
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        # 创建SessionImage对象
        session_image = SessionImage(
            data=test_image_data,
            purpose="control",
            source="upload",
            flux_image_type=FluxImageType.CONTROL
        )
        
        print(f"✅ 成功创建SessionImage对象")
        print(f"📷 图片大小: {session_image.get_size()} bytes")
        print(f"🎯 图片类型: {session_image.flux_image_type.value}")
        
        return session_image
        
    except Exception as e:
        print(f"❌ 创建SessionImage对象失败: {e}")
        return None

async def test_image_file_manager():
    """测试图片文件管理器"""
    print("\n🧪 测试图片文件管理器:")
    
    try:
        from pkg.workers.flux.image_file_manager import get_image_file_manager
        
        # 获取图片文件管理器
        image_manager = get_image_file_manager("/tmp/comfyui_test_uploads")
        print(f"✅ 成功获取图片文件管理器")
        print(f"📁 上传目录: {image_manager.upload_dir}")
        
        # 测试保存图片
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        file_path = image_manager.save_image_to_local(test_image_data, "test_image.jpg")
        print(f"✅ 图片保存成功: {file_path}")
        
        # 检查文件信息
        file_info = image_manager.get_file_info(file_path)
        if file_info and file_info['exists']:
            print(f"✅ 文件信息验证成功: 大小 {file_info['size']} bytes")
        else:
            print(f"❌ 文件信息验证失败")
        
        return image_manager
        
    except Exception as e:
        print(f"❌ 测试图片文件管理器失败: {e}")
        return None

async def test_loadimage_workflow():
    """测试LoadImage工作流"""
    print("\n🚀 测试LoadImage工作流:")
    
    try:
        from pkg.workers.flux.flux_workflow_manager import get_flux_workflow_manager
        from pkg.core import app
        
        # 创建应用实例
        ap = app.Application()
        
        # 加载配置
        pipeline_config = {
            'ai': {
                'comfyui-agent': {
                    'api-url': 'http://localhost:8188',
                    'timeout': 180,
                    'workflow-path': 'workflows'
                }
            }
        }
        
        # 获取Flux工作流管理器
        workflow_manager = get_flux_workflow_manager(ap, pipeline_config)
        print(f"✅ 成功获取Flux工作流管理器")
        
        # 创建测试数据
        session_image = create_test_session_image()
        if not session_image:
            print("❌ 创建测试图片失败")
            return False
        
        # 测试LoadImage模式
        user_text = "生成一张建筑照片，现代风格，高楼大厦"
        session_images = [session_image]
        
        print(f"📝 用户输入: {user_text}")
        print(f"📷 图片数量: {len(session_images)}")
        
        # 执行工作流（使用LoadImage模式）
        result = await workflow_manager.execute_flux_workflow(
            user_text, 
            None, 
            session_images
        )
        
        if result.success:
            print(f"✅ LoadImage工作流执行成功")
            print(f"⏱️  执行时间: {getattr(result, 'execution_time', 'N/A')} 秒")
            print(f"📷 生成图片大小: {len(result.image_data) if result.image_data else 0} bytes")
            return True
        else:
            print(f"❌ LoadImage工作流执行失败: {getattr(result, 'error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试LoadImage工作流异常: {e}")
        return False

async def test_simple_workflow_submission():
    """测试简单工作流提交"""
    print("\n🔧 测试简单工作流提交:")
    
    try:
        import aiohttp
        
        # 加载简单测试工作流
        workflow_file = "workflows/flux_simple_test.json"
        if not os.path.exists(workflow_file):
            print(f"❌ 工作流文件不存在: {workflow_file}")
            return False
        
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        print(f"✅ 成功加载工作流，包含 {len(workflow_data)} 个节点")
        
        # 检查LoadImage节点
        loadimage_nodes = []
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and 'class_type' in node_data:
                if node_data['class_type'] == 'LoadImage':
                    loadimage_nodes.append(node_id)
                    print(f"🔍 找到LoadImage节点: {node_id}")
        
        if not loadimage_nodes:
            print("❌ 未找到LoadImage节点")
            return False
        
        # 保存测试图片并更新工作流
        from pkg.workers.flux.image_file_manager import get_image_file_manager
        image_manager = get_image_file_manager()
        
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        file_path = image_manager.save_image_to_local(test_image_data, "test_control.png")
        print(f"✅ 测试图片保存成功: {file_path}")
        
        # 更新LoadImage节点的图片路径
        for node_id in loadimage_nodes:
            workflow_data[node_id]["inputs"]["image"] = file_path
            print(f"✅ 更新节点 {node_id} 的图片路径: {file_path}")
        
        # 提交到ComfyUI
        api_url = "http://localhost:8188"
        prompt_data = {"prompt": workflow_data}
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{api_url}/prompt", json=prompt_data) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result.get("prompt_id")
                    print(f"✅ 工作流提交成功: {prompt_id}")
                    
                    # 等待执行完成
                    print("⏳ 等待工作流执行...")
                    for i in range(30):  # 最多等待30秒
                        await asyncio.sleep(1)
                        
                        # 检查状态
                        async with session.get(f"{api_url}/history/{prompt_id}") as status_response:
                            if status_response.status == 200:
                                history_data = await status_response.json()
                                if prompt_id in history_data:
                                    prompt_data = history_data[prompt_id]
                                    if 'outputs' in prompt_data:
                                        print("✅ 工作流执行完成")
                                        return True
                    
                    print("⏰ 工作流执行超时")
                    return False
                else:
                    error_text = await response.text()
                    print(f"❌ 提交工作流失败: {response.status}, {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 测试简单工作流提交异常: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 LoadImage工作流测试工具")
    print("=" * 60)
    
    # 1. 测试图片文件管理器
    image_manager = await test_image_file_manager()
    if not image_manager:
        print("❌ 图片文件管理器测试失败")
        return
    
    # 2. 测试简单工作流提交
    success = await test_simple_workflow_submission()
    if not success:
        print("❌ 简单工作流提交测试失败")
        return
    
    # 3. 测试完整LoadImage工作流（可选）
    print("\n" + "=" * 60)
    print("🎯 基础测试完成，LoadImage节点工作正常！")
    
    # 如果需要测试完整工作流，取消下面的注释
    # success = await test_loadimage_workflow()
    # if success:
    #     print("\n🎉 所有测试通过！")
    # else:
    #     print("\n❌ 完整工作流测试失败")

if __name__ == "__main__":
    asyncio.run(main()) 