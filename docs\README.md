# LangBot 文档中心

## 📚 文档目录

### 架构设计
- [系统设计](./architecture/SYSTEM_DESIGN.md) - 系统整体架构设计
- [统一路由系统设计规划](./architecture/PRD-********-UnifiedRoutingSystem.md) - 统一路由系统设计
- [统一路由系统清理完成报告](./architecture/UNIFIED_ROUTING_CLEANUP_COMPLETED.md) - 路由系统清理成果
- [LLM分析代码清理完成报告](./architecture/LLM_ANALYSIS_CLEANUP_COMPLETED.md) - LLM分析代码清理成果
- [ComfyUI Agent重构方案](./architecture/COMFYUI_AGENT_REFACTORING.md) - 重构技术方案

### 集成指南
- [ComfyUI集成指南](./COMFYUI_INTEGRATION.md) - ComfyUI集成说明
- [二次开发集成指南](./二次开发集成指南.md) - 二次开发说明

### 部署运维
- [开发指南](./deployment/DEV_GUIDE.md) - 开发环境搭建
- [服务器重启指南](./deployment/SERVER_RESTART_GUIDE.md) - 服务重启说明
- [管理员同步指南](./admin-sync-guide.md) - 管理员功能说明

### 规划文档
- [开发计划](./planning/DEVELOPMENT_PLAN.md) - 开发计划
- [开发路线图](./planning/DEVELOPMENT_ROADMAP.md) - 路线图

### API文档
- [API规范 V1](./api-specs/API_V1.md) - API接口文档

### 故障排除
- [快速参考](./troubleshooting/quick-reference.md) - 常见问题
- [网络问题排除](./troubleshooting/langbot-wechatpad-network-issue.md) - 网络故障

---

## 🔄 最近更新

### 2024-12-20
- 🧹 **LLM分析代码清理完成**
  - 删除了冗余的LLM分析器文件
  - 统一了所有LLM分析功能到统一路由系统
  - 简化了模块间依赖关系
  - 详见: [LLM分析代码清理完成报告](./architecture/LLM_ANALYSIS_CLEANUP_COMPLETED.md)

### 2024-12-20
- 🚀 **统一路由系统清理完成**
  - 删除了旧的路由器文件
  - 统一了路由逻辑到新的路由系统
  - 简化了代码结构
  - 详见: [统一路由系统清理完成报告](./architecture/UNIFIED_ROUTING_CLEANUP_COMPLETED.md)

### 2024-01-07
- 📄 新增 [ComfyUI Agent重构方案](./architecture/COMFYUI_AGENT_REFACTORING.md)
- 🏗️ 制定了模块化重构计划，将1500+行的巨型文件拆分

---

**维护人员**: AI Assistant  
**最后更新**: 2024-12-20 