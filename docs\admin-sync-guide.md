# 管理员消息同步功能使用指南

## 概述

管理员消息同步功能允许您控制是否将用户的图片生成请求和结果同步到指定的管理员微信账号。默认情况下，此功能是**关闭**的，确保您的个人微信不会被打扰。

## 功能特性

- ✅ **流水线界面控制**: 直接在ComfyUI Agent配置中开关
- ✅ **一键开关**: 开启即可接收与用户相同的消息
- ✅ **即时生效**: 无需重启服务
- ✅ **安全设计**: 需要明确配置管理员微信ID

## 同步内容说明

当启用管理员同步后，您将接收到以下两种消息：

### 1. 用户请求通知
当用户发送图片生成请求时，向管理员发送通知：

```
[图片生成请求]
群组: AI创作交流群
用户: 张三
提示词: 可爱的小猫咪坐在花园里
```

### 2. 生成结果图片
将生成的图片发送到管理员微信：

```
[生成完成]
群组: AI创作交流群
用户: 张三
提示词: 可爱的小猫咪坐在花园里

[图片]
```

**这样您就能看到与用户完全相同的消息，了解系统的使用情况。**

## 配置方法

### 在管理界面进行配置（推荐）

#### 步骤1: 配置机器人
1. **进入机器人管理页面**
   - 登录LangBot管理后台
   - 进入"机器人"管理页面
   - 配置机器人的微信连接信息（包含您的微信ID）

#### 步骤2: 配置流水线
2. **进入流水线配置页面**
   - 创建或编辑包含ComfyUI Agent的流水线
   - 在AI部分找到"ComfyUI图片生成Agent"
   - 配置基本参数：
     - **API地址**: `http://localhost:8188`
     - **超时时间**: `120` 秒
     - **工作流路径**: `workflows`  
     - **默认工作流**: `default_workflow.json`
     - **管理员同步**: ☑ 开启 (默认关闭)

#### 步骤3: 绑定机器人和流水线
3. **在机器人管理页面**
   - 将配置好的流水线绑定到机器人
   - 这样机器人的微信ID就自动关联到流水线的同步功能

![配置界面示例]

**配置说明:**
- 机器人配置提供微信ID信息
- 流水线配置提供同步开关控制
- 开启同步后，您的微信将收到用户的图片生成请求和结果
- 关闭后完全安静，不受任何打扰
- 配置立即生效，无需重启服务

## 使用场景建议

### 🔧 开发调试期间
配置步骤：
- 在机器人管理页面配置好微信连接
- 在流水线中开启"管理员同步"开关
- 绑定机器人和流水线
- 这样可以实时监控系统运行情况

### 🎯 生产环境使用
根据需要灵活切换：
- **需要监控时**: 开启同步，了解用户使用情况
- **专心工作时**: 关闭同步，避免消息打扰
- **一键切换**: 随时在界面中开关，立即生效

### 🚫 完全关闭（默认）
初始状态下同步功能关闭，确保不会打扰您

## 安全注意事项

1. **微信ID隐私**: 管理员微信ID会保存在配置文件中，请确保配置文件安全
2. **内容审查**: 同步的消息包含用户请求内容，请注意内容合规性
3. **存储空间**: 如果启用图片同步，管理员微信会接收所有生成的图片
4. **频率控制**: 高频使用时可能产生大量通知消息

## 故障排除

### 问题1: 没有收到同步消息
1. 检查流水线配置中"管理员同步"开关是否开启
2. 确认管理员微信ID正确填写
3. 检查ComfyUI Agent是否已添加到流水线
4. 查看日志中是否有错误信息

### 问题2: 配置更改不生效
- 流水线配置更改通常立即生效，无需重启
- 如果仍有问题，可以重启LangBot服务

## 配置示例

### 机器人配置 (机器人管理页面)
```yaml
# 机器人配置中的微信连接信息
wechatpad:
  wxid: "wxid_admin123"              # 您的微信ID
  # 其他微信连接配置...
```

### 流水线配置 (流水线配置页面)
```yaml
ai:
  comfyui-agent:
    api-url: "http://localhost:8188"
    timeout: 120
    workflow-path: "workflows"
    default-workflow: "default_workflow.json"
    admin-sync-enabled: true          # 开启管理员同步
```

### 绑定关系
- 机器人绑定流水线后，系统自动使用机器人的微信ID作为管理员同步目标
- 流水线中的同步开关控制是否启用此功能

---

通过这个简化的管理员同步功能，您只需要在流水线界面一键开关，就能控制是否接收用户生成图片的相关信息。开启时，您将看到与用户完全相同的消息；关闭时，保持完全安静，不受任何打扰。 