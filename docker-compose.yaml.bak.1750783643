version: "3"

services:
  langbot:
    image: docker.langbot.app/langbot-public/rockchin/langbot:latest
    container_name: langbot
    volumes:
      # 数据目录
      - ./data:/app/data
      # 插件目录
      - ./plugins:/app/plugins
      # 二次开发代码挂载（覆盖容器内的pkg目录）
      - ./pkg:/app/pkg
      # 配置文件挂载
      - ./config:/app/config
      # 工作流文件
      - ./workflows:/app/workflows
      # 模板文件
      - ./templates:/app/templates
      # 资源文件
      - ./res:/app/res
    restart: on-failure
    environment:
      - TZ=Asia/Shanghai
      # ComfyUI Firebase认证token（用于Kontext工作流）
      - "API_KEY_COMFY_ORG=eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XQftlRB07kSesK9jLXpTkG5wbUZOAj6uZhsUNd9NsOAIPi4-36Uhcm-6byq-8gyAJOD7aHJE3jqZQwqG6ZcaplqGK1blPaOD0495xhcJDvk1aSPca46d0Adc8KFCFXVCHFce4bG1a2yaqd0-5A8uyQKTX4CbytJzsc4jFt9ao37Q2y-nTCZFs0Cj7ZAkhIKCuc5dsEYAtnt91qR73rXJBIc9KbzcxQJ57R2aa7XjV09VXgw7HNGkJ2_6vZ20LoFHcoAfLKPKOgmmn0Sk2RU_10nVZldgSaHRHiVmNokl5ngYDz1TQ7OYSCZxprL3klnBy6AgEusQlDYxWtfTs6M4Zw"
      - "AUTH_TOKEN_COMFY_ORG=eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XQftlRB07kSesK9jLXpTkG5wbUZOAj6uZhsUNd9NsOAIPi4-36Uhcm-6byq-8gyAJOD7aHJE3jqZQwqG6ZcaplqGK1blPaOD0495xhcJDvk1aSPca46d0Adc8KFCFXVCHFce4bG1a2yaqd0-5A8uyQKTX4CbytJzsc4jFt9ao37Q2y-nTCZFs0Cj7ZAkhIKCuc5dsEYAtnt91qR73rXJBIc9KbzcxQJ57R2aa7XjV09VXgw7HNGkJ2_6vZ20LoFHcoAfLKPKOgmmn0Sk2RU_10nVZldgSaHRHiVmNokl5ngYDz1TQ7OYSCZxprL3klnBy6AgEusQlDYxWtfTs6M4Zw"
    network_mode: host  # 使用host网络模式
    # 根据具体环境配置网络
