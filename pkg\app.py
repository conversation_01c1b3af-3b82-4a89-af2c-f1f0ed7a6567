# 在适当位置添加以下代码
from pkg.services.wechat_comfyui_service import WeChatComfyUIService

# 在Application类中添加
def init_wechat_comfyui_service(self):
    """初始化微信-ComfyUI服务"""
    # 获取配置
    wechat_comfyui_config = self.config.get("wechat_comfyui", {})
    
    # 如果未启用，则返回
    if not wechat_comfyui_config.get("enabled", False):
        return
        
    # 创建服务实例
    self.wechat_comfyui_service = WeChatComfyUIService(
        wechat_comfyui_config,
        self.llm_router,
        self.logger
    )
    
    # 注册群消息监听器
    for adapter_name in wechat_comfyui_config.get("adapters", ["WeChatPad", "gewechat"]):
        if adapter_name in self.platform_adapters:
            adapter_instance = self.platform_adapters[adapter_name]
            adapter_instance.register_listener(
                platform_events.GroupMessage,
                self.wechat_comfyui_service.handle_group_message
            )
            self.logger.info(f"已为 {adapter_name} 注册微信-ComfyUI服务")