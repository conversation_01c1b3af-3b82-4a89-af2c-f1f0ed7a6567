#!/bin/bash

# 双触发条件修复回滚脚本
# 用于撤销已应用的修复

set -e

echo "🔄 开始回滚双触发条件修复..."

# 检查补丁文件是否存在
if [ ! -f "fix-trigger-conditions.patch" ]; then
    echo "❌ 补丁文件 fix-trigger-conditions.patch 不存在"
    exit 1
fi

# 备份当前文件
echo "📋 备份当前文件..."
backup_dir="rollback_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

# 备份要修改的文件
if [ -f "pkg/pipeline/resprule/rules/atbot_with_prefix.py" ]; then
    cp "pkg/pipeline/resprule/rules/atbot_with_prefix.py" "$backup_dir/"
fi
if [ -f "pkg/provider/runners/comfyui_agent.py" ]; then
    cp "pkg/provider/runners/comfyui_agent.py" "$backup_dir/"
fi
if [ -f "pkg/core/session/models.py" ]; then
    cp "pkg/core/session/models.py" "$backup_dir/"
fi

echo "✅ 文件已备份到: $backup_dir"

# 回滚补丁
echo "🔄 回滚修复补丁..."
if git apply --reverse --check fix-trigger-conditions.patch 2>/dev/null; then
    git apply --reverse fix-trigger-conditions.patch
    echo "✅ 补丁回滚成功！"
    echo ""
    echo "🔄 已撤销的修改："
    echo "  - 双触发条件修复已撤销"
    echo "  - ComfyUI Agent 恢复到原始状态"
    echo "  - 会话管理功能恢复到原始状态"
    echo ""
    echo "⚠️  注意：现在可能重新出现原来的问题"
else
    echo "❌ 补丁无法回滚，可能存在冲突"
    echo "💡 请手动检查修改或联系技术支持"
    exit 1
fi 