#!/usr/bin/env python3
"""
简化的flux_controlnet测试脚本
"""

import json
import os

def test_workflow_loading():
    """测试工作流加载"""
    print("📋 测试工作流加载:")
    
    try:
        workflow_file = "workflows/flux_controlnet.json"
        if not os.path.exists(workflow_file):
            print(f"❌ 工作流文件不存在: {workflow_file}")
            return False
        
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        print(f"✅ 成功加载工作流，包含 {len(workflow_data)} 个节点")
        
        # 检查LoadImage节点
        loadimage_nodes = []
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and 'class_type' in node_data:
                if node_data['class_type'] == 'LoadImage':
                    loadimage_nodes.append((node_id, node_data.get('_meta', {}).get('title', '')))
                    print(f"🔍 找到LoadImage节点: {node_id} ({node_data.get('_meta', {}).get('title', '')})")
        
        if not loadimage_nodes:
            print("❌ 未找到LoadImage节点")
            return False
        
        # 检查节点210
        node_210 = workflow_data.get("210", {})
        if node_210:
            class_type = node_210.get("class_type", "")
            title = node_210.get("_meta", {}).get("title", "")
            inputs = node_210.get("inputs", {})
            
            print(f"✅ 节点210信息:")
            print(f"  - 类型: {class_type}")
            print(f"  - 标题: {title}")
            print(f"  - 输入: {inputs}")
            
            if class_type == "LoadImage" and title == "controlnet_image_input":
                print(f"✅ 节点210修改成功")
                return True
            else:
                print(f"❌ 节点210修改失败")
                return False
        else:
            print(f"❌ 找不到节点210")
            return False
            
    except Exception as e:
        print(f"❌ 测试工作流加载异常: {e}")
        return False

def test_image_file_manager():
    """测试图片文件管理器"""
    print("\n🧪 测试图片文件管理器:")
    
    try:
        from pkg.workers.flux.image_file_manager import get_image_file_manager
        
        # 获取图片文件管理器
        image_manager = get_image_file_manager("/tmp/comfyui_test_uploads")
        print(f"✅ 成功获取图片文件管理器")
        print(f"📁 上传目录: {image_manager.upload_dir}")
        
        # 测试保存图片
        test_image_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        
        file_path = image_manager.save_image_to_local(test_image_data, "test_control.png")
        print(f"✅ 图片保存成功: {file_path}")
        
        # 检查文件信息
        file_info = image_manager.get_file_info(file_path)
        if file_info and file_info['exists']:
            print(f"✅ 文件信息验证成功: 大小 {file_info['size']} bytes")
        else:
            print(f"❌ 文件信息验证失败")
        
        return image_manager
        
    except Exception as e:
        print(f"❌ 测试图片文件管理器失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 flux_controlnet LoadImage简化测试")
    print("=" * 60)
    
    # 1. 测试工作流加载
    success = test_workflow_loading()
    if not success:
        print("❌ 工作流加载测试失败")
        return
    
    # 2. 测试图片文件管理器
    image_manager = test_image_file_manager()
    if not image_manager:
        print("❌ 图片文件管理器测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎯 基础测试完成，LoadImage节点修改成功！")

if __name__ == "__main__":
    main() 