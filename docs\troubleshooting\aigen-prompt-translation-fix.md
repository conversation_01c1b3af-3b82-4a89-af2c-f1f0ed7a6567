# AIGEN提示词翻译修复

## 📋 问题信息

- **发现时间**: 2025-01-07
- **问题类型**: 提示词翻译失败
- **影响范围**: AIGEN纯文生图工作流
- **严重程度**: 高（用户输入被忽略）

## 🔍 问题描述

### 问题现象
用户使用AIGEN工作流进行纯文生图时，输入的中文提示词没有被翻译成英文，导致：
- 系统使用了默认的英文提示词
- 生成的图片与用户要求不符
- 用户体验严重受损

### 具体表现
```
用户输入: "aigen 生成一只小黑猫，金色眼睛"
期望结果: 生成一只小黑猫，金色眼睛
实际结果: 生成了默认提示词的图片（动漫少女）
```

### 日志特征
```
📊 LLM智能路由结果:
   工作流子类型: aigen_text_only
   置信度: high
   推理: 无图片输入，选择纯文生图工作流
   建议提示词: （空白）
   工作流文件: flux_default.json
```

## 🔧 根本原因

### 代码层面
在`pkg/core/workflow/unified_routing_system.py`的`_route_aigen_pipeline`方法中，当用户没有上传图片时，系统直接返回了一个简单的路由结果，没有调用LLM进行提示词翻译。

```python
# 原始代码（有问题）
if not has_images or image_count == 0:
    return _Level2RoutingResult(
        workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
        confidence=RoutingConfidence.HIGH,
        reasoning="无图片输入，选择纯文生图工作流",
        processing_time_ms=(time.time() - start_time) * 1000,
        workflow_file="flux_default.json"
        # 注意：这里没有 suggested_prompt 字段！
    )
```

### 设计层面
违反了两级路由架构的设计原则：
- 第二级路由应该进行完整的LLM分析
- 所有用户输入都应该被正确处理和翻译

## 💡 修复方案

### 1. 代码修复
修改`_route_aigen_pipeline`方法，让纯文生图情况也调用LLM进行提示词翻译：

```python
# 修复后的代码
if not has_images or image_count == 0:
    # 尝试使用LLM分析和翻译提示词
    llm_result = await self._route_with_llm(
        user_text, False, 0, query, WorkflowType.AIGEN
    )
    
    if llm_result and llm_result.suggested_prompt:
        # LLM成功翻译了提示词
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
            confidence=RoutingConfidence.HIGH,
            reasoning="无图片输入，选择纯文生图工作流，LLM翻译提示词",
            processing_time_ms=(time.time() - start_time) * 1000,
            suggested_prompt=llm_result.suggested_prompt,
            workflow_file="flux_default.json"
        )
    else:
        # LLM分析失败，使用基础的提示词处理
        basic_prompt = self._extract_basic_prompt(user_text)
        return _Level2RoutingResult(
            workflow_subtype=WorkflowSubType.AIGEN_TEXT_ONLY,
            confidence=RoutingConfidence.MEDIUM,
            reasoning="无图片输入，选择纯文生图工作流，基础提示词处理",
            processing_time_ms=(time.time() - start_time) * 1000,
            suggested_prompt=basic_prompt,
            workflow_file="flux_default.json"
        )
```

### 2. 添加辅助方法
新增`_extract_basic_prompt`方法作为LLM翻译失败时的回退方案：

```python
def _extract_basic_prompt(self, user_text: str) -> str:
    """从用户输入中提取基础的提示词"""
    # 移除触发词前缀
    text = user_text.strip()
    for prefix in ['aigen ', 'kontext ', 'kontext_api ']:
        if text.lower().startswith(prefix):
            text = text[len(prefix):].strip()
            break
    
    # 如果提示词为空，使用默认值
    if not text:
        return "a beautiful artwork"
    
    # 如果是中文，尝试简单的翻译
    if any('\u4e00' <= char <= '\u9fff' for char in text):
        # 包含中文字符，做简单的关键词映射
        text = text.replace('小黑猫', 'small black cat')
        text = text.replace('金色眼睛', 'golden eyes')
        text = text.replace('生成', 'generate')
        text = text.replace('一只', 'a')
        text = text.replace('，', ', ')
        text = text.replace('。', '.')
    
    return text
```

## 🧪 测试用例

### 测试场景1：纯文生图（中文提示词）
```
输入: "aigen 生成一只小黑猫，金色眼睛"
期望: 
- 工作流子类型: aigen_text_only
- 建议提示词: 包含"small black cat"和"golden eyes"
- 生成小黑猫图片
```

### 测试场景2：纯文生图（英文提示词）
```
输入: "aigen a beautiful landscape"
期望:
- 工作流子类型: aigen_text_only
- 建议提示词: 包含"beautiful landscape"
- 生成风景图片
```

### 测试场景3：纯文生图（空提示词）
```
输入: "aigen"
期望:
- 工作流子类型: aigen_text_only
- 建议提示词: "a beautiful artwork"
- 生成默认图片
```

## 📊 验证方法

### 1. 日志验证
检查以下日志是否正确输出：
```
🎯 开始LLM智能路由分析会话数据...
   文本: aigen 生成一只小黑猫，金色眼睛
   图片数量: 0
📊 LLM智能路由结果:
   工作流子类型: aigen_text_only
   置信度: high
   推理: 无图片输入，选择纯文生图工作流，LLM翻译提示词
   建议提示词: small black cat with golden eyes
   工作流文件: flux_default.json
```

### 2. 工作流数据验证
使用工作流监控器检查发送给ComfyUI的数据是否包含翻译后的提示词。

### 3. 生成结果验证
确认生成的图片符合用户的中文提示词要求。

## 🔄 部署步骤

1. 停止现有容器：`docker stop langbot && docker rm langbot`
2. 重新启动容器：`./langbot-manager.sh start`
3. 验证容器状态：`./langbot-manager.sh status`
4. 进行测试验证

## 📝 相关文档

- [两级路由架构设计](../architecture/TWO_LEVEL_ROUTING_ARCHITECTURE.md)
- [统一路由系统PRD](../architecture/PRD-********-UnifiedRoutingSystem.md)
- [测试指南](../testing/TESTING_GUIDE.md)

## 🏷️ 标签

- 修复类型：功能修复
- 影响组件：统一路由系统
- 测试级别：核心功能
- 优先级：高 