# 测试文件备份说明

## 备份原因
这些测试文件在重构后与新接口不兼容，暂时备份以避免影响核心测试运行。

## 已备份的测试文件

### 1. 消息处理器测试
- `test_message_processor.py` - MessageProcessor接口已重构，老测试用例与新接口不兼容

### 2. Kontext相关测试
- `test_comfyui_auth.py` - ComfyUIAuth类已迁移或重构
- `test_image_processor.py` - ImageProcessor接口已变更
- `test_prompt_optimizer.py` - PromptOptimizer类已迁移
- `test_session_manager.py` - SessionManager接口已重构
- `test_workflow_executor.py` - 工作流执行器已重构

### 3. 其他测试
- `test_kontext_auth.py` - Kontext认证测试需要异步支持
- `client_test.py` - Dify客户端测试需要异步支持
- `test_image_utils.py` - 图片工具已迁移到core模块

## 恢复建议
1. 根据新接口重新编写测试用例
2. 或在新版本稳定后逐步恢复并修正
3. 优先恢复核心功能的测试用例

## 备份时间
2024-12-19 - 重构清理阶段 