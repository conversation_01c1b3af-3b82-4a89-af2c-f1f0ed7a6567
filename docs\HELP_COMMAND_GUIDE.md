# Help命令功能说明

## 功能概述

用户现在可以通过多种方式获取图片生成服务的用户指南，无需手动查找文档。

## 支持的触发方式

用户可以通过以下任意一种方式触发帮助功能：

- `!help` - 感叹号前缀
- `/help` - 斜杠前缀（英文）
- `/帮助` - 斜杠前缀（中文）

## 功能特性

### 1. 智能内容读取
- 系统会自动读取 `docs/KONTEXT_USER_GUIDE.md` 文件
- 如果文件存在，会显示完整的用户指南内容
- 如果文件不存在，会显示内置的简化指南

### 2. Markdown格式转换
- 自动将Markdown格式转换为纯文本
- 保留标题结构，使用等号下划线突出显示
- 将列表标记转换为更易读的格式
- 移除代码块标记，保留纯文本内容

### 3. 错误处理
- 文件读取失败时自动回退到简化指南
- 记录错误日志便于调试
- 确保用户始终能看到帮助内容

## 技术实现

### 修改的文件

1. **`pkg/command/operators/help.py`**
   - 扩展了help命令的功能
   - 添加了Markdown到文本的转换功能
   - 实现了文件读取和错误处理

2. **`templates/legacy/command.json`**
   - 添加了斜杠前缀 `/` 支持
   - 现在支持 `!`、`！`、`/` 三种前缀

### 核心功能

```python
# Markdown转换功能
def _convert_markdown_to_text(self, markdown_content: str) -> str:
    """将Markdown格式转换为纯文本"""
    # 处理标题、列表、代码块等格式
    # 返回纯文本内容

# 文件读取功能
guide_path = os.path.join(os.getcwd(), 'docs', 'KONTEXT_USER_GUIDE.md')
if os.path.exists(guide_path):
    # 读取并转换文件内容
else:
    # 返回简化指南
```

## 用户体验

### 使用示例

```
用户: /help
机器人: [返回完整的图片生成用户指南]

用户: /帮助
机器人: [返回完整的图片生成用户指南]

用户: !help
机器人: [返回完整的图片生成用户指南]
```

### 内容预览

帮助内容包含：
- 🎨 三种触发指令说明（aigen、kontext、kontext api）
- 🎯 详细的使用示例
- 💡 使用技巧和注意事项
- 🔧 工作流类型说明

## 维护说明

### 更新用户指南
1. 修改 `docs/KONTEXT_USER_GUIDE.md` 文件
2. 重启LangBot服务
3. 用户发送 `/help` 即可看到更新后的内容

### 添加新的命令前缀
1. 编辑 `templates/legacy/command.json`
2. 在 `command-prefix` 数组中添加新前缀
3. 重启LangBot服务

## 故障排除

### 常见问题

1. **帮助内容显示不完整**
   - 检查 `docs/KONTEXT_USER_GUIDE.md` 文件是否存在
   - 确认文件编码为UTF-8

2. **斜杠命令不响应**
   - 确认 `templates/legacy/command.json` 中包含 `/` 前缀
   - 重启LangBot服务

3. **Markdown格式显示异常**
   - 检查用户指南文件的Markdown语法
   - 查看日志中的错误信息

### 日志调试

系统会记录以下日志信息：
- 文件读取成功/失败
- Markdown转换过程
- 错误详情

## 总结

这个功能大大提升了用户体验，让用户能够：
- 快速获取图片生成服务的完整指南
- 通过多种熟悉的方式触发帮助
- 获得格式良好的纯文本内容
- 在文件缺失时仍能看到基本说明

这是一个用户友好的功能增强，符合现代聊天机器人的交互习惯。 