from __future__ import annotations

import base64
import re
import typing
import aiohttp
from typing import Dict, Any, Optional, List

from ...core import app, entities as core_entities
from .. import entities as llm_entities
from ...platform import types as platform_types


class BaseAgent:
    """ComfyUI Agent基础类，提供公共功能"""
    
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.ap = ap
        self.pipeline_config = pipeline_config
        
        # 获取 ComfyUI 配置
        comfyui_config = self.pipeline_config['ai']['comfyui-agent']
        self.api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        self.ws_url = self.api_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.timeout = comfyui_config.get('timeout', 180)
        
        # 设置日志
        self._setup_logging()
        
    def _setup_logging(self) -> None:
        """设置日志配置"""
        if not hasattr(self, 'logger'):
            self.logger = self.ap.logger
    
    def _handle_error(self, error: Exception, context: str) -> dict:
        """统一错误处理"""
        error_msg = f"{context}: {str(error)}"
        self.logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'context': context
        }
    
    async def _optimize_prompt_with_llm(self, prompt: str, context: str, query: core_entities.Query) -> str:
        """使用LLM优化提示词的基础方法"""
        try:
            # 构建系统提示
            system_prompt = f"""你是一个专业的AI绘图提示词优化师。请将用户的{context}描述转换为更加精确、详细的英文提示词。

要求：
1. 保持原意不变
2. 增加艺术风格和技术细节
3. 优化语法和表达
4. 控制在200字以内
5. 只返回优化后的英文提示词，不要其他内容

用户原始描述：{prompt}"""

            # 尝试使用LLM进行优化
            try:
                # 从pipeline配置中获取模型UUID
                if query.pipeline_config:
                    model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
                    
                    if model_uuid:
                        # 找到对应的RuntimeLLMModel
                        for model in self.ap.model_mgr.llm_models:
                            if model.model_entity.uuid == model_uuid:
                                # 创建临时的对话消息
                                messages = [
                                    llm_entities.Message(role='system', content=system_prompt),
                                    llm_entities.Message(role='user', content=prompt)
                                ]
                                
                                # 调用LLM
                                result = await model.requester.invoke_llm(
                                    query,
                                    model,
                                    messages,
                                    [],  # 不需要工具调用
                                    extra_args={},
                                )
                                
                                # 获取响应内容
                                response_text = ""
                                if hasattr(result, 'content') and result.content:
                                    if isinstance(result.content, list):
                                        for element in result.content:
                                            if hasattr(element, 'text') and element.text:
                                                response_text += element.text
                                    elif isinstance(result.content, str):
                                        response_text = result.content
                                    else:
                                        response_text = str(result.content)
                                
                                if response_text and response_text.strip():
                                    return response_text.strip()
                                break
            except Exception as llm_error:
                self.logger.warning(f"LLM优化过程出错: {str(llm_error)}")
                    
        except Exception as e:
            self.logger.warning(f"LLM提示词优化失败: {str(e)}")
        
        # 如果优化失败，返回原始提示词
        return prompt
    
    async def _validate_image_data(self, image_data: bytes) -> bool:
        """验证图片数据的有效性"""
        try:
            if not image_data:
                return False
                
            # 检查图片大小（不能太小或太大）
            if len(image_data) < 100:  # 太小
                return False
            if len(image_data) > 50 * 1024 * 1024:  # 大于50MB
                return False
                
            # 检查图片魔数（简单验证）
            if image_data[:2] == b'\xff\xd8':  # JPEG
                return True
            elif image_data[:8] == b'\x89PNG\r\n\x1a\n':  # PNG
                return True
            elif image_data[:6] in (b'GIF87a', b'GIF89a'):  # GIF
                return True
            elif image_data[:4] == b'RIFF' and image_data[8:12] == b'WEBP':  # WebP
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"图片数据验证失败: {str(e)}")
            return False
    
    async def _extract_images_from_user_message(self, query: core_entities.Query) -> List[bytes]:
        """从用户消息中提取图片数据"""
        images = []
        
        try:
            self.logger.info(f"开始提取图片，user_message类型: {type(query.user_message)}")
            
            if not query.user_message or not query.user_message.content:
                self.logger.info("用户消息或内容为空")
                return images
                
            self.logger.info(f"user_message.content类型: {type(query.user_message.content)}")
            
            if isinstance(query.user_message.content, list):
                self.logger.info(f"content列表长度: {len(query.user_message.content)}")
                
                for i, content in enumerate(query.user_message.content):
                    self.logger.info(f"content[{i}]类型: {type(content)}, 属性: {dir(content)}")
                    
                    if hasattr(content, 'type'):
                        self.logger.info(f"content[{i}].type: {content.type}")
                        
                        if content.type == 'image_base64':
                            self.logger.info(f"找到图片类型content，属性: {dir(content)}")
                            
                            if hasattr(content, 'image_base64') and content.image_base64:
                                try:
                                    # 获取base64数据
                                    base64_data = content.image_base64
                                    self.logger.info(f"原始base64长度: {len(base64_data)}")
                                    
                                    # 移除data URL前缀（如果存在）
                                    if base64_data.startswith('data:'):
                                        base64_data = base64_data.split(',', 1)[1]
                                    self.logger.info(f"去掉data URL前缀后长度: {len(base64_data)}")
                                    
                                    # 清理base64数据
                                    base64_data = re.sub(r'[^A-Za-z0-9+/=]', '', base64_data)
                                    
                                    # 解码base64
                                    image_bytes = base64.b64decode(base64_data)
                                    self.logger.info(f"解码base64成功，大小: {len(image_bytes)}")
                                    
                                    # 验证图片数据
                                    if await self._validate_image_data(image_bytes):
                                        images.append(image_bytes)
                                        self.logger.info(f"图片验证成功，添加到列表")
                                    else:
                                        self.logger.warning(f"图片验证失败")
                                        
                                except Exception as decode_error:
                                    self.logger.error(f"解码base64图片失败: {str(decode_error)}")
            
            self.logger.info(f"最终提取到 {len(images)} 张图片")
            return images
            
        except Exception as e:
            self.logger.error(f"提取图片时出错: {str(e)}")
            return images
    
    def _get_user_id(self, query: core_entities.Query) -> str:
        """获取用户ID"""
        try:
            message_event = getattr(query, 'message_event', None)
            if message_event:
                sender = getattr(message_event, 'sender', None)
                if sender:
                    if hasattr(sender, 'member_name') and sender.member_name:
                        return str(sender.member_name)
                    elif hasattr(sender, 'nickname') and sender.nickname:
                        return str(sender.nickname)
                    elif hasattr(sender, 'user_id') and sender.user_id:
                        return str(sender.user_id)
            
            # 回退方案：使用session的launcher_id
            if hasattr(query, 'session') and query.session:
                return str(query.session.launcher_id)
            
            return "unknown_user"
        except Exception as e:
            self.logger.error(f"获取用户ID失败: {e}")
            return "unknown_user"
    
    def _get_chat_id(self, query: core_entities.Query) -> str:
        """获取聊天ID"""
        try:
            # 尝试从消息事件中获取群组ID
            message_event = getattr(query, 'message_event', None)
            if message_event:
                sender = getattr(message_event, 'sender', None)
                if sender:
                    group = getattr(sender, 'group', None)
                    if group and hasattr(group, 'id'):
                        return str(group.id)
            
            # 回退方案：使用session信息
            if hasattr(query, 'session') and query.session:
                return f"{query.session.launcher_type.value}_{query.session.launcher_id}"
            
            return "unknown_chat"
        except Exception as e:
            self.logger.error(f"获取聊天ID失败: {e}")
            return "unknown_chat"
    
    def _extract_user_text(self, query: core_entities.Query) -> str:
        """提取用户文本"""
        user_text = ""
        if query.user_message and query.user_message.content:
            if isinstance(query.user_message.content, str):
                user_text = query.user_message.content
            elif isinstance(query.user_message.content, list):
                for content in query.user_message.content:
                    if hasattr(content, 'text') and content.text and hasattr(content, 'type') and content.type == 'text':
                        user_text += str(content.text)
        return user_text.strip() 