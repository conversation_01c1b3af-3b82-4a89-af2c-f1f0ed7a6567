apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: ppio-chat-completions
  label:
    en_US: ppio 
    zh_Hans: 派欧云
  icon: ppio.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.ppinfra.com/v3/openai"
    - name: args
      label:
        en_US: Args
        zh_Hans: 附加参数
      type: object
      required: true
      default: {}
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: int
      required: true
      default: 120
execution:
  python:
    path: ./ppiochatcmpl.py
    attr: PPIOChatCompletions