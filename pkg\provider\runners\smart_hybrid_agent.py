"""
智能混合Agent
基于智能路由系统，自动分析用户意图和图像内容，选择最适合的工作流
"""
# pyright: reportGeneralTypeIssues=false

from __future__ import annotations
import typing
from typing import cast, AsyncGenerator
from .. import runner
from ...core import app, entities as core_entities
from .. import entities as llm_entities
from .unified_routing_mixin_v2 import UnifiedRoutingMixinV2
# 避免循环导入，将intelligent_router的导入移到函数内部
# from ...workers.intelligent_router import intelligent_router
# from .unified_agent import UnifiedAgent


@runner.runner_class('smart-hybrid-agent')
class SmartHybridAgentRunner(runner.RequestRunner, UnifiedRoutingMixinV2):
    """智能混合Agent运行器
    
    基于统一路由系统：
    - 自动分析用户意图和图像内容
    - 智能选择最适合的工作流
    - 支持多种输入模式：纯文本、图像控制、混合模式
    """

    def __init__(self, ap: app.Application, pipeline_config: dict):
        super().__init__(ap, pipeline_config)
        self.ap = ap
        self.pipeline_config = pipeline_config
        
        # 初始化统一路由系统
        UnifiedRoutingMixinV2.__init__(self)
        
        # 初始化统一智能Agent
        # self.unified_agent = UnifiedAgent(pipeline_config, ap, pipeline_config)
        self.unified_agent = None
        
        # 缓存运行器实例
        self._comfyui_runner = None

    async def run(self, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """智能路由处理"""
        try:
            # 提取用户文本和图片
            user_text = self._extract_user_text(query)
            user_images = await self._extract_user_images(query)
            user_id = self._get_user_id(query)
            
            # 🔥 使用统一路由系统判断是否是工作流请求
            routing_result = await self.route_workflow_intelligently(user_text, query, user_images)
            
            # 记录路由决策
            self.ap.logger.info(f"🎯 统一路由决策: {routing_result.workflow_type.value}")
            self.ap.logger.info(f"   路由级别: {routing_result.routing_level.value}")
            self.ap.logger.info(f"   置信度: {routing_result.confidence.value}")
            self.ap.logger.info(f"   推理: {routing_result.reasoning}")
            
            # 如果是工作流请求，使用ComfyUI Agent处理
            if routing_result.workflow_type in ['AIGEN', 'KONTEXT', 'KONTEXT_API']:
                self.ap.logger.info(f"检测到工作流请求，路由到ComfyUI Agent: {user_text[:50]}...")
                
                # 获取ComfyUI运行器
                comfyui_runner = await self._get_comfyui_runner()
                if comfyui_runner:
                    async for message in comfyui_runner.run(query):
                        yield message
                else:
                    yield llm_entities.Message(
                        role='assistant',
                        content="图片生成功能暂时不可用，请稍后再试。"
                    )
                return
            
            # 检查是否有活跃的Kontext会话
            try:
                from ...workers.kontext.kontext_session_manager import KontextSessionManager
                chat_id = self._get_chat_id(query)
                
                session_manager = KontextSessionManager()
                active_session = session_manager.get_user_session(user_id, chat_id)
                if active_session:
                    self.ap.logger.info(f"检测到活跃的Kontext会话，路由到ComfyUI Agent")
                    
                    # 获取ComfyUI运行器
                    comfyui_runner = await self._get_comfyui_runner()
                    if comfyui_runner:
                        async for message in comfyui_runner.run(query):
                            yield message
                    else:
                        yield llm_entities.Message(
                            role='assistant',
                            content="图片生成功能暂时不可用，请稍后再试。"
                        )
                    return
            except Exception as e:
                self.ap.logger.error(f"检查Kontext会话时出错: {e}")
            
            # 无工作流请求：不处理，让用户直接对话
            if user_text.strip():
                self.ap.logger.info(f"无工作流请求，不处理（传递给用户）: {user_text[:50]}...")
            else:
                self.ap.logger.info("空消息或纯图片消息，不处理")
                
        except Exception as e:
            self.ap.logger.error(f"Smart Hybrid Agent 运行出错: {str(e)}")
            yield llm_entities.Message(
                role='assistant',
                content=f"处理请求时出错: {str(e)}"
            )

    def _extract_user_text(self, query: core_entities.Query) -> str:
        """提取用户文本"""
        user_text = ""
        try:
            if query.user_message and query.user_message.content:
                if isinstance(query.user_message.content, str):
                    user_text = query.user_message.content
                elif isinstance(query.user_message.content, list):
                    for content in query.user_message.content:
                        # 安全地检查content的类型和属性
                        if hasattr(content, 'type') and content.type == 'text':
                            if hasattr(content, 'text') and content.text:
                                user_text += str(content.text)
        except Exception as e:
            self.ap.logger.error(f"提取用户文本时出错: {e}")
            
        return user_text.strip()



    def _get_user_id(self, query: core_entities.Query) -> str:
        """获取用户ID"""
        return f"{query.launcher_type.value}_{query.sender_id}"
    
    def _get_chat_id(self, query: core_entities.Query) -> str:
        """获取聊天ID"""
        return f"{query.launcher_type.value}_{query.launcher_id}"
    
    async def _extract_user_images(self, query: core_entities.Query) -> list:
        """提取用户图片"""
        # 简化实现，实际应该使用 ImageProcessor
        return []
    


    async def _get_comfyui_runner(self):
        """获取ComfyUI运行器"""
        if self._comfyui_runner is None:
            try:
                from .comfyui_agent import ComfyUIAgentRunner
                self._comfyui_runner = ComfyUIAgentRunner(self.ap, self.pipeline_config)
            except Exception as e:
                self.ap.logger.error(f"初始化ComfyUI运行器失败: {e}")
                return None
        return self._comfyui_runner 

 