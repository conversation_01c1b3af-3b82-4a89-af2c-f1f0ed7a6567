apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: qqofficial
  label:
    en_US: QQ Official API
    zh_Hans: QQ 官方 API
  description:
    en_US: QQ Official API (Webhook)
    zh_Hans: QQ 官方 API (Webhook)，请查看文档了解使用方式
  icon: qqofficial.svg
spec:
  config:
    - name: appid
      label:
        en_US: App ID
        zh_Hans: 应用ID
      type: string
      required: true
      default: ""
    - name: secret
      label:
        en_US: Secret
        zh_Hans: 密钥
      type: string
      required: true
      default: ""
    - name: port
      label:
        en_US: Port
        zh_Hans: 监听端口
      type: integer
      required: true
      default: 2284
    - name: token
      label:
        en_US: Token
        zh_Hans: 令牌
      type: string
      required: true
      default: ""
execution:
  python:
    path: ./qqofficial.py
    attr: QQOfficialAdapter
