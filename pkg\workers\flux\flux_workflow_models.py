"""
Flux 工作流数据模型
定义 Flux 专有的数据结构和枚举
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple
import time


class SeedInstruction(Enum):
    """种子指令枚举"""
    RANDOM = "random"                      # 随机种子
    USE_LAST = "use_last"                  # 使用上一次种子
    SPECIFIC = "specific"                  # 指定种子
    

class AspectRatio(Enum):
    """纵横比枚举"""
    SQUARE = "square"                      # 正方形 1:1
    LANDSCAPE = "landscape"                # 横版 16:10, 16:9等
    PORTRAIT = "portrait"                  # 竖版 9:16, 10:16等


class QualityLevel(Enum):
    """质量级别枚举"""
    FAST = "fast"                          # 快速生成 (15-20步)
    STANDARD = "standard"                  # 标准质量 (20-25步)
    HIGH = "high"                          # 高质量 (25-30步)
    ULTRA = "ultra"                        # 超高质量 (30+步)


class LoRACategory(Enum):
    """LoRA模型分类枚举"""
    DETAIL = "detail"                      # 细节增强
    STYLE = "style"                        # 风格模型
    CHARACTER = "character"                # 角色模型
    BACKGROUND = "background"              # 背景增强
    LIGHTING = "lighting"                  # 光照效果
    TEXTURE = "texture"                    # 纹理增强


@dataclass
class FluxParameters:
    """Flux 工作流参数"""
    # 基础参数
    prompt: str = ""
    negative_prompt: str = ""
    width: int = 1024
    height: int = 1024
    
    # 生成参数
    steps: int = 20
    guidance: float = 3.5
    seed: int = -1
    
    # 检测到的属性
    aspect_ratio: AspectRatio = AspectRatio.SQUARE
    quality_level: QualityLevel = QualityLevel.STANDARD
    seed_instruction: SeedInstruction = SeedInstruction.RANDOM
    
    # 元数据
    enhanced_prompt: str = ""              # LLM增强后的提示词
    detected_keywords: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    
    # 图片和LoRA相关参数
    base64_image_data: str = ""            # base64编码的图片数据
    lora_name: str = ""                    # LoRA名称
    
    def get_resolution_pair(self) -> Tuple[int, int]:
        """获取分辨率对"""
        return (self.width, self.height)
    
    def get_pixel_count(self) -> int:
        """获取像素总数"""
        return self.width * self.height
    
    def is_landscape(self) -> bool:
        """是否为横版"""
        return self.width > self.height
    
    def is_portrait(self) -> bool:
        """是否为竖版"""
        return self.height > self.width
    
    def is_square(self) -> bool:
        """是否为正方形"""
        return self.width == self.height


@dataclass
class LoRAConfig:
    """LoRA 模型配置"""
    name: str
    filename: str
    weight: float = 0.8
    category: LoRACategory = LoRACategory.DETAIL
    
    # 模型信息
    trigger_words: List[str] = field(default_factory=list)
    description: str = ""
    file_path: str = ""
    
    # Civitai 信息
    civitai_id: Optional[str] = None
    civitai_url: Optional[str] = None
    rating: float = 0.0
    downloads: int = 0
    
    # 状态信息
    is_local: bool = False
    is_active: bool = True
    last_used: float = field(default_factory=time.time)
    
    def matches_keywords(self, keywords: List[str]) -> bool:
        """检查是否匹配关键词"""
        if not keywords:
            return False
        
        # 检查触发词匹配
        keyword_lower = [kw.lower() for kw in keywords]
        trigger_lower = [tw.lower() for tw in self.trigger_words]
        
        return any(kw in tw or tw in kw for kw in keyword_lower for tw in trigger_lower)
    
    def get_match_score(self, prompt: str) -> float:
        """获取与提示词的匹配分数"""
        if not prompt:
            return 0.0
        
        prompt_lower = prompt.lower()
        score = 0.0
        
        # 基于触发词计算匹配分数
        for trigger_word in self.trigger_words:
            if trigger_word.lower() in prompt_lower:
                score += 1.0
        
        # 根据模型评分调整
        if self.rating > 0:
            score *= (self.rating / 5.0)  # 标准化到0-1
        
        return min(score, 5.0)  # 最大分数为5


@dataclass
class AnalysisResult:
    """参数分析结果"""
    success: bool
    parameters: FluxParameters = field(default_factory=FluxParameters)
    
    # LLM 分析信息
    llm_used: bool = False
    llm_response: str = ""
    analysis_time: float = 0.0
    
    # 检测信息
    detected_patterns: List[str] = field(default_factory=list)
    confidence: float = 0.0
    
    # 错误信息
    error_message: str = ""
    fallback_used: bool = False
    
    # 建议信息
    suggestions: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def add_suggestion(self, suggestion: str):
        """添加建议"""
        if suggestion not in self.suggestions:
            self.suggestions.append(suggestion)
    
    def add_warning(self, warning: str):
        """添加警告"""
        if warning not in self.warnings:
            self.warnings.append(warning)


@dataclass
class ExecutionResult:
    """工作流执行结果"""
    success: bool
    image_data: Optional[bytes] = None
    
    # 执行信息
    prompt_id: str = ""
    execution_time: float = 0.0
    
    # 使用的参数
    final_parameters: FluxParameters = field(default_factory=FluxParameters)
    used_loras: List[LoRAConfig] = field(default_factory=list)
    
    # 节点映射信息
    node_mappings: Dict[str, str] = field(default_factory=dict)
    workflow_file: str = ""
    
    # 错误信息
    error_message: str = ""
    error_code: str = ""
    
    # 统计信息
    queue_time: float = 0.0
    processing_time: float = 0.0
    total_time: float = 0.0
    
    # 种子信息
    final_seed: int = -1
    seed_source: str = ""  # "random", "last_used", "specific"
    
    def get_generation_info(self) -> Dict[str, Any]:
        """获取生成信息摘要"""
        return {
            'success': self.success,
            'execution_time': self.execution_time,
            'final_seed': self.final_seed,
            'seed_source': self.seed_source,
            'parameters': {
                'prompt': self.final_parameters.prompt,
                'width': self.final_parameters.width,
                'height': self.final_parameters.height,
                'steps': self.final_parameters.steps,
                'guidance': self.final_parameters.guidance
            },
            'loras_used': [lora.name for lora in self.used_loras],
            'workflow_file': self.workflow_file
        }


@dataclass
class SeedRecord:
    """种子记录"""
    seed: int
    timestamp: float = field(default_factory=time.time)
    prompt: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    success: bool = True
    
    def is_recent(self, max_age_hours: float = 24.0) -> bool:
        """检查是否为最近的记录"""
        age_hours = (time.time() - self.timestamp) / 3600
        return age_hours <= max_age_hours
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'seed': self.seed,
            'timestamp': self.timestamp,
            'prompt': self.prompt,
            'parameters': self.parameters,
            'success': self.success
        }


@dataclass
class StandardNodeConfig:
    """标准节点配置"""
    node_id: str
    class_type: str
    
    # 参数映射
    parameter_mappings: Dict[str, Optional[str]] = field(default_factory=dict)
    
    # 默认值
    default_values: Dict[str, Any] = field(default_factory=dict)
    
    # 输入连接
    input_connections: Dict[str, Tuple[str, int]] = field(default_factory=dict)
    
    # 验证规则
    value_constraints: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def validate_parameter(self, param_name: str, value: Any) -> bool:
        """验证参数值"""
        if param_name not in self.value_constraints:
            return True
        
        constraints = self.value_constraints[param_name]
        
        # 检查数值范围
        if 'min' in constraints and value < constraints['min']:
            return False
        if 'max' in constraints and value > constraints['max']:
            return False
        
        # 检查允许的值
        if 'allowed_values' in constraints and value not in constraints['allowed_values']:
            return False
        
        return True
    
    def get_input_value(self, param_name: str, user_value: Any) -> Any:
        """获取输入值（应用默认值和约束）"""
        if user_value is not None:
            if self.validate_parameter(param_name, user_value):
                return user_value
        
        # 使用默认值
        return self.default_values.get(param_name, user_value) 